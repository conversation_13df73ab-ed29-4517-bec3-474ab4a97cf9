<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --louna-primary: #9c89b8;
      --louna-secondary: #f0a6ca;
      --louna-accent: #b8bedd;
      --louna-dark: #1a1a2e;
      --louna-light: #edf2fb;
      --louna-gradient: linear-gradient(135deg, #9c89b8, #f0a6ca);
      --header-height: 60px;
      --sidebar-width: 250px;
    }
    
    body {
      font-family: 'Quicksand', sans-serif;
      background-color: var(--louna-light);
      color: var(--louna-dark);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: var(--louna-gradient);
      color: white;
      height: var(--header-height);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      display: flex;
      align-items: center;
      padding: 0 1rem;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .header h1 {
      font-size: 1.5rem;
      margin: 0;
      font-weight: 700;
    }
    
    .sidebar {
      background-color: white;
      width: var(--sidebar-width);
      position: fixed;
      top: var(--header-height);
      left: 0;
      bottom: 0;
      padding: 1rem 0;
      box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
      overflow-y: auto;
      z-index: 900;
    }
    
    .sidebar-menu {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .sidebar-menu li {
      padding: 0.5rem 1rem;
    }
    
    .sidebar-menu a {
      color: var(--louna-dark);
      text-decoration: none;
      display: flex;
      align-items: center;
      padding: 0.5rem;
      border-radius: 5px;
      transition: all 0.3s ease;
    }
    
    .sidebar-menu a:hover {
      background-color: rgba(156, 137, 184, 0.1);
    }
    
    .sidebar-menu a.active {
      background-color: var(--louna-primary);
      color: white;
    }
    
    .sidebar-menu i {
      margin-right: 0.5rem;
      font-size: 1.2rem;
    }
    
    .main-content {
      margin-left: var(--sidebar-width);
      margin-top: var(--header-height);
      padding: 2rem;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .home-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      padding: 2rem;
    }
    
    .welcome-section {
      text-align: center;
      margin-bottom: 3rem;
    }
    
    .welcome-section h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      background: var(--louna-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    
    .welcome-section p {
      font-size: 1.2rem;
      color: #666;
      max-width: 800px;
      margin: 0 auto;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }
    
    .feature-card {
      background-color: #f9f9f9;
      border-radius: 10px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .feature-icon {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--louna-primary);
    }
    
    .feature-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    .feature-description {
      color: #666;
      font-size: 0.9rem;
    }
    
    .stats-section {
      background-color: #f9f9f9;
      border-radius: 10px;
      padding: 1.5rem;
    }
    
    .stats-title {
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
      text-align: center;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1.5rem;
    }
    
    .stat-card {
      background-color: white;
      border-radius: 10px;
      padding: 1.5rem;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    
    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      color: var(--louna-primary);
      margin-bottom: 0.5rem;
    }
    
    .stat-label {
      color: #666;
      font-size: 0.9rem;
    }
    
    .footer {
      background-color: var(--louna-dark);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: auto;
      margin-left: var(--sidebar-width);
    }
    
    .system-status {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 0.5rem;
    }
    
    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    
    .status-indicator.active {
      background-color: #4CAF50;
    }
    
    .status-indicator.inactive {
      background-color: #F44336;
    }
    
    .status-text {
      font-size: 0.8rem;
      margin-right: 1rem;
    }
    
    .version {
      font-size: 0.8rem;
      opacity: 0.7;
    }
    
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .sidebar.show {
        transform: translateX(0);
      }
      
      .main-content, .footer {
        margin-left: 0;
      }
      
      .menu-toggle {
        display: block;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <button class="menu-toggle btn btn-link text-white me-3 d-md-none">
      <i class="bi bi-list"></i>
    </button>
    <h1><%= title %></h1>
  </header>
  
  <nav class="sidebar">
    <ul class="sidebar-menu">
      <li><a href="/louna/home" class="<%= page === 'home' ? 'active' : '' %>"><i class="bi bi-house"></i> Accueil</a></li>
      <li><a href="/louna/chat" class="<%= page === 'chat' ? 'active' : '' %>"><i class="bi bi-chat"></i> Chat</a></li>
      <li><a href="/louna/memory" class="<%= page === 'memory' ? 'active' : '' %>"><i class="bi bi-brain"></i> Mémoire</a></li>
      <li><a href="/louna/training" class="<%= page === 'training' ? 'active' : '' %>"><i class="bi bi-mortarboard"></i> Formation</a></li>
      <li><a href="/louna/code" class="<%= page === 'code' ? 'active' : '' %>"><i class="bi bi-code-square"></i> Code</a></li>
      <li><a href="/louna/security" class="<%= page === 'security' ? 'active' : '' %>"><i class="bi bi-shield-lock"></i> Sécurité</a></li>
      <li><a href="/louna/backup" class="<%= page === 'backup' ? 'active' : '' %>"><i class="bi bi-cloud-arrow-up"></i> Sauvegarde</a></li>
      <li><a href="/louna/monitor" class="<%= page === 'monitor' ? 'active' : '' %>"><i class="bi bi-activity"></i> Surveillance</a></li>
      <li><a href="/louna/accelerators" class="<%= page === 'accelerators' ? 'active' : '' %>"><i class="bi bi-lightning"></i> Accélérateurs</a></li>
      <li><a href="/louna/stats" class="<%= page === 'stats' ? 'active' : '' %>"><i class="bi bi-bar-chart"></i> Statistiques</a></li>
      <li><a href="/louna/settings" class="<%= page === 'settings' ? 'active' : '' %>"><i class="bi bi-gear"></i> Paramètres</a></li>
      <li><a href="/louna/models" class="<%= page === 'models' ? 'active' : '' %>"><i class="bi bi-boxes"></i> Modèles</a></li>
      <li><a href="/louna/documents" class="<%= page === 'documents' ? 'active' : '' %>"><i class="bi bi-file-earmark-text"></i> Documents</a></li>
      <li><a href="/louna/prompts" class="<%= page === 'prompts' ? 'active' : '' %>"><i class="bi bi-chat-square-text"></i> Prompts</a></li>
      <li><a href="/louna/mcp" class="<%= page === 'mcp' ? 'active' : '' %>"><i class="bi bi-cpu"></i> MCP</a></li>
      <li><a href="/louna/internet" class="<%= page === 'internet' ? 'active' : '' %>"><i class="bi bi-globe"></i> Internet</a></li>
      <li><a href="/louna/vpn" class="<%= page === 'vpn' ? 'active' : '' %>"><i class="bi bi-shield"></i> VPN</a></li>
      <li><a href="/louna/antivirus" class="<%= page === 'antivirus' ? 'active' : '' %>"><i class="bi bi-virus"></i> Antivirus</a></li>
      <li><a href="/louna/cognitive" class="<%= page === 'cognitive' ? 'active' : '' %>"><i class="bi bi-lightbulb"></i> Cognitive</a></li>
    </ul>
  </nav>
  
  <main class="main-content">
    <div class="home-container">
      <section class="welcome-section">
        <h2>Bienvenue sur Louna</h2>
        <p>Votre interface cognitive avancée pour interagir avec l'intelligence artificielle de manière naturelle et intuitive.</p>
      </section>
      
      <section class="features-grid">
        <div class="feature-card">
          <div class="feature-icon"><i class="bi bi-chat-dots"></i></div>
          <h3 class="feature-title">Chat Intelligent</h3>
          <p class="feature-description">Discutez avec Louna comme avec un humain. Elle comprend le contexte et apprend de vos interactions.</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon"><i class="bi bi-brain"></i></div>
          <h3 class="feature-title">Mémoire Thermique</h3>
          <p class="feature-description">Louna se souvient de vos conversations et utilise sa mémoire thermique pour des réponses contextuelles.</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon"><i class="bi bi-code-square"></i></div>
          <h3 class="feature-title">Assistance Code</h3>
          <p class="feature-description">Obtenez de l'aide pour vos projets de programmation avec des suggestions intelligentes.</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon"><i class="bi bi-lightning"></i></div>
          <h3 class="feature-title">Accélérateurs</h3>
          <p class="feature-description">Utilisez les accélérateurs pour des tâches spécifiques et augmentez votre productivité.</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon"><i class="bi bi-shield-lock"></i></div>
          <h3 class="feature-title">Sécurité Avancée</h3>
          <p class="feature-description">Vos données sont protégées avec des protocoles de sécurité de pointe.</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon"><i class="bi bi-globe"></i></div>
          <h3 class="feature-title">Accès Internet</h3>
          <p class="feature-description">Louna peut rechercher des informations sur internet pour vous fournir des réponses à jour.</p>
        </div>
      </section>
      
      <section class="stats-section">
        <h3 class="stats-title">Statistiques du Système</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">100%</div>
            <div class="stat-label">Disponibilité</div>
          </div>
          
          <div class="stat-card">
            <div class="stat-value">128</div>
            <div class="stat-label">Mémoires Stockées</div>
          </div>
          
          <div class="stat-card">
            <div class="stat-value">15</div>
            <div class="stat-label">Modèles Disponibles</div>
          </div>
          
          <div class="stat-card">
            <div class="stat-value">5 ms</div>
            <div class="stat-label">Temps de Réponse</div>
          </div>
        </div>
      </section>
    </div>
  </main>
  
  <footer class="footer">
    <p>Louna - Interface Cognitive Avancée &copy; 2025</p>
    <div class="system-status">
      <span class="status-indicator active"></span>
      <span class="status-text">Système actif</span>
      <span class="version">v1.0.0</span>
    </div>
  </footer>
  
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const menuToggle = document.querySelector('.menu-toggle');
      const sidebar = document.querySelector('.sidebar');
      
      // Gérer le menu mobile
      if (menuToggle) {
        menuToggle.addEventListener('click', function() {
          sidebar.classList.toggle('show');
        });
      }
    });
  </script>
</body>
</html>
