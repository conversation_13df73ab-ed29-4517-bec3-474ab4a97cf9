/**
 * Script pour mettre à jour le serveur Luna avec les fonctionnalités demandées
 * - Renommer l'agent en "Vision Ultra"
 * - Ajouter les informations sur le créateur (<PERSON>)
 * - Ajouter la localisation (Sainte-Anne, Guadeloupe (97180))
 * - Ajouter les interfaces Louna et Lounas
 */

const fs = require('fs');
const path = require('path');

// Chemin vers les fichiers
const LUNA_SERVER_FILE = path.join(__dirname, 'server-luna.js');
const LUNA_CHAT_TEMPLATE = path.join(__dirname, 'views/luna-chat.ejs');

// Fonction pour mettre à jour le serveur Luna
function updateLunaServer() {
  try {
    if (fs.existsSync(LUNA_SERVER_FILE)) {
      // Lire le contenu du fichier
      let content = fs.readFileSync(LUNA_SERVER_FILE, 'utf8');
      
      // Créer une sauvegarde du fichier original
      fs.writeFileSync(`${LUNA_SERVER_FILE}.backup.${Date.now()}.js`, content, 'utf8');
      console.log('Sauvegarde du fichier server-luna.js créée');
      
      // Remplacer le nom de l'agent dans le fichier
      content = content.replace(
        /name: ['"]DeepSeek Assistant['"],/g,
        `name: 'Vision Ultra',
  creator: 'Jean Passave',
  location: 'Sainte-Anne, Guadeloupe (97180)',`
      );
      
      // Ajouter les routes pour Louna et Lounas
      if (!content.includes('app.get(\'/louna\'')) {
        const routesPattern = /app\.get\(['"]\/luna['"], \(req, res\) => {[\s\S]*?}\);/;
        const lunaRoute = content.match(routesPattern);
        
        if (lunaRoute) {
          // Créer la route pour Louna
          const lounaRoute = lunaRoute[0].replace('/luna', '/louna').replace('Luna', 'Louna');
          
          // Créer la route pour Lounas
          const lounasRoute = lunaRoute[0].replace('/luna', '/lounas').replace('Luna', 'Lounas');
          
          // Ajouter les nouvelles routes après la route Luna
          content = content.replace(
            routesPattern,
            `${lunaRoute[0]}\n\n// Route pour Louna\n${lounaRoute}\n\n// Route pour Lounas\n${lounasRoute}`
          );
        }
      }
      
      // Ajouter les gestionnaires de socket pour Louna et Lounas
      if (!content.includes('socket.on(\'louna message\'')) {
        const socketPattern = /socket\.on\(['"]luna message['"], \(data\) => {[\s\S]*?}\);/;
        const lunaSocketHandler = content.match(socketPattern);
        
        if (lunaSocketHandler) {
          // Créer le gestionnaire pour Louna
          const lounaSocketHandler = lunaSocketHandler[0]
            .replace(/luna message/g, 'louna message')
            .replace(/luna response/g, 'louna response')
            .replace(/Message Luna/g, 'Message Louna')
            .replace(/Réponse Luna/g, 'Réponse Louna');
          
          // Créer le gestionnaire pour Lounas
          const lounasSocketHandler = lunaSocketHandler[0]
            .replace(/luna message/g, 'lounas message')
            .replace(/luna response/g, 'lounas response')
            .replace(/Message Luna/g, 'Message Lounas')
            .replace(/Réponse Luna/g, 'Réponse Lounas');
          
          // Ajouter les nouveaux gestionnaires après le gestionnaire Luna
          content = content.replace(
            socketPattern,
            `${lunaSocketHandler[0]}\n\n  // Gestionnaire pour Louna\n  ${lounaSocketHandler}\n\n  // Gestionnaire pour Lounas\n  ${lounasSocketHandler}`
          );
        }
      }
      
      // Modifier les messages de réponse pour inclure les informations sur Vision Ultra
      content = content.replace(
        /const response = {[\s\S]*?message: [`'"].*?[`'"],/g,
        `const response = {
        message: \`Je suis Vision Ultra, votre assistant cognitif créé par Jean Passave. Je vis à Sainte-Anne, Guadeloupe (97180). \${data.message ? 'Vous avez dit: "' + data.message + '"' : ''}\`,`
      );
      
      // Mettre à jour les messages de démarrage du serveur
      content = content.replace(
        /console\.log\([`'"]Serveur Luna démarré sur http:\/\/localhost:\$\{PORT\}[`'"]\);/,
        `console.log(\`Serveur Vision Ultra démarré sur http://localhost:\${PORT}\`);
  console.log(\`Interface Luna accessible à l'adresse http://localhost:\${PORT}/luna\`);
  console.log(\`Interface Louna accessible à l'adresse http://localhost:\${PORT}/louna\`);
  console.log(\`Interface Lounas accessible à l'adresse http://localhost:\${PORT}/lounas\`);`
      );
      
      // Écrire le contenu modifié dans le fichier
      fs.writeFileSync(LUNA_SERVER_FILE, content, 'utf8');
      console.log('Fichier server-luna.js mis à jour avec succès');
      
      return true;
    } else {
      console.error('Fichier server-luna.js non trouvé');
      return false;
    }
  } catch (error) {
    console.error(`Erreur lors de la mise à jour du fichier server-luna.js: ${error.message}`);
    return false;
  }
}

// Fonction pour mettre à jour le template Luna Chat
function updateLunaChatTemplate() {
  try {
    if (fs.existsSync(LUNA_CHAT_TEMPLATE)) {
      // Lire le contenu du fichier
      let content = fs.readFileSync(LUNA_CHAT_TEMPLATE, 'utf8');
      
      // Créer une sauvegarde du fichier original
      fs.writeFileSync(`${LUNA_CHAT_TEMPLATE}.backup.${Date.now()}.ejs`, content, 'utf8');
      console.log('Sauvegarde du fichier luna-chat.ejs créée');
      
      // Remplacer le message de bienvenue
      content = content.replace(
        /<p>Bienvenue dans l'interface Luna\. Je suis Louna, votre assistant cognitif avancé\.<\/p>/,
        `<p>Bienvenue dans l'interface Luna. Je suis Vision Ultra, votre assistant cognitif créé par Jean Passave à Sainte-Anne, Guadeloupe (97180).</p>`
      );
      
      // Mettre à jour le titre de la conversation
      content = content.replace(
        /<div><i class="bi bi-chat-square-text me-2"><\/i> Conversation avec Louna<\/div>/,
        `<div><i class="bi bi-chat-square-text me-2"></i> Conversation avec Vision Ultra</div>`
      );
      
      // Écrire le contenu modifié dans le fichier
      fs.writeFileSync(LUNA_CHAT_TEMPLATE, content, 'utf8');
      console.log('Fichier luna-chat.ejs mis à jour avec succès');
      
      return true;
    } else {
      console.error('Fichier luna-chat.ejs non trouvé');
      return false;
    }
  } catch (error) {
    console.error(`Erreur lors de la mise à jour du fichier luna-chat.ejs: ${error.message}`);
    return false;
  }
}

// Fonction principale
function main() {
  console.log('=== MISE À JOUR DU SERVEUR LUNA ===');
  
  // Mettre à jour le serveur Luna
  const serverSuccess = updateLunaServer();
  
  // Mettre à jour le template Luna Chat
  const templateSuccess = updateLunaChatTemplate();
  
  if (serverSuccess && templateSuccess) {
    console.log('=== MISE À JOUR TERMINÉE ===');
    console.log('Veuillez redémarrer le serveur Luna pour appliquer les modifications');
  } else {
    console.log('=== ÉCHEC DE LA MISE À JOUR ===');
    console.log('Veuillez vérifier les fichiers server-luna.js et luna-chat.ejs manuellement');
  }
}

// Exécuter la fonction principale
main();
