/**
 * Script pour corriger le problème de contexte de mémoire thermique
 * Ce script modifie le serveur pour s'assurer que les informations de la mémoire thermique
 * sont correctement incluses dans le contexte du modèle.
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le fichier server-luna.js
const SERVER_FILE = '/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui/server-luna.js';

// Fonction pour lire le contenu du fichier server-luna.js
function readServerFile() {
  try {
    return fs.readFileSync(SERVER_FILE, 'utf8');
  } catch (error) {
    console.error(`Erreur lors de la lecture du fichier server-luna.js: ${error.message}`);
    process.exit(1);
  }
}

// Fonction pour écrire le contenu modifié dans le fichier server-luna.js
function writeServerFile(content) {
  try {
    fs.writeFileSync(SERVER_FILE, content, 'utf8');
    console.log('Fichier server-luna.js modifié avec succès');
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'écriture du fichier server-luna.js: ${error.message}`);
    return false;
  }
}

// Fonction pour créer une sauvegarde du fichier server-luna.js
function backupServerFile() {
  try {
    const backupPath = '/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui/server-luna.backup.' + Date.now() + '.js';
    fs.copyFileSync(SERVER_FILE, backupPath);
    console.log(`Sauvegarde créée: ${backupPath}`);
    return true;
  } catch (error) {
    console.error(`Erreur lors de la création de la sauvegarde: ${error.message}`);
    return false;
  }
}

// Fonction pour corriger le problème de contexte de mémoire thermique
function fixMemoryContext() {
  console.log('=== CORRECTION DU PROBLÈME DE CONTEXTE DE MÉMOIRE THERMIQUE ===');

  // 1. Créer une sauvegarde du fichier server-luna.js
  console.log('\n1. Création d\'une sauvegarde du fichier server-luna.js...');
  if (!backupServerFile()) {
    console.error('Impossible de continuer sans sauvegarde');
    process.exit(1);
  }

  // 2. Lire le contenu du fichier server-luna.js
  console.log('\n2. Lecture du fichier server-luna.js...');
  const serverContent = readServerFile();

  // 3. Rechercher la fonction qui prépare le contexte pour le modèle
  console.log('\n3. Recherche de la fonction qui prépare le contexte pour le modèle...');

  // Rechercher la fonction qui gère les messages WebSocket
  const wsMessageHandlerRegex = /wss\.on\('connection', \(ws\) => \{[\s\S]*?\}\);/g;
  const wsMessageHandler = serverContent.match(wsMessageHandlerRegex);

  if (!wsMessageHandler) {
    console.error('Impossible de trouver le gestionnaire de messages WebSocket');
    process.exit(1);
  }

  // Rechercher la partie qui prépare le contexte pour le modèle
  const contextPreparationRegex = /(const contextMessages = \[[\s\S]*?\];)/g;
  const contextPreparation = wsMessageHandler[0].match(contextPreparationRegex);

  if (!contextPreparation) {
    console.error('Impossible de trouver la préparation du contexte');
    process.exit(1);
  }

  // 4. Modifier la préparation du contexte pour inclure les informations de la mémoire thermique
  console.log('\n4. Modification de la préparation du contexte...');

  // Nouvelle préparation du contexte
  const newContextPreparation = `
        // Préparer le contexte pour le modèle
        let contextMessages = [];

        // Ajouter les informations de la mémoire thermique au contexte
        if (thermalMemory) {
          try {
            // Récupérer les entrées de la mémoire thermique
            let memoryEntries = [];

            // Essayer différentes méthodes pour récupérer les entrées
            if (typeof thermalMemory.getAllEntries === 'function') {
              memoryEntries = await thermalMemory.getAllEntries();
            } else if (thermalMemory.memory && Array.isArray(thermalMemory.memory.memories)) {
              memoryEntries = thermalMemory.memory.memories;
            } else if (typeof thermalMemory.getAll === 'function') {
              memoryEntries = await thermalMemory.getAll();
            }

            if (memoryEntries && memoryEntries.length > 0) {
              console.log(\`Ajout de \${memoryEntries.length} entrées de mémoire thermique au contexte\`);

              // Filtrer les entrées pertinentes (par exemple, les informations sur l'utilisateur)
              const relevantEntries = memoryEntries.filter(entry =>
                entry.type === 'user_identity' ||
                entry.type === 'assistant_identity' ||
                entry.type === 'important_fact' ||
                entry.zone === 1 || // Zone instantanée
                entry.zone === 2 || // Zone court terme
                entry.zone === 6    // Zone long terme
              );

              if (relevantEntries.length > 0) {
                // Ajouter un message système pour introduire les informations de la mémoire
                contextMessages.push({
                  role: 'system',
                  content: \`INFORMATIONS IMPORTANTES DE LA MÉMOIRE THERMIQUE (à utiliser pour répondre aux questions de l'utilisateur):\n\${
                    relevantEntries.map(entry => {
                      // Formater l'entrée en fonction de son type
                      if (entry.messages && entry.messages.length > 0) {
                        return entry.messages.map(msg => msg.content).join('\\n');
                      } else if (entry.content) {
                        return entry.content;
                      } else {
                        return JSON.stringify(entry);
                      }
                    }).join('\\n\\n')
                  }\`
                });
              }
            }
          } catch (error) {
            console.error(\`Erreur lors de la récupération des entrées de la mémoire thermique: \${error.message}\`);
          }
        }

        // Ajouter les messages système standard
        contextMessages.push(
          { role: 'system', content: systemPrompt },
          { role: 'system', content: \`Date et heure actuelles: \${new Date().toLocaleString()}\` }
        );
  `;

  // Remplacer la préparation du contexte
  const modifiedServerContent = serverContent.replace(contextPreparationRegex, newContextPreparation);

  // 5. Écrire le contenu modifié dans le fichier server-luna.js
  console.log('\n5. Écriture du contenu modifié dans le fichier server-luna.js...');
  if (!writeServerFile(modifiedServerContent)) {
    console.error('Échec de l\'écriture du contenu modifié');
    process.exit(1);
  }

  console.log('\n=== CORRECTION DU PROBLÈME DE CONTEXTE DE MÉMOIRE THERMIQUE TERMINÉE ===');
  console.log('Redémarrez le serveur pour appliquer les modifications');
}

// Exécuter la correction
fixMemoryContext();
