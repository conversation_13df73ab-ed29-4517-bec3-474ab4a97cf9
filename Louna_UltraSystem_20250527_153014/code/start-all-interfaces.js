/**
 * <PERSON>ript pour démarrer toutes les interfaces
 */

const { spawn } = require('child_process');
const path = require('path');

// Fonction pour démarrer un serveur
function startServer(scriptName, name) {
  console.log(`Démarrage du serveur ${name}...`);
  
  const server = spawn('node', [scriptName], {
    stdio: 'pipe',
    detached: true
  });
  
  server.stdout.on('data', (data) => {
    console.log(`[${name}] ${data.toString().trim()}`);
  });
  
  server.stderr.on('data', (data) => {
    console.error(`[${name}] ERREUR: ${data.toString().trim()}`);
  });
  
  server.on('close', (code) => {
    console.log(`[${name}] Le serveur s'est arrêté avec le code ${code}`);
  });
  
  return server;
}

// Fonction principale
function main() {
  console.log('=== DÉMARRAGE DE TOUTES LES INTERFACES ===');
  
  // Démarrer le serveur Luna
  const lunaServer = startServer('server-luna-simple.js', 'Luna');
  
  // Attendre 2 secondes avant de démarrer le serveur Louna
  setTimeout(() => {
    // Démarrer le serveur Louna
    const lounaServer = startServer('server-louna.js', 'Louna');
    
    // Attendre 2 secondes avant de démarrer le serveur Lounas
    setTimeout(() => {
      // Démarrer le serveur Lounas
      const lounasServer = startServer('server-lounas.js', 'Lounas');
      
      // Afficher les URLs des interfaces
      setTimeout(() => {
        console.log('\n=== INTERFACES DISPONIBLES ===');
        console.log('Luna: http://localhost:3001/luna');
        console.log('Louna: http://localhost:3002/louna');
        console.log('Lounas: http://localhost:3003/lounas');
        console.log('\nAppuyez sur Ctrl+C pour arrêter tous les serveurs');
      }, 2000);
    }, 2000);
  }, 2000);
}

// Gérer l'arrêt du script
process.on('SIGINT', () => {
  console.log('\n=== ARRÊT DE TOUS LES SERVEURS ===');
  process.exit();
});

// Exécuter la fonction principale
main();
