<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Chat</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .chat-container {
      border: 1px solid #ccc;
      border-radius: 5px;
      padding: 10px;
      height: 400px;
      overflow-y: auto;
      margin-bottom: 10px;
    }
    
    .message {
      margin-bottom: 10px;
      padding: 10px;
      border-radius: 5px;
    }
    
    .user-message {
      background-color: #e6f7ff;
      text-align: right;
    }
    
    .agent-message {
      background-color: #f0f0f0;
    }
    
    .input-container {
      display: flex;
    }
    
    #message-input {
      flex: 1;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 5px;
      margin-right: 10px;
    }
    
    button {
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    
    button:hover {
      background-color: #45a049;
    }
    
    .status {
      margin-top: 10px;
      color: #666;
    }
    
    .interface-selector {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <h1>Test Chat</h1>
  
  <div class="interface-selector">
    <label for="interface-select">Sélectionner l'interface :</label>
    <select id="interface-select">
      <option value="luna">Luna (port 3001)</option>
      <option value="louna" selected>Louna (port 3002)</option>
      <option value="lounas">Lounas (port 3003)</option>
    </select>
    <button id="connect-btn">Connecter</button>
    <span id="connection-status">Non connecté</span>
  </div>
  
  <div class="chat-container" id="chat-container"></div>
  
  <div class="input-container">
    <input type="text" id="message-input" placeholder="Tapez votre message ici...">
    <button id="send-btn">Envoyer</button>
  </div>
  
  <div class="status" id="status"></div>
  
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <script>
    let socket;
    let selectedInterface = 'louna';
    
    const interfaceSelect = document.getElementById('interface-select');
    const connectBtn = document.getElementById('connect-btn');
    const connectionStatus = document.getElementById('connection-status');
    const chatContainer = document.getElementById('chat-container');
    const messageInput = document.getElementById('message-input');
    const sendBtn = document.getElementById('send-btn');
    const statusElement = document.getElementById('status');
    
    // Fonction pour se connecter au serveur
    function connectToServer() {
      // Déconnecter le socket existant s'il y en a un
      if (socket) {
        socket.disconnect();
      }
      
      // Obtenir l'interface sélectionnée
      selectedInterface = interfaceSelect.value;
      
      // Déterminer le port en fonction de l'interface
      const port = selectedInterface === 'luna' ? 3001 : selectedInterface === 'louna' ? 3002 : 3003;
      
      // Se connecter au serveur
      socket = io(`http://localhost:${port}`);
      
      // Mettre à jour le statut de connexion
      connectionStatus.textContent = 'Connexion en cours...';
      
      // Gérer la connexion
      socket.on('connect', () => {
        connectionStatus.textContent = 'Connecté';
        statusElement.textContent = `Connecté à l'interface ${selectedInterface} sur le port ${port}`;
        
        // Ajouter un message système
        addMessage(`Connecté à l'interface ${selectedInterface}`, 'system');
      });
      
      // Gérer la déconnexion
      socket.on('disconnect', () => {
        connectionStatus.textContent = 'Déconnecté';
        statusElement.textContent = `Déconnecté de l'interface ${selectedInterface}`;
        
        // Ajouter un message système
        addMessage(`Déconnecté de l'interface ${selectedInterface}`, 'system');
      });
      
      // Gérer les erreurs de connexion
      socket.on('connect_error', (error) => {
        connectionStatus.textContent = 'Erreur de connexion';
        statusElement.textContent = `Erreur de connexion: ${error.message}`;
        
        // Ajouter un message système
        addMessage(`Erreur de connexion: ${error.message}`, 'system');
      });
      
      // Gérer les réponses de l'agent
      socket.on(`${selectedInterface} response`, (data) => {
        // Ajouter la réponse au chat
        addMessage(data.message, 'agent');
        
        // Mettre à jour le statut
        statusElement.textContent = `Réponse reçue à ${new Date().toLocaleTimeString()}`;
      });
    }
    
    // Fonction pour envoyer un message
    function sendMessage() {
      const message = messageInput.value.trim();
      
      if (message && socket && socket.connected) {
        // Ajouter le message au chat
        addMessage(message, 'user');
        
        // Envoyer le message au serveur
        socket.emit(`${selectedInterface} message`, { message });
        
        // Mettre à jour le statut
        statusElement.textContent = `Message envoyé à ${new Date().toLocaleTimeString()}`;
        
        // Vider le champ de saisie
        messageInput.value = '';
      }
    }
    
    // Fonction pour ajouter un message au chat
    function addMessage(message, type) {
      const messageElement = document.createElement('div');
      messageElement.classList.add('message');
      
      if (type === 'user') {
        messageElement.classList.add('user-message');
        messageElement.textContent = `Vous: ${message}`;
      } else if (type === 'agent') {
        messageElement.classList.add('agent-message');
        messageElement.textContent = `${selectedInterface}: ${message}`;
      } else {
        messageElement.classList.add('system-message');
        messageElement.textContent = `Système: ${message}`;
      }
      
      chatContainer.appendChild(messageElement);
      
      // Faire défiler vers le bas
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    // Gérer le clic sur le bouton de connexion
    connectBtn.addEventListener('click', connectToServer);
    
    // Gérer le clic sur le bouton d'envoi
    sendBtn.addEventListener('click', sendMessage);
    
    // Gérer l'appui sur la touche Entrée dans le champ de saisie
    messageInput.addEventListener('keypress', (event) => {
      if (event.key === 'Enter') {
        sendMessage();
      }
    });
    
    // Se connecter automatiquement au chargement de la page
    connectToServer();
  </script>
</body>
</html>
