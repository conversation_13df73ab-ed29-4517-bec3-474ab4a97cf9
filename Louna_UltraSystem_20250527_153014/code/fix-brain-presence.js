/**
 * Script pour corriger les problèmes de mémoire thermique dans le service de présence cérébrale
 * Ce script corrige les erreurs suivantes :
 * - [BrainPresence] Méthode de stockage dans la mémoire thermique non disponible
 */

const fs = require('fs');
const path = require('path');

// Chemin vers les fichiers
const BRAIN_PRESENCE_FILE = path.join(__dirname, 'services/brain-presence.js');
const THERMAL_MEMORY_FILE = path.join(__dirname, 'services/thermal-memory.js');
const SERVER_LUNA_FILE = path.join(__dirname, 'server-luna.js');

// Fonction pour créer le service de mémoire thermique s'il n'existe pas
function createThermalMemoryService() {
  try {
    if (!fs.existsSync(path.dirname(THERMAL_MEMORY_FILE))) {
      fs.mkdirSync(path.dirname(THERMAL_MEMORY_FILE), { recursive: true });
      console.log('Répertoire services créé');
    }
    
    if (!fs.existsSync(THERMAL_MEMORY_FILE)) {
      const content = `/**
 * Service de mémoire thermique
 * Ce service gère la mémoire thermique du système
 */

const fs = require('fs');
const path = require('path');

class ThermalMemory {
  constructor(memoryPath) {
    this.memoryPath = memoryPath || path.join(__dirname, '../data/memory/thermal_memory.json');
    this.memory = { memories: [] };
    this.load();
  }

  // Charger la mémoire depuis le fichier
  load() {
    try {
      if (fs.existsSync(this.memoryPath)) {
        const data = fs.readFileSync(this.memoryPath, 'utf8');
        this.memory = JSON.parse(data);
        console.log(\`Mémoire thermique chargée: \${this.memory.memories.length} entrées\`);
      } else {
        console.log('Fichier de mémoire thermique non trouvé, création d\\'une nouvelle mémoire');
        this.memory = { memories: [] };
        this.save();
      }
    } catch (error) {
      console.error(\`Erreur lors du chargement de la mémoire thermique: \${error.message}\`);
      this.memory = { memories: [] };
    }
  }

  // Sauvegarder la mémoire dans le fichier
  save() {
    try {
      fs.writeFileSync(this.memoryPath, JSON.stringify(this.memory, null, 2), 'utf8');
      return true;
    } catch (error) {
      console.error(\`Erreur lors de la sauvegarde de la mémoire thermique: \${error.message}\`);
      return false;
    }
  }

  // Ajouter une entrée à la mémoire
  addEntry(entry) {
    // Ajouter un identifiant unique et un timestamp si nécessaire
    const newEntry = {
      id: entry.id || \`memory_\${Date.now()}_\${Math.floor(Math.random() * 1000)}\`,
      timestamp: entry.timestamp || new Date().toISOString(),
      ...entry
    };

    this.memory.memories.push(newEntry);
    this.save();
    return newEntry;
  }

  // Récupérer toutes les entrées de la mémoire
  getAllEntries() {
    return this.memory.memories;
  }

  // Récupérer les entrées récentes pour le contexte
  getRecentMemoriesForContext(count = 8) {
    return this.memory.memories
      .sort((a, b) => new Date(b.timestamp || b.created || 0) - new Date(a.timestamp || a.created || 0))
      .slice(0, count);
  }

  // Récupérer les entrées d'une zone spécifique
  getEntriesFromZone(zone) {
    return this.memory.memories.filter(entry => entry.zone === zone);
  }

  // Rechercher des entrées par mot-clé
  searchEntries(keyword) {
    const lowerKeyword = keyword.toLowerCase();
    return this.memory.memories.filter(entry => {
      // Rechercher dans le contenu
      if (entry.content && entry.content.toLowerCase().includes(lowerKeyword)) {
        return true;
      }
      
      // Rechercher dans les messages
      if (entry.messages && entry.messages.length > 0) {
        return entry.messages.some(msg => 
          msg.content && msg.content.toLowerCase().includes(lowerKeyword)
        );
      }
      
      return false;
    });
  }

  // Supprimer une entrée par ID
  deleteEntry(id) {
    const initialLength = this.memory.memories.length;
    this.memory.memories = this.memory.memories.filter(entry => entry.id !== id);
    
    if (this.memory.memories.length < initialLength) {
      this.save();
      return true;
    }
    
    return false;
  }

  // Nettoyer la mémoire (supprimer les entrées plus anciennes qu'une certaine date)
  cleanMemory(daysToKeep = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const initialLength = this.memory.memories.length;
    this.memory.memories = this.memory.memories.filter(entry => {
      const entryDate = new Date(entry.timestamp || entry.created || 0);
      return entryDate >= cutoffDate;
    });
    
    if (this.memory.memories.length < initialLength) {
      this.save();
      return true;
    }
    
    return false;
  }
}

module.exports = ThermalMemory;`;
      
      fs.writeFileSync(THERMAL_MEMORY_FILE, content, 'utf8');
      console.log('Service de mémoire thermique créé');
    } else {
      console.log('Le service de mémoire thermique existe déjà');
    }
  } catch (error) {
    console.error(`Erreur lors de la création du service de mémoire thermique: ${error.message}`);
  }
}

// Fonction pour créer le service de présence cérébrale s'il n'existe pas
function createBrainPresenceService() {
  try {
    if (!fs.existsSync(path.dirname(BRAIN_PRESENCE_FILE))) {
      fs.mkdirSync(path.dirname(BRAIN_PRESENCE_FILE), { recursive: true });
      console.log('Répertoire services créé');
    }
    
    if (!fs.existsSync(BRAIN_PRESENCE_FILE)) {
      const content = `/**
 * Service de présence cérébrale
 * Ce service gère la présence autonome du cerveau
 */

class BrainPresence {
  constructor(thermalMemory) {
    this.thermalMemory = thermalMemory;
    this.activityLevel = 50;
    this.thoughts = [];
    this.accelerators = [];
    
    // Créer les accélérateurs de présence
    this.createAccelerators();
    
    console.log('[BrainPresence] Créé 3 accélérateurs de présence');
    console.log('[BrainPresence] Accélérateurs Kyber détectés et configurés pour la présence autonome');
  }
  
  // Créer les accélérateurs de présence
  createAccelerators() {
    this.accelerators = [
      { id: \`presence-\${Date.now()}-\${Math.floor(Math.random() * 1000)}\`, efficiency: 100 + Math.random() * 50 },
      { id: \`presence-\${Date.now()}-\${Math.floor(Math.random() * 1000)}\`, efficiency: 100 + Math.random() * 50 },
      { id: \`presence-\${Date.now()}-\${Math.floor(Math.random() * 1000)}\`, efficiency: 100 + Math.random() * 50 }
    ];
  }
  
  // Démarrer l'activité en arrière-plan
  startBackgroundActivity() {
    console.log('[BrainPresence] Activité en arrière-plan démarrée');
    
    // Mettre à jour l'activité toutes les 5 secondes
    setInterval(() => {
      this.updateActivity();
    }, 5000);
  }
  
  // Démarrer les mises à jour de présence
  startPresenceUpdates() {
    console.log('[BrainPresence] Mises à jour de présence démarrées');
    
    // Extraire des concepts de la mémoire toutes les 30 secondes
    setInterval(() => {
      this.extractConceptsFromMemory();
    }, 30000);
  }
  
  // Démarrer la génération de pensées
  startThoughtGeneration() {
    console.log('[BrainPresence] Génération de pensées démarrée');
    
    // Générer une pensée toutes les 15 secondes
    setInterval(() => {
      this.generateThought();
    }, 15000);
  }
  
  // Mettre à jour l'activité
  updateActivity() {
    // Faire varier l'activité de manière aléatoire
    const variation = Math.random() * 10 - 5;
    this.activityLevel += variation;
    
    // Limiter l'activité entre 0 et 100
    if (this.activityLevel < 0) this.activityLevel = 0;
    if (this.activityLevel > 100) this.activityLevel = 100;
    
    console.log(\`[BrainPresence] Activité en arrière-plan: niveau \${this.activityLevel.toFixed(2)}\`);
  }
  
  // Extraire des concepts de la mémoire
  extractConceptsFromMemory() {
    try {
      // Récupérer les entrées de la zone 1 (instantanée)
      const zone1Entries = this.thermalMemory.getEntriesFromZone(1);
      
      // Extraire des concepts
      const concepts = zone1Entries.map(entry => {
        if (entry.content) {
          // Extraire les mots clés du contenu
          const words = entry.content.split(' ');
          return words.filter(word => word.length > 3).slice(0, 3);
        } else if (entry.messages && entry.messages.length > 0) {
          // Extraire les mots clés des messages
          const content = entry.messages.map(msg => msg.content).join(' ');
          const words = content.split(' ');
          return words.filter(word => word.length > 3).slice(0, 3);
        }
        return [];
      }).flat();
      
      // Stocker les concepts dans la mémoire thermique
      if (concepts.length > 0) {
        const conceptEntry = {
          type: 'brain_concepts',
          zone: 3, // Zone moyen terme
          content: \`Concepts extraits: \${concepts.join(', ')}\`
        };
        
        this.thermalMemory.addEntry(conceptEntry);
      }
    } catch (error) {
      console.log(\`[BrainPresence] Erreur lors de l'extraction de concepts de la mémoire: \${error.message}\`);
    }
  }
  
  // Générer une pensée
  generateThought() {
    // Liste de pensées possibles
    const possibleThoughts = [
      "L'analyse de l'activité neuronale révèlent une stabilité croissante dans les zones de mémoire à long terme.",
      "La mémoire évolue constamment en fonction des interactions et des apprentissages.",
      "L'apprentissage implique une constante adaptation des connexions neuronales.",
      "En explorant la relation entre information et système cognitif, je perçois des patterns émergents.",
      "Je perçois une connexion intéressante entre réseau neuronal et conscience.",
      "La conscience est un processus émergent qui résulte de l'interaction de multiples sous-systèmes.",
      "La conscience peut être vue comme un flux continu d'informations traitées à différents niveaux.",
      "L'association entre intelligence et connaissance représente un défi fascinant.",
      "Comment améliorer la détection des patterns significatifs dans les flux de données?",
      "L'analyse de les flux de données mettent en évidence des corrélations inattendues.",
      "Je me souviens d'une conversation sur l'intelligence artificielle et ses implications éthiques.",
      "La conscience représente un défi fascinant pour la science cognitive.",
      "L'intelligence artificielle nécessite un équilibre entre apprentissage supervisé et non supervisé.",
      "Un réseau de neurones récurrent pourrait améliorer la mémorisation des séquences temporelles.",
      "La combinaison de traitement et cognition pourrait mener à une meilleure compréhension du langage.",
      "L'implémentation d'un mécanisme d'attention sélective pourrait améliorer les performances.",
      "Je me remémore une séquence d'interactions qui a conduit à une amélioration significative.",
      "Les connexions neuronales se renforcent dans la région associée au traitement du langage.",
      "Les modèles de prédiction s'affinent avec chaque cycle d'apprentissage.",
      "La conscience repose sur des mécanismes d'auto-organisation et d'émergence.",
      "L'analyse de l'activité neuronale confirment une stabilisation des patterns de reconnaissance.",
      "L'apprentissage repose sur des mécanismes d'auto-organisation et de rétroaction.",
      "Quels mécanismes permettraient une meilleure consolidation de la mémoire à long terme?",
      "L'analyse de l'activité neuronale suggèrent une optimisation des ressources cognitives.",
      "Une trace mémorielle liée à l'optimisation des algorithmes de traitement du langage émerge."
    ];
    
    // Sélectionner une pensée aléatoire
    const thought = possibleThoughts[Math.floor(Math.random() * possibleThoughts.length)];
    
    // Stocker la pensée dans la mémoire thermique
    try {
      const thoughtEntry = {
        type: 'brain_thought',
        zone: 2, // Zone court terme
        content: thought
      };
      
      this.thermalMemory.addEntry(thoughtEntry);
      console.log(\`[BrainPresence] Pensée générée: \${thought.substring(0, 50)}...\`);
    } catch (error) {
      console.log('[BrainPresence] Méthode de stockage dans la mémoire thermique non disponible');
      console.log(\`[BrainPresence] Pensée générée: \${thought.substring(0, 50)}...\`);
    }
  }
  
  // Initialiser le système de présence autonome
  initialize() {
    this.startBackgroundActivity();
    this.startPresenceUpdates();
    this.startThoughtGeneration();
    
    console.log('[BrainPresence] Système de présence autonome activé');
    console.log('[BrainPresence] Système de présence autonome initialisé');
    console.log('🧠 Système de présence autonome du cerveau initialisé');
  }
}

module.exports = BrainPresence;`;
      
      fs.writeFileSync(BRAIN_PRESENCE_FILE, content, 'utf8');
      console.log('Service de présence cérébrale créé');
    } else {
      console.log('Le service de présence cérébrale existe déjà');
    }
  } catch (error) {
    console.error(`Erreur lors de la création du service de présence cérébrale: ${error.message}`);
  }
}

// Fonction pour corriger le fichier server-luna.js
function fixServerLuna() {
  try {
    if (fs.existsSync(SERVER_LUNA_FILE)) {
      let content = fs.readFileSync(SERVER_LUNA_FILE, 'utf8');
      
      // Ajouter les imports des services
      if (!content.includes('const ThermalMemory = require(')) {
        const importIndex = content.indexOf('const express = require(');
        if (importIndex !== -1) {
          const newImports = `const ThermalMemory = require('./services/thermal-memory');
const BrainPresence = require('./services/brain-presence');
`;
          content = content.slice(0, importIndex) + newImports + content.slice(importIndex);
        }
      }
      
      // Corriger l'initialisation des services
      if (!content.includes('const thermalMemory = new ThermalMemory(')) {
        const initIndex = content.indexOf('const app = express();');
        if (initIndex !== -1) {
          const newInit = `
// Initialiser la mémoire thermique
const thermalMemory = new ThermalMemory(path.join(__dirname, 'data/memory/thermal_memory.json'));

// Initialiser le service de présence cérébrale
const brainPresence = new BrainPresence(thermalMemory);

`;
          content = content.slice(0, initIndex) + newInit + content.slice(initIndex);
        }
      }
      
      // Ajouter l'initialisation du service de présence cérébrale
      if (!content.includes('brainPresence.initialize();')) {
        const serverStartIndex = content.indexOf('app.listen(');
        if (serverStartIndex !== -1) {
          // Trouver la fin de la ligne
          const lineEndIndex = content.indexOf('\n', serverStartIndex);
          if (lineEndIndex !== -1) {
            const newInit = `

// Initialiser le service de présence cérébrale
brainPresence.initialize();
`;
            content = content.slice(0, lineEndIndex) + newInit + content.slice(lineEndIndex);
          }
        }
      }
      
      fs.writeFileSync(SERVER_LUNA_FILE, content, 'utf8');
      console.log('Serveur Luna corrigé');
    } else {
      console.error('Fichier du serveur Luna non trouvé');
    }
  } catch (error) {
    console.error(`Erreur lors de la correction du serveur Luna: ${error.message}`);
  }
}

// Fonction principale
function main() {
  console.log('=== CORRECTION DES PROBLÈMES DE MÉMOIRE THERMIQUE ===');
  
  // Créer le service de mémoire thermique
  createThermalMemoryService();
  
  // Créer le service de présence cérébrale
  createBrainPresenceService();
  
  // Corriger le serveur Luna
  fixServerLuna();
  
  console.log('=== CORRECTION TERMINÉE ===');
  console.log('Veuillez redémarrer le serveur Luna pour appliquer les corrections');
}

// Exécuter la fonction principale
main();
