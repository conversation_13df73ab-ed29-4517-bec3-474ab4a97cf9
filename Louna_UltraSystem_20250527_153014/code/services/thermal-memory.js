/**
 * Service de mémoire thermique
 * Gère le stockage et la récupération des informations dans différentes zones de mémoire
 */

const fs = require('fs');
const path = require('path');

class ThermalMemory {
  constructor(filePath) {
    this.filePath = filePath;
    this.zones = {
      instant: [],      // Mémoire instantanée (quelques secondes)
      short_term: [],   // Mémoire à court terme (quelques minutes)
      working: [],      // Mémoire de travail (session actuelle)
      medium_term: [],  // Mémoire à moyen terme (quelques jours)
      long_term: [],    // Mémoire à long terme (permanente)
      dream: [],        // Mémoire de rêve (traitement en arrière-plan)
      kyber: [],        // Mémoire kyber (informations critiques)
      archive: []       // Archive (informations rarement utilisées)
    };
    
    // État émotionnel global
    this.emotionalState = {
      joy: 0.5,
      sadness: 0.1,
      fear: 0.1,
      anger: 0.1,
      surprise: 0.2,
      trust: 0.6
    };
    
    // Charger les données existantes
    this.loadMemory();
    
    // Configurer la migration périodique
    setInterval(() => this.migrateMemories(), 60000); // Toutes les minutes
  }
  
  // Charger la mémoire depuis le fichier
  loadMemory() {
    try {
      // Vérifier si le fichier existe
      if (fs.existsSync(this.filePath)) {
        const data = fs.readFileSync(this.filePath, 'utf8');
        const parsedData = JSON.parse(data);
        
        // Charger les zones de mémoire
        for (const zone in parsedData.zones) {
          if (this.zones[zone] !== undefined) {
            this.zones[zone] = parsedData.zones[zone];
          }
        }
        
        // Charger l'état émotionnel s'il existe
        if (parsedData.emotionalState) {
          this.emotionalState = parsedData.emotionalState;
        }
        
        console.log(`Mémoire thermique chargée depuis ${this.filePath}`);
      } else {
        console.log(`Fichier de mémoire thermique non trouvé. Création d'une nouvelle mémoire.`);
        this.saveMemory();
      }
    } catch (error) {
      console.error(`Erreur lors du chargement de la mémoire thermique: ${error.message}`);
      // Créer un nouveau fichier en cas d'erreur
      this.saveMemory();
    }
  }
  
  // Sauvegarder la mémoire dans le fichier
  saveMemory() {
    try {
      // Créer le répertoire parent s'il n'existe pas
      const dir = path.dirname(this.filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      // Préparer les données à sauvegarder
      const data = {
        zones: this.zones,
        emotionalState: this.emotionalState,
        lastSaved: new Date().toISOString()
      };
      
      // Écrire dans le fichier
      fs.writeFileSync(this.filePath, JSON.stringify(data, null, 2));
      console.log(`Mémoire thermique sauvegardée dans ${this.filePath}`);
    } catch (error) {
      console.error(`Erreur lors de la sauvegarde de la mémoire thermique: ${error.message}`);
    }
  }
  
  // Ajouter une entrée à la mémoire
  addEntry(entry) {
    // Générer un ID unique
    const id = `mem-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // Créer l'entrée complète
    const memoryEntry = {
      id,
      ...entry,
      created: new Date().toISOString(),
      lastAccessed: new Date().toISOString(),
      accessCount: 1,
      importance: Math.random() * 0.5 + 0.3, // Importance entre 0.3 et 0.8
      zone: 'instant',
      connections: []
    };
    
    // Ajouter à la zone instantanée
    this.zones.instant.push(memoryEntry);
    
    // Sauvegarder la mémoire
    this.saveMemory();
    
    return memoryEntry;
  }
  
  // Récupérer une entrée par ID
  getEntryById(id) {
    // Parcourir toutes les zones
    for (const zone in this.zones) {
      const entry = this.zones[zone].find(item => item.id === id);
      if (entry) {
        // Mettre à jour les statistiques d'accès
        entry.lastAccessed = new Date().toISOString();
        entry.accessCount += 1;
        
        // Augmenter légèrement l'importance à chaque accès
        entry.importance = Math.min(1, entry.importance + 0.05);
        
        // Sauvegarder les modifications
        this.saveMemory();
        
        return entry;
      }
    }
    
    return null;
  }
  
  // Rechercher des entrées par contenu
  searchEntries(query) {
    const results = [];
    
    // Parcourir toutes les zones
    for (const zone in this.zones) {
      const zoneResults = this.zones[zone].filter(item => 
        item.content && item.content.toLowerCase().includes(query.toLowerCase())
      );
      
      // Ajouter les résultats trouvés
      results.push(...zoneResults);
    }
    
    // Mettre à jour les statistiques d'accès pour les résultats
    results.forEach(entry => {
      entry.lastAccessed = new Date().toISOString();
      entry.accessCount += 1;
      
      // Augmenter légèrement l'importance à chaque accès
      entry.importance = Math.min(1, entry.importance + 0.02);
    });
    
    // Sauvegarder les modifications
    if (results.length > 0) {
      this.saveMemory();
    }
    
    return results;
  }
  
  // Migrer les mémoires entre les zones
  migrateMemories() {
    console.log('Migration des mémoires...');
    
    // Instant -> Short Term
    this.migrateZone('instant', 'short_term', entry => {
      const age = Date.now() - new Date(entry.created).getTime();
      return age > 30000 && entry.importance < 0.7; // 30 secondes
    });
    
    // Short Term -> Working
    this.migrateZone('short_term', 'working', entry => {
      const age = Date.now() - new Date(entry.created).getTime();
      return age > 300000 && entry.importance < 0.7; // 5 minutes
    });
    
    // Working -> Medium Term
    this.migrateZone('working', 'medium_term', entry => {
      const age = Date.now() - new Date(entry.created).getTime();
      return age > 3600000 && entry.importance < 0.7; // 1 heure
    });
    
    // Medium Term -> Long Term
    this.migrateZone('medium_term', 'long_term', entry => {
      const age = Date.now() - new Date(entry.created).getTime();
      return age > 86400000 && entry.importance > 0.5; // 1 jour
    });
    
    // Medium Term -> Archive
    this.migrateZone('medium_term', 'archive', entry => {
      const age = Date.now() - new Date(entry.created).getTime();
      return age > 86400000 && entry.importance <= 0.5; // 1 jour
    });
    
    // Sauvegarder les modifications
    this.saveMemory();
  }
  
  // Migrer les entrées d'une zone à une autre selon un critère
  migrateZone(sourceZone, targetZone, criteriaFn) {
    const toMigrate = [];
    
    // Identifier les entrées à migrer
    this.zones[sourceZone].forEach((entry, index) => {
      if (criteriaFn(entry)) {
        toMigrate.push({ entry, index });
      }
    });
    
    // Migrer les entrées (de la fin vers le début pour éviter les problèmes d'index)
    for (let i = toMigrate.length - 1; i >= 0; i--) {
      const { entry, index } = toMigrate[i];
      
      // Mettre à jour la zone
      entry.zone = targetZone;
      
      // Supprimer de la source
      this.zones[sourceZone].splice(index, 1);
      
      // Ajouter à la cible
      this.zones[targetZone].push(entry);
      
      console.log(`Mémoire ${entry.id} migrée de ${sourceZone} vers ${targetZone}`);
    }
  }
  
  // Obtenir des statistiques sur la mémoire
  getStats() {
    const stats = {
      totalMemories: 0,
      zoneStats: {}
    };
    
    // Calculer les statistiques pour chaque zone
    for (const zone in this.zones) {
      const count = this.zones[zone].length;
      stats.totalMemories += count;
      
      stats.zoneStats[zone] = {
        count,
        avgImportance: count > 0 
          ? this.zones[zone].reduce((sum, entry) => sum + entry.importance, 0) / count 
          : 0
      };
    }
    
    return stats;
  }
}

module.exports = ThermalMemory;
