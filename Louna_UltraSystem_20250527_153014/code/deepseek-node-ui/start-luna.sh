#!/bin/bash

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Afficher le logo
echo -e "${PURPLE}"
echo "  _                            "
echo " | |    _   _ _ __   __ _      "
echo " | |   | | | | '_ \ / _\` |     "
echo " | |___| |_| | | | | (_| |     "
echo " |_____|\\__,_|_| |_|\\__,_|   "
echo "                               "
echo -e "${NC}"
echo -e "${CYAN}Interface Cognitive Avancée avec Mémoire Thermique et MCP${NC}"
echo -e "${YELLOW}Développé par Jean-Luc PASSAVE${NC}"
echo ""

# Vérifier d'abord si un serveur est en cours d'exécution sur le port 3001
if lsof -i:3001 &> /dev/null; then
    echo -e "${YELLOW}Un serveur est déjà en cours d'exécution sur le port 3001.${NC}"
    echo -e "${YELLOW}Arrêt du serveur existant...${NC}"
    kill $(lsof -t -i:3001) 2> /dev/null || true
    sleep 2
fi

# Créer les dossiers nécessaires
echo -e "${BLUE}Création des dossiers nécessaires...${NC}"
mkdir -p data/memory
mkdir -p data/memory/instant
mkdir -p data/memory/short_term
mkdir -p data/memory/working
mkdir -p data/memory/medium_term
mkdir -p data/memory/long_term
mkdir -p data/memory/dream
mkdir -p data/memory/kyber

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
    echo -e "${YELLOW}Ollama n'est pas installé. L'application fonctionnera en mode simulé.${NC}"
    export OLLAMA_AVAILABLE=false
else
    # Vérifier si Ollama est en cours d'exécution
    echo -e "${BLUE}Vérification si Ollama est en cours d'exécution...${NC}"
    if curl -s http://localhost:11434/api/version &> /dev/null; then
        echo -e "${GREEN}Ollama est en cours d'exécution.${NC}"
        export OLLAMA_AVAILABLE=true
        
        # Vérifier si le modèle DeepSeek r1 est disponible
        echo -e "${BLUE}Vérification si le modèle DeepSeek r1 est disponible...${NC}"
        if ollama list | grep -q "deepseek-r1"; then
            echo -e "${GREEN}Le modèle DeepSeek r1 est disponible.${NC}"
        else
            echo -e "${YELLOW}Le modèle DeepSeek r1 n'est pas disponible. L'application fonctionnera en mode simulé.${NC}"
            export OLLAMA_AVAILABLE=false
        fi
    else
        echo -e "${YELLOW}Ollama n'est pas en cours d'exécution. Démarrage d'Ollama...${NC}"
        ollama serve &
        OLLAMA_PID=$!
        
        # Attendre que Ollama soit prêt
        echo -e "${YELLOW}Attente du démarrage d'Ollama...${NC}"
        for i in {1..10}; do
            if curl -s http://localhost:11434/api/version &> /dev/null; then
                echo -e "${GREEN}Ollama est maintenant en cours d'exécution.${NC}"
                export OLLAMA_AVAILABLE=true
                break
            fi
            echo -e "${YELLOW}Attente...${NC}"
            sleep 2
        done
        
        if ! curl -s http://localhost:11434/api/version &> /dev/null; then
            echo -e "${RED}Impossible de démarrer Ollama. L'application fonctionnera en mode simulé.${NC}"
            export OLLAMA_AVAILABLE=false
        fi
    fi
fi

# Vérification et initialisation de la mémoire
echo -e "${BLUE}Vérification et initialisation de la mémoire thermique...${NC}"

# Vérifier si le fichier de configuration de la mémoire existe
if [ ! -f "data/memory/memory_config.json" ]; then
    echo -e "${YELLOW}Création du fichier de configuration de la mémoire...${NC}"
    cat > data/memory/memory_config.json <<EOF
{
  "version": "1.0",
  "memoryZones": {
    "instant": { "maxItems": 50, "retentionHours": 1 },
    "short_term": { "maxItems": 200, "retentionHours": 24 },
    "working": { "maxItems": 100, "retentionHours": 72 },
    "medium_term": { "maxItems": 500, "retentionHours": 720 },
    "long_term": { "maxItems": 1000, "retentionHours": 8760 },
    "dream": { "maxItems": 20, "retentionHours": 8760 }
  },
  "thermalSettings": {
    "consolidationFrequency": 1,
    "dreamCycleFrequency": 24,
    "temperatureThresholds": {
      "hotZone": 80,
      "warmZone": 60,
      "coolZone": 40,
      "coldZone": 20,
      "archiveZone": 5
    }
  }
}
EOF
    echo -e "${GREEN}Configuration de la mémoire thermique créée.${NC}"
fi

# Vérification du système MCP
echo -e "${BLUE}Vérification du système MCP...${NC}"
if [ ! -d "mcp" ]; then
    echo -e "${YELLOW}Création du dossier MCP...${NC}"
    mkdir -p mcp
fi

# Démarrer le serveur Luna
echo -e "${BLUE}Démarrage de l'Interface Luna...${NC}"
echo -e "${GREEN}L'interface sera disponible à l'adresse http://localhost:3001/luna${NC}"
echo -e "${YELLOW}Appuyez sur Ctrl+C pour arrêter le serveur.${NC}"
echo ""

# Lancement du serveur avec vérification des erreurs
node server-luna.js || {
  echo -e "${RED}Erreur lors du démarrage du serveur Luna.${NC}"
  exit 1
}

# Si le script arrive ici, c'est que le serveur a été arrêté
echo -e "${YELLOW}Serveur Luna arrêté.${NC}"

# Arrêter Ollama si nous l'avons démarré
if [ -n "$OLLAMA_PID" ]; then
  echo -e "${YELLOW}Arrêt d'Ollama...${NC}"
  kill $OLLAMA_PID
  wait $OLLAMA_PID 2>/dev/null
  echo -e "${GREEN}Ollama arrêté.${NC}"
fi

exit 0
