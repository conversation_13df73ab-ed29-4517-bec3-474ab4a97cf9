/**
 * Module de transfert direct au cerveau pour l'agent Luna
 * 
 * Ce module permet de transférer directement des informations au cerveau de l'agent
 * en utilisant les accélérateurs Kyber pour maximiser l'efficacité du transfert.
 * 
 * Contrairement à la formation standard qui passe par l'API de chat,
 * ce transfert direct écrit les informations directement dans la mémoire thermique
 * et le réseau neuronal de l'agent.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// Importer le module de mémoire thermique
const ThermalMemory = require('./thermal-memory/thermal-memory');

class DirectBrainTransfer {
  constructor() {
    // Initialiser la mémoire thermique
    this.thermalMemory = new ThermalMemory();
    
    // Chemin vers les fichiers de mémoire
    this.desktopPath = path.join(os.homedir(), 'Desktop');
    this.memoryFolder = path.join(this.desktopPath, 'mistral mémoire thermique');
    this.neuralNetPath = path.join(this.memoryFolder, 'neural-connections.json');
    
    // Statistiques de transfert
    this.transferStats = {
      totalTransfers: 0,
      successfulTransfers: 0,
      failedTransfers: 0,
      startTime: null,
      endTime: null,
      transferredBytes: 0
    };
  }
  
  /**
   * Transfère directement une connaissance au cerveau de l'agent
   * @param {Object} knowledge - Objet contenant la connaissance à transférer
   * @param {string} knowledge.domain - Domaine de la connaissance (ex: "mathématiques", "physique")
   * @param {string} knowledge.concept - Concept spécifique
   * @param {string} knowledge.content - Contenu détaillé de la connaissance
   * @param {number} knowledge.importance - Importance de la connaissance (1-10)
   * @returns {boolean} - Succès du transfert
   */
  async transferKnowledge(knowledge) {
    try {
      console.log(`Transfert direct au cerveau: ${knowledge.concept} (${knowledge.domain})`);
      
      // Vérifier que la connaissance est valide
      if (!knowledge.domain || !knowledge.concept || !knowledge.content) {
        console.error('Connaissance invalide: domaine, concept et contenu sont requis');
        this.transferStats.failedTransfers++;
        return false;
      }
      
      // 1. Créer des connexions neuronales pour cette connaissance
      const success = await this.createNeuralConnections(knowledge);
      
      if (success) {
        // 2. Stocker la connaissance dans la mémoire thermique (zone 6 - archive)
        this.storeInThermalMemory(knowledge);
        
        // 3. Mettre à jour les statistiques
        this.transferStats.successfulTransfers++;
        this.transferStats.totalTransfers++;
        this.transferStats.transferredBytes += JSON.stringify(knowledge).length;
        
        console.log(`Transfert réussi: ${knowledge.concept}`);
        return true;
      } else {
        this.transferStats.failedTransfers++;
        this.transferStats.totalTransfers++;
        console.error(`Échec du transfert: ${knowledge.concept}`);
        return false;
      }
    } catch (error) {
      console.error('Erreur lors du transfert direct au cerveau:', error);
      this.transferStats.failedTransfers++;
      this.transferStats.totalTransfers++;
      return false;
    }
  }
  
  /**
   * Crée des connexions neuronales pour une connaissance
   * @param {Object} knowledge - La connaissance à connecter
   * @returns {boolean} - Succès de la création des connexions
   */
  async createNeuralConnections(knowledge) {
    try {
      // Charger l'état neuronal actuel
      let neuralState = {};
      if (fs.existsSync(this.neuralNetPath)) {
        neuralState = JSON.parse(fs.readFileSync(this.neuralNetPath, 'utf8'));
      } else {
        neuralState = {
          connections: {},
          patterns: {},
          insights: [],
          evolutionGeneration: 0,
          lastUpdate: new Date().toISOString()
        };
      }
      
      // Créer un identifiant unique pour cette connaissance
      const knowledgeId = `${knowledge.domain}-${knowledge.concept}-${Date.now()}`;
      
      // Extraire les mots-clés du contenu
      const keywords = this.extractKeywords(knowledge.content);
      
      // Créer des connexions pour chaque mot-clé
      keywords.forEach(keyword => {
        if (!neuralState.connections[keyword]) {
          neuralState.connections[keyword] = [];
        }
        
        // Ajouter cette connaissance à la connexion
        neuralState.connections[keyword].push({
          id: knowledgeId,
          domain: knowledge.domain,
          concept: knowledge.concept,
          importance: knowledge.importance || 5,
          timestamp: new Date().toISOString()
        });
      });
      
      // Créer un pattern pour cette connaissance
      if (!neuralState.patterns[knowledge.domain]) {
        neuralState.patterns[knowledge.domain] = [];
      }
      
      neuralState.patterns[knowledge.domain].push({
        id: knowledgeId,
        concept: knowledge.concept,
        keywords: keywords,
        timestamp: new Date().toISOString()
      });
      
      // Ajouter un insight si la connaissance est importante
      if (knowledge.importance >= 8) {
        neuralState.insights.push({
          id: knowledgeId,
          domain: knowledge.domain,
          concept: knowledge.concept,
          content: knowledge.content.substring(0, 200) + '...',
          timestamp: new Date().toISOString()
        });
      }
      
      // Incrémenter la génération d'évolution
      neuralState.evolutionGeneration++;
      neuralState.lastUpdate = new Date().toISOString();
      
      // Sauvegarder l'état neuronal
      fs.writeFileSync(this.neuralNetPath, JSON.stringify(neuralState, null, 2), 'utf8');
      
      console.log(`Connexions neuronales créées pour: ${knowledge.concept} (${keywords.length} mots-clés)`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la création des connexions neuronales:', error);
      return false;
    }
  }
  
  /**
   * Stocke une connaissance dans la mémoire thermique (zone 6 - archive)
   * @param {Object} knowledge - La connaissance à stocker
   */
  storeInThermalMemory(knowledge) {
    try {
      // Créer un objet de conversation pour la mémoire thermique
      const conversation = {
        id: `direct-transfer-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        title: `${knowledge.domain}: ${knowledge.concept}`,
        messages: [
          {
            role: 'system',
            content: 'Transfert direct de connaissance au cerveau'
          },
          {
            role: 'user',
            content: `Connaissance sur ${knowledge.concept} dans le domaine ${knowledge.domain}`
          },
          {
            role: 'assistant',
            content: knowledge.content
          }
        ],
        metadata: {
          source: 'direct-brain-transfer',
          domain: knowledge.domain,
          concept: knowledge.concept,
          importance: knowledge.importance || 5,
          transferDate: new Date().toISOString()
        }
      };
      
      // Ajouter directement à la zone 6 (archive)
      this.thermalMemory.addConversation(conversation);
      
      // Forcer le placement en zone 6
      const memoryIndex = this.thermalMemory.memory.memories.findIndex(mem => mem.id === conversation.id);
      if (memoryIndex >= 0) {
        this.thermalMemory.memory.memories[memoryIndex].zone = 6;
        this.thermalMemory.memory.memories[memoryIndex].temperature = 5;
        this.thermalMemory.saveMemories();
      }
      
      console.log(`Connaissance stockée dans la mémoire thermique (zone 6): ${knowledge.concept}`);
    } catch (error) {
      console.error('Erreur lors du stockage dans la mémoire thermique:', error);
    }
  }
  
  /**
   * Extrait les mots-clés d'un texte
   * @param {string} text - Le texte à analyser
   * @returns {string[]} - Liste des mots-clés
   */
  extractKeywords(text) {
    // Liste de mots vides à ignorer
    const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'que', 'qui', 'quoi', 'dont', 'où', 'comment', 'pourquoi', 'quand', 'est', 'sont', 'sera', 'pour', 'par', 'dans', 'sur', 'sous', 'avec', 'sans', 'de', 'du', 'au', 'aux', 'à'];
    
    // Nettoyer et tokenizer le texte
    const words = text.toLowerCase()
      .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3 && !stopWords.includes(word));
    
    // Compter les occurrences de chaque mot
    const wordCounts = {};
    words.forEach(word => {
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    });
    
    // Trier par nombre d'occurrences et prendre les 20 premiers
    const sortedWords = Object.keys(wordCounts).sort((a, b) => wordCounts[b] - wordCounts[a]);
    return sortedWords.slice(0, 20);
  }
  
  /**
   * Transfère un ensemble de connaissances au cerveau
   * @param {Array} knowledgeSet - Tableau d'objets de connaissance
   */
  async transferKnowledgeSet(knowledgeSet) {
    this.transferStats.startTime = new Date();
    console.log(`Démarrage du transfert direct de ${knowledgeSet.length} connaissances...`);
    
    for (const knowledge of knowledgeSet) {
      await this.transferKnowledge(knowledge);
      // Pause courte entre les transferts pour éviter la surcharge
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    this.transferStats.endTime = new Date();
    const duration = (this.transferStats.endTime - this.transferStats.startTime) / 1000;
    
    console.log('\nTransfert direct au cerveau terminé !');
    console.log(`Statistiques: ${this.transferStats.successfulTransfers}/${this.transferStats.totalTransfers} transferts réussis`);
    console.log(`Durée: ${duration.toFixed(2)} secondes`);
    console.log(`Données transférées: ${(this.transferStats.transferredBytes / 1024).toFixed(2)} KB`);
    console.log(`Vitesse: ${(this.transferStats.transferredBytes / 1024 / duration).toFixed(2)} KB/s`);
  }
}

module.exports = DirectBrainTransfer;
