const express = require('express');
const http = require('http');
const path = require('path');
const socketIo = require('socket.io');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Route principale - rediriger vers Luna
app.get('/', (req, res) => {
  res.redirect('/luna');
});

// Route pour charger l'interface Luna
app.get('/luna', (req, res) => {
  res.render('luna-chat', {
    title: 'Luna - Interface Cognitive Avancée'
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`\n\n=== SERVEUR LUNA MINIMAL DÉMARRÉ ===`);
  console.log(`Serveur Luna minimal démarré sur http://localhost:${PORT}`);
  console.log(`Accès à l'interface: http://localhost:${PORT}/luna`);
  console.log(`=== SERVEUR LUNA MINIMAL PRÊT ===\n\n`);
});
