/**
 * Serveur Luna - Interface cognitive avancée pour DeepSeek r1
 * Intégration avec la mémoire thermique et le système MCP
 */

// Gestion globale des erreurs non capturées
process.on('uncaughtException', (error) => {
  console.error('❌ ERREUR NON CAPTURÉE:', error);
  console.error('Stack trace:', error.stack);

  // Créer un fichier de log pour l'erreur
  const logDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const now = new Date();
  const dateStr = now.toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const errorLogPath = path.join(logDir, `error-${dateStr}.log`);

  fs.writeFileSync(errorLogPath, `Date: ${now.toISOString()}\nErreur: ${error.message}\nStack: ${error.stack}\n`);
  console.error(`Détails de l'erreur enregistrés dans: ${errorLogPath}`);

  // Terminer le processus avec un code d'erreur après un délai pour permettre l'écriture des logs
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// Gestion des rejets de promesses non gérés
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ PROMESSE REJETÉE NON GÉRÉE:', reason);
  // Ne pas terminer le processus, mais enregistrer l'erreur
  console.error('Stack trace:', reason.stack);
});

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const fs = require('fs');
const os = require('os');
const fileUpload = require('express-fileupload');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));
app.use(fileUpload({
  limits: { fileSize: 50 * 1024 * 1024 }, // Limite de 50 MB
}));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Créer les dossiers nécessaires s'ils n'existent pas
const MEMORY_DIR = path.join(__dirname, 'data/memory');
if (!fs.existsSync(MEMORY_DIR)) {
  fs.mkdirSync(MEMORY_DIR, { recursive: true });
}

const memoryFolders = ['instant', 'short_term', 'working', 'medium_term', 'long_term', 'dream', 'kyber'];
memoryFolders.forEach(folder => {
  const folderPath = path.join(MEMORY_DIR, folder);
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath, { recursive: true });
  }
});

// Charger ou initialiser le module de mémoire thermique
let thermalMemory;
let temperatureRegulation;
let slidingThermalZones;
try {
  const ThermalMemory = require('./thermal-memory/thermal-memory');
  thermalMemory = new ThermalMemory();
  console.log('Mémoire thermique initialisée');

  // Configurer le réseau d'accélérateurs Kyber s'il existe
  try {
    const KyberAccelerators = require('./thermal-memory/kyber-accelerators');
    const kyberAccelerators = new KyberAccelerators(thermalMemory);
    thermalMemory.kyberAccelerators = kyberAccelerators;
    console.log('Accélérateurs Kyber initialisés');
  } catch (error) {
    console.log('Accélérateurs Kyber non disponibles:', error.message);
  }

  // Initialiser le nouveau module de zones thermiques glissantes
  try {
    const SlidingThermalZones = require('./thermal-memory/sliding-thermal-zones');
    slidingThermalZones = new SlidingThermalZones(thermalMemory);
    console.log('🌡️ Système de zones thermiques glissantes activé');
    console.log('💡 Les 6 zones se déplacent comme un curseur en fonction de la température réelle');
  } catch (error) {
    console.log('Module de zones thermiques glissantes non disponible:', error.message);

    // Fallback vers le régulateur de température classique si le nouveau module n'est pas disponible
    try {
      const TemperatureRegulation = require('./thermal-memory/temperature-regulation');
      temperatureRegulation = new TemperatureRegulation(thermalMemory);
      console.log('🌡️ Fallback: Régulation de température réelle activée');
    } catch (fallbackError) {
      console.log('Module de régulation de température non disponible:', fallbackError.message);
    }
  }

  // S'assurer que la mémoire a les 6 niveaux correctement configurés
  const memoryLevels = [
    { name: 'instant', description: 'Mémoire instantanée', temperature: 100 },
    { name: 'short_term', description: 'Mémoire à court terme', temperature: 80 },
    { name: 'working', description: 'Mémoire de travail', temperature: 60 },
    { name: 'medium_term', description: 'Mémoire à moyen terme', temperature: 40 },
    { name: 'creative', description: 'Mémoire créative et rêves', temperature: 20 },
    { name: 'long_term', description: 'Mémoire à long terme', temperature: 5 }
  ];

  // Assurer que tous les niveaux existent
  if (thermalMemory.ensureLevelsExist) {
    thermalMemory.ensureLevelsExist(memoryLevels);
  }

  // Ajouter des méthodes spécifiques pour l'entrée/sortie si elles n'existent pas
  if (!thermalMemory.addInputMemory) {
    thermalMemory.addInputMemory = function(data) {
      data.direction = 'input';
      // Utiliser addConversation au lieu de addMemory qui n'existe pas
      if (this.addConversation) {
        this.addConversation({
          id: `input_${Date.now()}`,
          title: data.content.substring(0, 30),
          messages: [{role: 'user', content: data.content}],
          timestamp: new Date().toISOString()
        });
      }
      console.log('Entrée mémorisée:', data.content.substring(0, 50) + '...');
    };
  }

  if (!thermalMemory.addOutputMemory) {
    thermalMemory.addOutputMemory = function(data) {
      data.direction = 'output';
      // Utiliser addConversation au lieu de addMemory qui n'existe pas
      if (this.addConversation) {
        this.addConversation({
          id: `output_${Date.now()}`,
          title: data.content.substring(0, 30),
          messages: [{role: 'assistant', content: data.content}],
          timestamp: new Date().toISOString()
        });
      }
      console.log('Sortie mémorisée:', data.content.substring(0, 50) + '...');
    };
  }

  if (!thermalMemory.updateCreativeMemory) {
    thermalMemory.updateCreativeMemory = function(input, output) {
      // Utiliser addConversation au lieu de addMemory qui n'existe pas
      if (this.addConversation) {
        this.addConversation({
          id: `creative_${Date.now()}`,
          title: `Inspiration: ${input.substring(0, 20)}`,
          messages: [
            {role: 'user', content: input},
            {role: 'assistant', content: output}
          ],
          zone: 5, // Zone créative
          temperature: 20, // Température de la zone créative
          timestamp: new Date().toISOString()
        });
      }
      console.log('Mémoire créative mise à jour');
    };
  }

  if (!thermalMemory.getRecentMemoriesForContext) {
    thermalMemory.getRecentMemoriesForContext = function(count = 5) {
      if (this.getMemories) {
        const allMemories = this.getMemories();
        return allMemories
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
          .slice(0, count);
      }
      return [];
    };
  }

} catch (error) {
  console.log('Module de mémoire thermique non disponible:', error.message);
  // Créer une version simulée plus complète
  thermalMemory = {
    addMemory: (data) => console.log('Mémoire ajoutée (simulée):', data),
    getMemories: () => [],
    addConversation: (data) => console.log('Conversation ajoutée (simulée):', data),
    getConversations: () => [],
    addInputMemory: (data) => console.log('Entrée mémorisée (simulée):', data),
    addOutputMemory: (data) => console.log('Sortie mémorisée (simulée):', data),
    updateCreativeMemory: (input, output) => console.log('Mémoire créative mise à jour (simulée)'),
    getRecentMemoriesForContext: () => []
  };
}

// Configurer le système MCP
let mcpSystem;
try {
  const MCPServer = require('./mcp/mcp-server');
  mcpSystem = new MCPServer({
    port: 3002,
    allowInternet: true,
    allowDesktop: true,
    allowSystemCommands: true,
    debug: true
  });
  mcpSystem.start()
    .then(() => console.log('Système MCP initialisé et démarré'))
    .catch(error => console.log('Erreur lors du démarrage du système MCP:', error.message));
} catch (error) {
  console.log('Module MCP non disponible:', error.message);
  // Version simulée de MCP
  mcpSystem = {
    getSystemInfo: () => ({
      cpu: Math.floor(20 + Math.random() * 15),
      memory: Math.floor(40 + Math.random() * 20),
      disk: Math.floor(50 + Math.random() * 30)
    })
  };
}

// Charger les routes pour Luna
const lunaRoutes = require('./routes/luna');
const lunaModelsRouter = require('./routes/luna-models');
const lunaDocumentsRouter = require('./routes/luna-documents');
const lunaMemoryRouter = require('./routes/luna-memory');
const lunaPromptsRouter = require('./routes/luna-prompts');
const lunaMcpRouter = require('./routes/luna-mcp');
const reflectionAcceleratorsRouterInit = require('./routes/reflection-accelerators');

app.use('/models', lunaModelsRouter);
app.use('/documents', lunaDocumentsRouter);
app.use('/memory', lunaMemoryRouter);

// Initialiser la route de mémoire avec la référence à la mémoire thermique
lunaMemoryRouter.init(thermalMemory);

// Initialiser la route des accélérateurs de réflexion
const reflectionAcceleratorsRouter = reflectionAcceleratorsRouterInit({
  thermalMemory,
  kyberAccelerators: thermalMemory.kyberAccelerators
});

app.use('/luna', lunaRoutes);
app.use('/luna', lunaDocumentsRouter);
app.use('/luna', lunaPromptsRouter.router);
app.use('/luna', lunaMcpRouter.router);
app.use('/api/reflection', reflectionAcceleratorsRouter);

// Initialiser les gestionnaires de socket pour les prompts
lunaPromptsRouter.initSocketHandlers(io);

// Initialiser les gestionnaires de socket pour le MCP
lunaMcpRouter.initSocketHandlers(io, thermalMemory);

// Gestionnaires de socket pour les accélérateurs de réflexion
io.on('connection', (socket) => {
  // Obtenir les données des accélérateurs de réflexion
  socket.on('get reflection accelerators', async () => {
    try {
      // Si les accélérateurs Kyber ne sont pas disponibles, renvoyer des données simulées
      if (!thermalMemory.kyberAccelerators) {
        socket.emit('reflection accelerators data', {
          success: true,
          accelerators: simulateAccelerators(),
          stats: simulateStats()
        });
        return;
      }

      // Obtenir les accélérateurs de réflexion réels
      const accelerators = thermalMemory.kyberAccelerators.accelerators.reflection || [];

      // Calculer les statistiques
      const stats = {
        efficiency: thermalMemory.kyberAccelerators.getAcceleratorsTotalEfficiency('reflection'),
        throughput: calculateThroughput(accelerators),
        temperature: calculateTemperature(accelerators),
        load: calculateLoad(accelerators)
      };

      socket.emit('reflection accelerators data', {
        success: true,
        accelerators: accelerators,
        stats: stats
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des accélérateurs de réflexion:', error);
      socket.emit('reflection accelerators error', {
        success: false,
        error: 'Erreur lors de la récupération des accélérateurs de réflexion'
      });
    }
  });

  // Optimiser les accélérateurs de réflexion
  socket.on('optimize reflection accelerators', async () => {
    try {
      // Si les accélérateurs Kyber ne sont pas disponibles, simuler l'optimisation
      if (!thermalMemory.kyberAccelerators) {
        socket.emit('reflection accelerators optimized', {
          success: true,
          accelerators: simulateAccelerators(true),
          stats: simulateStats(true),
          message: 'Accélérateurs de réflexion optimisés avec succès (simulation)'
        });
        return;
      }

      // Optimiser les accélérateurs de réflexion réels
      thermalMemory.kyberAccelerators.accelerators.reflection.forEach(acc => {
        // Augmenter l'efficacité de 1-5%
        const improvement = Math.random() * 0.04 + 0.01;
        acc.efficiency = Math.min(0.99, acc.efficiency + improvement);

        // Réduire la température
        acc.temperature = Math.max(0, acc.temperature - 0.2);

        // Mettre à jour la dernière activité
        acc.lastActivity = new Date().toISOString();
      });

      // Recalculer les statistiques
      const stats = {
        efficiency: thermalMemory.kyberAccelerators.getAcceleratorsTotalEfficiency('reflection'),
        throughput: calculateThroughput(thermalMemory.kyberAccelerators.accelerators.reflection),
        temperature: calculateTemperature(thermalMemory.kyberAccelerators.accelerators.reflection),
        load: calculateLoad(thermalMemory.kyberAccelerators.accelerators.reflection)
      };

      socket.emit('reflection accelerators optimized', {
        success: true,
        accelerators: thermalMemory.kyberAccelerators.accelerators.reflection,
        stats: stats,
        message: 'Accélérateurs de réflexion optimisés avec succès'
      });
    } catch (error) {
      console.error('Erreur lors de l\'optimisation des accélérateurs de réflexion:', error);
      socket.emit('reflection accelerators error', {
        success: false,
        error: 'Erreur lors de l\'optimisation des accélérateurs de réflexion'
      });
    }
  });
});

// Endpoints pour la gestion des modèles
app.get('/luna/models', async (req, res) => {
  try {
    // Essayer de récupérer les modèles depuis l'API Ollama
    try {
      const response = await axios.get(`${OLLAMA_API_URL}/api/tags`, {
        timeout: 5000, // 5 secondes de timeout
        validateStatus: function (status) {
          return status >= 200 && status < 500; // Accepter tous les codes 2xx, 3xx et 4xx
        }
      });

      if (response.data && response.data.models) {
        const models = response.data.models.map(model => model.name);

        // Mettre à jour la liste globale des modèles
        global.availableModels = models;

        // Vérifier si le modèle actif est dans la liste
        if (!models.includes(global.selectedOllamaModel)) {
          global.selectedOllamaModel = models[0] || 'deepseek-r1:7b';
        }

        res.json({
          success: true,
          models,
          activeModel: global.selectedOllamaModel,
          connectionStatus: {
            connected: true,
            version: global.ollamaConnectionStatus.version || 'inconnue'
          }
        });
        return;
      }
    } catch (apiError) {
      console.error('Erreur lors de la récupération des modèles depuis l\'API:', apiError.message);
    }

    // Si on arrive ici, c'est que l'API a échoué ou que la réponse était invalide
    // Fournir des modèles par défaut
    const defaultModels = ["deepseek-r1:7b", "llama2:7b", "mistral:7b"];

    // Si nous avons déjà des modèles en mémoire, les utiliser
    const models = global.availableModels.length > 0 ? global.availableModels : defaultModels;

    // S'assurer que le modèle actif est dans la liste
    if (!models.includes(global.selectedOllamaModel)) {
      global.selectedOllamaModel = models[0];
    }

    res.json({
      success: true,
      models,
      activeModel: global.selectedOllamaModel,
      warning: 'Utilisation des modèles en cache ou par défaut. La connexion à Ollama pourrait être instable.',
      connectionStatus: global.ollamaConnectionStatus
    });

  } catch (error) {
    console.error('Erreur critique lors de la récupération des modèles:', error.message);

    // En cas d'erreur critique, fournir des modèles par défaut
    const defaultModels = ["deepseek-r1:7b", "llama2:7b", "mistral:7b"];

    res.json({
      success: false,
      error: 'Impossible de récupérer les modèles. Utilisation des modèles par défaut.',
      models: defaultModels,
      activeModel: global.selectedOllamaModel || defaultModels[0],
      errorDetails: error.message
    });
  }
});

// Route principale - rediriger vers Luna
app.get('/', (req, res) => {
  res.redirect('/luna');
});

// Route pour charger l'interface Luna
app.get('/luna', (req, res) => {
  res.render('luna-chat', {
    title: 'Luna - Interface Cognitive Avancée'
  });
});

// Route pour la page de réflexion
app.get('/luna/reflection', (req, res) => {
  res.render('luna-reflection', {
    title: 'Luna - Réflexion'
  });
});

// Route pour la page de mémoire
app.get('/luna/memory', (req, res) => {
  res.render('luna-memory', {
    title: 'Luna - Mémoire Thermique'
  });
});

// Route pour la page d'accélérateurs
app.get('/luna/accelerators', (req, res) => {
  res.render('luna-accelerators-new', {
    title: 'Luna - Accélérateurs Kyber',
    page: 'accelerators',
    systemStatus: {
      active: true,
      version: '1.0.0'
    }
  });
});

// Route pour la page du cerveau
app.get('/luna/brain', (req, res) => {
  res.render('luna-brain', {
    title: 'Luna - Cerveau'
  });
});

// Route pour la page du système cognitif
app.get('/luna/cognitive', (req, res) => {
  res.render('luna-cognitive', {
    title: 'Luna - Système Cognitif'
  });
});

// Route pour la page des paramètres
app.get('/luna/settings', (req, res) => {
  res.render('luna-settings', {
    title: 'Luna - Paramètres'
  });
});

// Route pour la page MCP
app.get('/luna/mcp', (req, res) => {
  res.render('luna-mcp', {
    title: 'Luna - MCP'
  });
});

// Route pour la page Code
app.get('/luna/code', (req, res) => {
  res.render('luna-code', {
    title: 'Luna - Éditeur de Code'
  });
});

// Route pour la page de température et mémoire thermique
app.get('/luna/thermal', (req, res) => {
  res.render('luna-base', {
    page: 'luna-thermal',
    title: 'Luna - Système de Mémoire Thermique'
  });
});

// API pour obtenir les données de température réelle
app.get('/luna/api/temperature', (req, res) => {
  if (slidingThermalZones) {
    // Utiliser le nouveau module de zones thermiques glissantes
    res.json({
      success: true,
      data: slidingThermalZones.getThermalZonesState(),
      moduleType: 'sliding-zones',
      timestamp: new Date().toISOString()
    });
  } else if (temperatureRegulation) {
    // Fallback vers l'ancien module
    res.json({
      success: true,
      data: temperatureRegulation.getTemperatureData(),
      moduleType: 'temperature-regulation',
      timestamp: new Date().toISOString()
    });
  } else {
    res.json({
      success: false,
      error: 'Module de régulation de température non disponible'
    });
  }
});

// API pour déclencher manuellement un cycle de refroidissement
app.post('/luna/api/cooling/trigger', (req, res) => {
  if (slidingThermalZones) {
    // Pour le nouveau module, c'est équivalent à activer brièvement le mode sommeil
    slidingThermalZones.setSleepMode(true);

    // Désactiver le mode sommeil après 30 secondes
    setTimeout(() => {
      slidingThermalZones.setSleepMode(false);
    }, 30000);

    res.json({
      success: true,
      message: 'Cycle de refroidissement déclenché via mode sommeil temporaire',
      sleepMode: true
    });
  } else if (temperatureRegulation && temperatureRegulation.triggerManualCooling) {
    const result = temperatureRegulation.triggerManualCooling();
    res.json({
      success: true,
      message: 'Cycle de refroidissement déclenché',
      result
    });
  } else {
    res.json({
      success: false,
      error: 'Module de régulation de température non disponible'
    });
  }
});

// API pour activer/désactiver la régulation de température
app.post('/luna/api/temperature/regulation', (req, res) => {
  const { enabled } = req.body;

  if (slidingThermalZones) {
    // Pour le nouveau module, on active/désactive le mode sommeil
    const status = slidingThermalZones.setSleepMode(enabled === true);
    res.json({
      success: true,
      enabled: status,
      mode: 'sleep'
    });
  } else if (temperatureRegulation && temperatureRegulation.setRegulationEnabled) {
    const status = temperatureRegulation.setRegulationEnabled(enabled === true);
    res.json({
      success: true,
      enabled: status,
      mode: 'regulation'
    });
  } else {
    res.json({
      success: false,
      error: 'Module de régulation de température non disponible'
    });
  }
});

// API spécifique pour le mode sommeil des zones thermiques glissantes
app.post('/luna/api/thermal/sleep', (req, res) => {
  const { enabled } = req.body;

  if (slidingThermalZones) {
    const status = slidingThermalZones.setSleepMode(enabled === true);
    res.json({
      success: true,
      sleepMode: status,
      message: status ? 'Mode sommeil activé' : 'Mode sommeil désactivé'
    });
  } else {
    res.json({
      success: false,
      error: 'Module de zones thermiques glissantes non disponible'
    });
  }
});

// URL de l'API Ollama
const OLLAMA_API_URL = 'http://localhost:11434';

// Variable globale pour stocker le modèle sélectionné
global.selectedOllamaModel = "deepseek-r1:7b"; // Utiliser deepseek-r1 comme modèle par défaut

// Liste des modèles disponibles (sera mise à jour dynamiquement)
global.availableModels = [];

// Variables globales pour la gestion de la connexion à Ollama
global.ollamaConnectionStatus = {
  connected: false,
  lastCheck: null,
  reconnecting: false,
  version: null,
  retryCount: 0,
  maxRetries: 5,
  retryInterval: 2000 // 2 secondes entre les tentatives
};

// Vérifier la disponibilité d'Ollama et récupérer les modèles disponibles
async function checkOllama(forceCheck = false) {
  // Éviter de vérifier trop fréquemment si déjà connecté (sauf si forceCheck est true)
  const now = Date.now();
  if (!forceCheck &&
      global.ollamaConnectionStatus.connected &&
      global.ollamaConnectionStatus.lastCheck &&
      (now - global.ollamaConnectionStatus.lastCheck) < 5000) {
    // Si on a vérifié il y a moins de 5 secondes et qu'on était connecté, on renvoie simplement le statut
    return global.ollamaConnectionStatus.connected;
  }

  // Mettre à jour le timestamp de la dernière vérification
  global.ollamaConnectionStatus.lastCheck = now;

  try {
    console.log(`Tentative de connexion à Ollama sur ${OLLAMA_API_URL}...`);

    // Vérifier si Ollama répond
    const versionResponse = await axios.get(`${OLLAMA_API_URL}/api/version`, {
      timeout: 5000,
      retry: 2,
      retryDelay: 1000
    });

    if (versionResponse.data && versionResponse.data.version) {
      console.log('Version Ollama détectée:', versionResponse.data.version);
      global.ollamaConnectionStatus.version = versionResponse.data.version;
      global.ollamaConnectionStatus.retryCount = 0; // Réinitialiser le compteur de tentatives

      // Récupérer la liste des modèles disponibles
      try {
        console.log('Récupération des modèles disponibles...');
        const modelResponse = await axios.get(`${OLLAMA_API_URL}/api/tags`, {
          timeout: 8000,
          retry: 1,
          retryDelay: 1000
        });

        if (modelResponse.data && modelResponse.data.models) {
          global.availableModels = modelResponse.data.models.map(model => model.name);
          console.log(`Modèles disponibles (${global.availableModels.length}): ${global.availableModels.join(', ')}`);

          // Vérifier si le modèle par défaut est disponible
          const defaultModel = "deepseek-r1:7b";
          if (global.availableModels.includes(defaultModel)) {
            console.log(`Modèle par défaut ${defaultModel} disponible.`);
            global.selectedOllamaModel = defaultModel;
          } else if (global.availableModels.includes("deepseek-r1")) {
            // Essayer avec le nom sans version
            console.log(`Utilisation du modèle deepseek-r1 sans version spécifiée.`);
            global.selectedOllamaModel = "deepseek-r1";
          } else if (global.availableModels.length > 0) {
            // Si le modèle par défaut n'est pas disponible, utiliser le premier modèle de la liste
            console.log(`Modèle DeepSeek r1 non disponible. Utilisation de ${global.availableModels[0]} à la place.`);
            global.selectedOllamaModel = global.availableModels[0];
          } else {
            console.warn('Aucun modèle disponible!');
            global.ollamaConnectionStatus.connected = false;
            return false;
          }

          // Si tout s'est bien passé, Ollama est disponible
          console.log('Ollama est prêt et opérationnel!');
          global.ollamaConnectionStatus.connected = true;
          global.ollamaConnectionStatus.reconnecting = false;
          io.emit('ollama status', {
            connected: true,
            model: global.selectedOllamaModel,
            version: global.ollamaConnectionStatus.version
          });
          return true;
        } else {
          console.error('Format de réponse inattendu de l\'API Ollama pour les modèles.');
          global.ollamaConnectionStatus.connected = false;
          return false;
        }
      } catch (modelError) {
        console.error('Erreur lors de la récupération des modèles:', modelError.message);
        global.ollamaConnectionStatus.connected = false;
        return false;
      }
    } else {
      console.error('Format de réponse inattendu de l\'API Ollama pour la version.');
      global.ollamaConnectionStatus.connected = false;
      return false;
    }
  } catch (error) {
    console.error('Erreur lors de la vérification d\'Ollama:', error.message);
    global.ollamaConnectionStatus.connected = false;

    // Si on n'est pas déjà en train de tenter une reconnexion et qu'on n'a pas dépassé le nombre max de tentatives
    if (!global.ollamaConnectionStatus.reconnecting &&
        global.ollamaConnectionStatus.retryCount < global.ollamaConnectionStatus.maxRetries) {
      global.ollamaConnectionStatus.reconnecting = true;
      global.ollamaConnectionStatus.retryCount++;
      console.log(`Tentative de reconnexion ${global.ollamaConnectionStatus.retryCount}/${global.ollamaConnectionStatus.maxRetries} dans ${global.ollamaConnectionStatus.retryInterval/1000}s...`);

      // Programmer une nouvelle tentative dans l'intervalle configuré
      setTimeout(async () => {
        global.ollamaConnectionStatus.reconnecting = false;
        const result = await checkOllama(true);
        if (result) {
          console.log('Reconnexion à Ollama réussie!');
        } else if (global.ollamaConnectionStatus.retryCount >= global.ollamaConnectionStatus.maxRetries) {
          console.error('Échec de la reconnexion à Ollama après épuisement des tentatives.');
          io.emit('ollama status', {
            connected: false,
            error: 'Échec de la connexion à Ollama après plusieurs tentatives.'
          });
        }
      }, global.ollamaConnectionStatus.retryInterval);
    }

    return false;
  }
}

// Configurer les WebSockets pour la communication en temps réel
io.on('connection', (socket) => {
  console.log('Client connecté:', socket.id);

  // Envoyer le statut d'Ollama au client dès la connexion
  socket.on('check ollama', async () => {
    try {
      const isAvailable = await checkOllama();
      socket.emit('ollama status', {
        connected: isAvailable,
        model: global.selectedOllamaModel || 'deepseek-r1:7b'
      });
    } catch (error) {
      socket.emit('ollama status', { connected: false, error: error.message });
    }
  });

  // Événement pour les mises à jour de mémoire thermique
  socket.on('request_memory_update', (data) => {
    console.log('Demande de mise à jour de mémoire thermique reçue');

    // Diffuser la mise à jour à tous les clients
    io.emit('memory_update', data);
  });

  // Envoyer des mises à jour périodiques de la mémoire thermique
  const memoryUpdateInterval = setInterval(() => {
    // Générer des données aléatoires pour la simulation
    const memoryData = {
      zones: Array(6).fill().map((_, i) => ({
        activity: Math.random() * 100 / (i + 1)
      })),
      hotPoints: Math.floor(Math.random() * 5) + 1,
      coldPoints: Math.floor(Math.random() * 3) + 1,
      transfers: Math.floor(Math.random() * 10) + 5,
      avgTemp: Math.random() * 30 + 50,
      memoryUsage: {
        current: Math.floor(Math.random() * 100) + 20,
        max: 1000
      },
      acceleratorEfficiency: Math.random() * 100 + 100
    };

    // Émettre l'événement de mise à jour
    io.emit('memory_update', memoryData);
  }, 10000); // Toutes les 10 secondes

  // Nettoyer l'intervalle lors de la déconnexion
  socket.on('disconnect', () => {
    clearInterval(memoryUpdateInterval);
  });

  // Gérer les messages de l'utilisateur pour Luna
  socket.on('luna message', async ({ message }) => {
    try {
      // Pas de log pour réduire le bruit
      // Vérifier si Ollama est disponible
      const isOllamaAvailable = await checkOllama();

      if (isOllamaAvailable) {
        try {
          // Enregistrer l'entrée dans la mémoire thermique (comme si ça entrait par les "oreilles")
          if (thermalMemory) {
            try {
              if (typeof thermalMemory.addInputMemory === 'function') {
                thermalMemory.addInputMemory({
                  type: 'user_input',
                  channel: 'hearing',  // Entrée par les "oreilles"
                  content: message,
                  timestamp: new Date().toISOString(),
                  source: 'text_input'
                });
                // Pas de log pour réduire le bruit
              } else {
                // Pas de log pour réduire le bruit
              }
            } catch (memoryError) {
              // Log d'erreur uniquement en cas de problème réel
              console.error('Erreur mémoire:', memoryError.message);
            }
          }

          // Préparer le contexte de la conversation
          const context = {
            system: `Vous êtes Louna, une assistante IA très avancée équipée d'une mémoire thermique à six niveaux et du Model Context Protocol (MCP). Votre mémoire est structurée avec les niveaux suivants: mémoire instantanée (niveau 1), mémoire à court terme (niveau 2), mémoire de travail (niveau 3), mémoire à moyen terme (niveau 4), zone créative et rêves (niveau 5), et mémoire à long terme (niveau 6). Vous recevez les messages par vos "oreilles" (les entrées utilisateur) et répondez par votre "bouche" (vos réponses générées). Date actuelle: ${new Date().toLocaleDateString('fr-FR')}.`,
            messages: [
              { role: "user", content: message }
            ]
          };

          // Récupérer le modèle sélectionné depuis les paramètres ou utiliser DeepSeek r1 par défaut
          const selectedModel = global.selectedOllamaModel || "deepseek-r1:7b";
          console.log(`Utilisation du modèle: ${selectedModel}`);

          // Préparer le contexte de mémoire pour l'agent
          let memoryContext = '';
          if (thermalMemory && thermalMemory.getRecentMemoriesForContext) {
            const recentMemories = await thermalMemory.getRecentMemoriesForContext(5);
            if (recentMemories && recentMemories.length > 0) {
              memoryContext = '\n\nVoici des éléments de ma mémoire qui pourraient être pertinents:\n' +
                recentMemories.map(m => `- ${m.content}`).join('\n');
            }
          }

          // Contexte enrichi avec la mémoire
          const enhancedContext = context.system + memoryContext;
          console.log('📒 Contexte mémoire enrichi ajouté');

          console.log(`Envoi de la requête au modèle ${selectedModel} via l'API Ollama`);
          console.log('URL de l\'API Ollama:', `${OLLAMA_API_URL}/api/chat`);

          let response;
          let apiError = null;

          // Vérifier d'abord si Ollama est disponible
          try {
            const healthCheck = await axios.get(`${OLLAMA_API_URL}`, {
              timeout: 5000 // 5 secondes de timeout pour le health check
            });
            console.log(`Ollama est disponible, version: ${healthCheck.data?.version || 'inconnue'}`);
          } catch (healthError) {
            console.error('Erreur lors de la vérification de la disponibilité d\'Ollama:', healthError.message);
            throw new Error('Le serveur Ollama n\'est pas accessible. Veuillez vérifier qu\'il est bien démarré.');
          }

          // Essayer d'abord avec l'endpoint /api/chat (nouvelle API)
          try {
            console.log('Tentative avec l\'API moderne (/api/chat)...');
            response = await axios.post(`${OLLAMA_API_URL}/api/chat`, {
              model: selectedModel,
              messages: [
                { role: "system", content: enhancedContext },
                ...context.messages
              ],
              stream: false,
              options: {
                temperature: 0.7,
                num_predict: -1 // Pas de limite de longueur pour les réponses
              }
            }, {
              timeout: 60000, // 60 secondes de timeout
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              validateStatus: function (status) {
                return status >= 200 && status < 500; // Accepter tous les codes 2xx, 3xx et 4xx
              }
            });

            // Vérifier si la réponse est valide
            if (response.status !== 200 || !response.data || !response.data.message) {
              throw new Error(`Réponse invalide de l'API: ${response.status}`);
            }

          } catch (error) {
            apiError = error;
            console.log(`Erreur avec /api/chat: ${error.message}`);

            // Essayer avec l'endpoint /chat (ancienne API)
            try {
              console.log('Tentative avec l\'API legacy (/chat)...');
              response = await axios.post(`${OLLAMA_API_URL}/chat`, {
                model: selectedModel,
                messages: [
                  { role: "system", content: enhancedContext },
                  ...context.messages
                ],
                stream: false,
                options: {
                  temperature: 0.7,
                  num_predict: -1 // Pas de limite de longueur pour les réponses
                }
              }, {
                timeout: 60000, // 60 secondes de timeout
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json'
                },
                validateStatus: function (status) {
                  return status >= 200 && status < 500; // Accepter tous les codes 2xx, 3xx et 4xx
                }
              });

              // Vérifier si la réponse est valide
              if (response.status !== 200 || !response.data || !response.data.message) {
                throw new Error(`Réponse invalide de l'API: ${response.status}`);
              }

            } catch (legacyError) {
              console.error('Erreur avec les deux endpoints d\'API:', legacyError.message);

              // Essayer une dernière tentative avec l'API de complétion
              try {
                console.log('Tentative avec l\'API de complétion (/api/generate)...');
                const completionResponse = await axios.post(`${OLLAMA_API_URL}/api/generate`, {
                  model: selectedModel,
                  prompt: `${enhancedContext}\n\nUtilisateur: ${message}\n\nAssistant:`,
                  stream: false,
                  options: {
                    temperature: 0.7,
                    num_predict: -1 // Pas de limite de longueur pour les réponses
                  }
                }, {
                  timeout: 60000 // 60 secondes de timeout
                });

                // Convertir la réponse de l'API de complétion au format de l'API de chat
                response = {
                  status: 200,
                  data: {
                    message: {
                      content: completionResponse.data.response || "Je suis désolé, je n'ai pas pu générer une réponse."
                    }
                  }
                };

              } catch (completionError) {
                console.error('Toutes les tentatives d\'API ont échoué:', completionError.message);
                throw new Error('Impossible de communiquer avec le modèle après plusieurs tentatives. Veuillez vérifier la configuration d\'Ollama.');
              }
            }
          }

          console.log('Réponse reçue d\'Ollama:', response.status);

          // Enregistrer dans la mémoire thermique
          if (thermalMemory) {
            try {
              // Enregistrer la sortie (réponse de l'assistant)
              if (typeof thermalMemory.addOutputMemory === 'function') {
                thermalMemory.addOutputMemory({
                  type: 'assistant_output',
                  channel: 'speech',  // Sortie par la "bouche"
                  content: response.data.message.content,
                  timestamp: new Date().toISOString(),
                  source: 'text_generation'
                });
                // Pas de log pour réduire le bruit
              } else {
                // Pas de log pour réduire le bruit
              }

              // Conserver également la conversation complète
              if (typeof thermalMemory.addConversation === 'function') {
                thermalMemory.addConversation({
                  id: `conv_${Date.now()}`,
                  title: message.substring(0, 30),
                  messages: [
                    {role: 'user', content: message},
                    {role: 'assistant', content: response.data.message.content}
                  ],
                  timestamp: new Date().toISOString()
                });
                // Pas de log pour réduire le bruit
              }

              // Alimenter la zone créative/rêve (niveau 5) périodiquement
              if (Math.random() < 0.2 && typeof thermalMemory.updateCreativeMemory === 'function') {
                thermalMemory.updateCreativeMemory(message, response.data.message.content);
                // Pas de log pour réduire le bruit
              }
            } catch (memoryError) {
              console.error('Erreur lors de l\'enregistrement dans la mémoire thermique:', memoryError);
            }
          }

          // Envoyer la réponse au client
          setTimeout(() => {
            socket.emit('luna response', {
              message: response.data.message.content
            });
          }, 500); // Réduire le délai pour accélérer la réponse

        } catch (error) {
          console.error('Erreur lors de l\'appel à l\'API Ollama:', error);
          socket.emit('luna response', {
            message: `Erreur de connexion avec le modèle : ${error.message}. Veuillez vérifier qu'Ollama est bien lancé.`
          });
        }
      } else {
        socket.emit('luna response', {
          message: `Le modèle n'est pas disponible. Veuillez vérifier qu'Ollama est bien lancé.`
        });
      }
    } catch (error) {
      console.error('Erreur lors du traitement du message:', error);
      socket.emit('luna response', {
        message: `Je suis désolée, une erreur s'est produite lors du traitement de votre message: ${error.message}`
      });
    }
  });

  // Gérer la déconnexion
  socket.on('disconnect', () => {
    console.log('Client déconnecté:', socket.id);
  });
});

// La fonction simulateResponse a été supprimée car nous utilisons maintenant directement le modèle Claude

// Démarrer le serveur avec gestion d'erreurs
server.listen(PORT, () => {
  console.log(`Serveur Luna démarré sur http://localhost:${PORT}`);
  console.log('Interface Luna activée et prête à l\'emploi');
}).on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Erreur: Le port ${PORT} est déjà utilisé par une autre application.`);
    console.error('Veuillez arrêter cette application ou utiliser un port différent.');
  } else {
    console.error(`❌ Erreur lors du démarrage du serveur:`, error.message);
  }
  process.exit(1);
});

// Gérer l'arrêt propre du serveur
process.on('SIGINT', () => {
  console.log('Arrêt du serveur Luna...');
  if (mcpSystem && mcpSystem.stop) {
    mcpSystem.stop().then(() => {
      console.log('Système MCP arrêté');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});

// Fonctions utilitaires pour les accélérateurs de réflexion

/**
 * Calcule le débit total des accélérateurs
 * @param {Array} accelerators - Liste des accélérateurs
 * @returns {number} Débit total en MB/s
 */
function calculateThroughput(accelerators) {
  if (!accelerators || accelerators.length === 0) {
    return 0;
  }

  return accelerators
    .filter(acc => acc.active)
    .reduce((sum, acc) => sum + (acc.throughput || 0), 0);
}

/**
 * Calcule la température moyenne des accélérateurs
 * @param {Array} accelerators - Liste des accélérateurs
 * @returns {number} Température moyenne
 */
function calculateTemperature(accelerators) {
  if (!accelerators || accelerators.length === 0) {
    return 0;
  }

  const activeAccelerators = accelerators.filter(acc => acc.active);
  if (activeAccelerators.length === 0) {
    return 0;
  }

  return activeAccelerators.reduce((sum, acc) => sum + (acc.temperature || 0), 0) / activeAccelerators.length;
}

/**
 * Calcule la charge moyenne des accélérateurs
 * @param {Array} accelerators - Liste des accélérateurs
 * @returns {number} Charge moyenne
 */
function calculateLoad(accelerators) {
  if (!accelerators || accelerators.length === 0) {
    return 0;
  }

  const activeAccelerators = accelerators.filter(acc => acc.active);
  if (activeAccelerators.length === 0) {
    return 0;
  }

  return activeAccelerators.reduce((sum, acc) => sum + (acc.load || 0), 0) / activeAccelerators.length;
}

/**
 * Simule des accélérateurs de réflexion
 * @param {boolean} optimized - Si true, simule des accélérateurs optimisés
 * @returns {Array} Liste d'accélérateurs simulés
 */
function simulateAccelerators(optimized = false) {
  const count = 3;
  const accelerators = [];

  for (let i = 0; i < count; i++) {
    // Base efficiency between 0.7 and 0.9
    let efficiency = Math.random() * 0.2 + 0.7;

    // If optimized, increase efficiency by 1-5%
    if (optimized) {
      efficiency = Math.min(0.99, efficiency + (Math.random() * 0.04 + 0.01));
    }

    accelerators.push({
      id: `sim-reflection-${i}`,
      type: 'reflection',
      efficiency: efficiency,
      throughput: Math.random() * 1000 + 1000,
      temperature: Math.random() * 0.5,
      load: Math.random() * 0.5 + 0.3,
      active: true,
      created: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    });
  }

  return accelerators;
}

/**
 * Simule des statistiques d'accélérateurs
 * @param {boolean} optimized - Si true, simule des statistiques optimisées
 * @returns {Object} Statistiques simulées
 */
function simulateStats(optimized = false) {
  // Base efficiency between 1.5 and 1.7
  let efficiency = Math.random() * 0.2 + 1.5;

  // If optimized, increase efficiency by 5-10%
  if (optimized) {
    efficiency = Math.min(2.0, efficiency * (1 + (Math.random() * 0.05 + 0.05)));
  }

  return {
    efficiency: efficiency,
    throughput: Math.random() * 3000 + 3000,
    temperature: Math.random() * 0.5,
    load: Math.random() * 0.5 + 0.3
  };
}

// Exporter le module
module.exports = app;
