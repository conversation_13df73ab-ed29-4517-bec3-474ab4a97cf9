/**
 * Routes pour le Master Control Program (MCP) de Luna
 */

const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');

// Chemin vers le fichier de configuration MCP
const MCP_CONFIG_FILE = path.join(__dirname, '../data/mcp-config.json');

// S'assurer que le dossier data existe
const DATA_DIR = path.join(__dirname, '../data');
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// État global du MCP
let mcpState = {
  active: false,
  internetAccess: true,
  securityLevel: 'medium',
  lastUpdate: new Date().toISOString()
};

// Charger la configuration MCP
function loadMcpConfig() {
  try {
    if (fs.existsSync(MCP_CONFIG_FILE)) {
      mcpState = JSON.parse(fs.readFileSync(MCP_CONFIG_FILE, 'utf8'));
      console.log('Configuration MCP chargée');
    } else {
      // Créer un fichier de configuration par défaut
      saveMcpConfig();
    }
  } catch (error) {
    console.error('Erreur lors du chargement de la configuration MCP:', error);
  }
}

// Sauvegarder la configuration MCP
function saveMcpConfig() {
  try {
    mcpState.lastUpdate = new Date().toISOString();
    fs.writeFileSync(MCP_CONFIG_FILE, JSON.stringify(mcpState, null, 2), 'utf8');
    console.log('Configuration MCP sauvegardée');
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de la configuration MCP:', error);
  }
}

// Route pour la page MCP
router.get('/mcp', (req, res) => {
  res.render('luna-mcp', {
    title: 'Luna - Master Control Program'
  });
});

// Initialiser les gestionnaires de socket pour le MCP
function initSocketHandlers(io, thermalMemory) {
  io.on('connection', (socket) => {
    console.log('Client connecté au MCP:', socket.id);
    
    // Obtenir le statut MCP
    socket.on('get mcp status', () => {
      socket.emit('mcp status', {
        success: true,
        active: mcpState.active,
        internetAccess: mcpState.internetAccess,
        securityLevel: mcpState.securityLevel
      });
    });
    
    // Définir le statut MCP
    socket.on('set mcp status', (data) => {
      // Mettre à jour uniquement les propriétés fournies
      if (data.active !== undefined) mcpState.active = data.active;
      if (data.internetAccess !== undefined) mcpState.internetAccess = data.internetAccess;
      if (data.securityLevel !== undefined) mcpState.securityLevel = data.securityLevel;
      
      // Sauvegarder la configuration
      saveMcpConfig();
      
      // Informer tous les clients connectés
      io.emit('mcp status', {
        success: true,
        active: mcpState.active,
        internetAccess: mcpState.internetAccess,
        securityLevel: mcpState.securityLevel
      });
      
      console.log('Statut MCP mis à jour:', mcpState);
    });
    
    // Obtenir les métriques système
    socket.on('get system metrics', () => {
      // Simuler des métriques système
      const metrics = {
        success: true,
        cpuUsage: Math.floor(20 + Math.random() * 30),
        memoryUsage: Math.floor(2000 + Math.random() * 2000),
        networkUsage: Math.floor(5 + Math.random() * 20),
        temperature: Math.floor(50 + Math.random() * 20)
      };
      
      socket.emit('system metrics', metrics);
    });
    
    // Obtenir le statut des accélérateurs
    socket.on('get accelerators status', () => {
      // Utiliser les accélérateurs réels si disponibles
      let accelerators = {
        memory: { count: 3, efficiency: 0.6 },
        thermal: { count: 3, efficiency: 0.7 },
        reflection: { count: 3, efficiency: 0.9 }
      };
      
      // Si la mémoire thermique est disponible, utiliser ses accélérateurs
      if (thermalMemory && thermalMemory.accelerators) {
        try {
          const kyberStats = thermalMemory.getAcceleratorsStats();
          
          accelerators = {
            memory: {
              count: kyberStats.memory.count,
              efficiency: kyberStats.memory.efficiency
            },
            thermal: {
              count: kyberStats.thermal.count,
              efficiency: kyberStats.thermal.efficiency
            },
            reflection: {
              count: kyberStats.reflection ? kyberStats.reflection.count : 3,
              efficiency: kyberStats.reflection ? kyberStats.reflection.efficiency : 0.9
            }
          };
        } catch (error) {
          console.error('Erreur lors de la récupération des accélérateurs:', error);
        }
      }
      
      socket.emit('accelerators status', {
        success: true,
        accelerators
      });
    });
    
    // Exécuter une commande MCP
    socket.on('mcp command', (data) => {
      const command = data.command;
      let response = '';
      
      // Traiter les commandes
      switch (command) {
        case 'status':
          response = `Statut du système:\n`;
          response += `- MCP: ${mcpState.active ? 'Actif' : 'Inactif'}\n`;
          response += `- Accès Internet: ${mcpState.internetAccess ? 'Activé' : 'Désactivé'}\n`;
          response += `- Niveau de sécurité: ${mcpState.securityLevel.toUpperCase()}\n`;
          response += `- Dernière mise à jour: ${new Date(mcpState.lastUpdate).toLocaleString('fr-FR')}`;
          break;
          
        case 'accelerators':
          // Utiliser les accélérateurs réels si disponibles
          if (thermalMemory && thermalMemory.accelerators) {
            try {
              const report = thermalMemory.generateAcceleratorsReport(true);
              response = report || 'Rapport des accélérateurs non disponible';
            } catch (error) {
              response = `Erreur lors de la génération du rapport: ${error.message}`;
            }
          } else {
            response = 'Rapport des accélérateurs:\n';
            response += '- Mémoire: 3 accélérateurs, 60% d\'efficacité\n';
            response += '- Thermique: 3 accélérateurs, 70% d\'efficacité\n';
            response += '- Réflexion: 3 accélérateurs, 90% d\'efficacité\n';
            response += '- Zones: 6 accélérateurs, efficacité variable';
          }
          break;
          
        case 'memory':
          // Utiliser la mémoire thermique réelle si disponible
          if (thermalMemory) {
            try {
              const stats = thermalMemory.getMemoryStats();
              response = `Statut de la mémoire thermique:\n`;
              response += `- Total des mémoires: ${stats.totalMemories}\n`;
              response += `- Zone 1 (Récente): ${stats.zone1Count} mémoires\n`;
              response += `- Zone 2 (Chaude): ${stats.zone2Count} mémoires\n`;
              response += `- Zone 3 (Tiède): ${stats.zone3Count} mémoires\n`;
              response += `- Zone 4 (Fraîche): ${stats.zone4Count} mémoires\n`;
              response += `- Zone 5 (Froide): ${stats.zone5Count} mémoires\n`;
              response += `- Zone 6 (Archive): ${stats.zone6Count} mémoires`;
            } catch (error) {
              response = `Erreur lors de la récupération des statistiques de mémoire: ${error.message}`;
            }
          } else {
            response = 'Statut de la mémoire thermique:\n';
            response += '- Total des mémoires: 0\n';
            response += '- Mémoire thermique non initialisée';
          }
          break;
          
        case 'network':
          response = `Diagnostic réseau:\n`;
          response += `- Accès Internet: ${mcpState.internetAccess ? 'Activé' : 'Désactivé'}\n`;
          response += `- Latence: ${Math.floor(20 + Math.random() * 10)}ms\n`;
          response += `- Bande passante: ${Math.floor(50 + Math.random() * 50)} Mbps\n`;
          response += `- Connexions actives: ${Math.floor(1 + Math.random() * 5)}\n`;
          response += `- Paquets envoyés: ${Math.floor(1000 + Math.random() * 5000)}\n`;
          response += `- Paquets reçus: ${Math.floor(1000 + Math.random() * 5000)}`;
          break;
          
        default:
          response = `Commande inconnue: ${command}`;
      }
      
      socket.emit('mcp command response', {
        success: true,
        command,
        response
      });
    });
  });
}

// Charger la configuration au démarrage
loadMcpConfig();

module.exports = {
  router,
  initSocketHandlers
};
