/**
 * Routes pour la gestion des prompts Luna
 */

const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Chemin vers le fichier de stockage des prompts
const PROMPTS_FILE = path.join(__dirname, '../data/prompts.json');

// S'assurer que le dossier data existe
const DATA_DIR = path.join(__dirname, '../data');
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Charger les prompts depuis le fichier
function loadPrompts() {
  try {
    if (fs.existsSync(PROMPTS_FILE)) {
      const data = JSON.parse(fs.readFileSync(PROMPTS_FILE, 'utf8'));
      return data;
    } else {
      // Créer un fichier vide avec la structure de base
      const emptyData = {
        prompts: [],
        customCategories: [],
        lastModified: new Date().toISOString()
      };
      fs.writeFileSync(PROMPTS_FILE, JSON.stringify(emptyData, null, 2), 'utf8');
      return emptyData;
    }
  } catch (error) {
    console.error('Erreur lors du chargement des prompts:', error);
    return {
      prompts: [],
      customCategories: [],
      lastModified: new Date().toISOString()
    };
  }
}

// Sauvegarder les prompts dans le fichier
function savePrompts(data) {
  try {
    // Mettre à jour la date de dernière modification
    data.lastModified = new Date().toISOString();
    
    // Écrire dans le fichier
    fs.writeFileSync(PROMPTS_FILE, JSON.stringify(data, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('Erreur lors de la sauvegarde des prompts:', error);
    return false;
  }
}

// Route pour la page des prompts
router.get('/prompts', (req, res) => {
  res.render('luna-base', {
    page: 'luna-prompts',
    title: 'Luna - Bibliothèque de Prompts'
  });
});

// Initialiser les gestionnaires de socket pour les prompts
function initSocketHandlers(io) {
  io.on('connection', (socket) => {
    console.log('Client connecté aux prompts:', socket.id);
    
    // Charger les prompts
    socket.on('load prompts', () => {
      try {
        const data = loadPrompts();
        socket.emit('prompts loaded', {
          success: true,
          prompts: data.prompts,
          customCategories: data.customCategories,
          lastModified: data.lastModified
        });
      } catch (error) {
        console.error('Erreur lors du chargement des prompts:', error);
        socket.emit('prompts loaded', {
          success: false,
          error: error.message
        });
      }
    });
    
    // Sauvegarder un prompt
    socket.on('save prompt', (promptData) => {
      try {
        const data = loadPrompts();
        
        // Vérifier si le prompt existe déjà
        const existingIndex = data.prompts.findIndex(p => p.id === promptData.id);
        
        if (existingIndex >= 0) {
          // Mettre à jour le prompt existant
          data.prompts[existingIndex] = {
            ...data.prompts[existingIndex],
            ...promptData,
            lastModified: new Date().toISOString()
          };
        } else {
          // Ajouter un nouveau prompt
          data.prompts.push({
            ...promptData,
            id: promptData.id || `prompt_${Date.now()}`,
            created: new Date().toISOString(),
            lastModified: new Date().toISOString()
          });
        }
        
        // Sauvegarder les changements
        if (savePrompts(data)) {
          socket.emit('prompt saved', { success: true });
        } else {
          socket.emit('prompt saved', {
            success: false,
            error: 'Erreur lors de la sauvegarde du prompt'
          });
        }
      } catch (error) {
        console.error('Erreur lors de la sauvegarde du prompt:', error);
        socket.emit('prompt saved', {
          success: false,
          error: error.message
        });
      }
    });
    
    // Supprimer un prompt
    socket.on('delete prompt', (data) => {
      try {
        const promptsData = loadPrompts();
        
        // Filtrer le prompt à supprimer
        promptsData.prompts = promptsData.prompts.filter(p => p.id !== data.id);
        
        // Sauvegarder les changements
        if (savePrompts(promptsData)) {
          socket.emit('prompt deleted', { success: true });
        } else {
          socket.emit('prompt deleted', {
            success: false,
            error: 'Erreur lors de la suppression du prompt'
          });
        }
      } catch (error) {
        console.error('Erreur lors de la suppression du prompt:', error);
        socket.emit('prompt deleted', {
          success: false,
          error: error.message
        });
      }
    });
    
    // Sauvegarder les catégories personnalisées
    socket.on('save categories', (data) => {
      try {
        const promptsData = loadPrompts();
        
        // Mettre à jour les catégories
        promptsData.customCategories = data.categories;
        
        // Sauvegarder les changements
        if (savePrompts(promptsData)) {
          socket.emit('categories saved', { success: true });
        } else {
          socket.emit('categories saved', {
            success: false,
            error: 'Erreur lors de la sauvegarde des catégories'
          });
        }
      } catch (error) {
        console.error('Erreur lors de la sauvegarde des catégories:', error);
        socket.emit('categories saved', {
          success: false,
          error: error.message
        });
      }
    });
  });
}

module.exports = {
  router,
  initSocketHandlers
};
