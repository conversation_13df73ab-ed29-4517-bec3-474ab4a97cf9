/**
 * Script de démarrage sécurisé pour le serveur Luna
 * Ce script effectue des vérifications préliminaires avant de démarrer le serveur
 * et met en place une gestion d'erreurs robuste
 */

const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MAX_RESTART_ATTEMPTS = 3;
const RESTART_DELAY = 5000; // 5 secondes
let restartAttempts = 0;

// Fonction pour créer un fichier de log avec la date
function createLogFile() {
  const now = new Date();
  const dateStr = now.toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const logDir = path.join(__dirname, 'logs');

  // Créer le répertoire de logs s'il n'existe pas
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  return path.join(logDir, `luna-server-${dateStr}.log`);
}

// Fonction pour démarrer le serveur
function startServer() {
  console.log('🚀 Démarrage du serveur Luna...');

  // Créer un fichier de log
  const logFile = createLogFile();
  const logStream = fs.createWriteStream(logFile, { flags: 'a' });

  console.log(`📝 Les logs seront enregistrés dans: ${logFile}`);

  // Exécuter la validation de syntaxe
  try {
    console.log('🔍 Validation de la syntaxe...');
    execSync('node validate-syntax.js', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ La validation de syntaxe a échoué. Le serveur ne sera pas démarré.');
    process.exit(1);
  }

  // Démarrer le serveur
  const server = spawn('node', ['server-luna.js'], {
    stdio: ['inherit', 'pipe', 'pipe'],
    env: { ...process.env, NODE_ENV: 'production' }
  });

  // Rediriger la sortie standard vers la console et le fichier de log
  server.stdout.on('data', (data) => {
    process.stdout.write(data);
    logStream.write(data);
  });

  // Rediriger la sortie d'erreur vers la console et le fichier de log
  server.stderr.on('data', (data) => {
    process.stderr.write(data);
    logStream.write(data);
  });

  // Gérer la fermeture du serveur
  server.on('close', (code) => {
    logStream.end();

    if (code !== 0 && restartAttempts < MAX_RESTART_ATTEMPTS) {
      restartAttempts++;
      console.error(`❌ Le serveur s'est arrêté avec le code ${code}. Tentative de redémarrage ${restartAttempts}/${MAX_RESTART_ATTEMPTS} dans ${RESTART_DELAY/1000} secondes...`);

      setTimeout(() => {
        startServer();
      }, RESTART_DELAY);
    } else if (code !== 0) {
      console.error(`❌ Le serveur s'est arrêté avec le code ${code} après ${MAX_RESTART_ATTEMPTS} tentatives de redémarrage.`);
      process.exit(1);
    } else {
      console.log("✅ Le serveur s'est arrêté normalement.");
    }
  });

  // Gérer les erreurs du processus
  server.on('error', (error) => {
    console.error('❌ Erreur lors du démarrage du serveur:', error.message);
    logStream.write(`Erreur: ${error.message}\n`);
    logStream.end();
    process.exit(1);
  });

  // Gérer les signaux d'arrêt
  process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt du serveur...');
    server.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Arrêt du serveur...');
    server.kill('SIGTERM');
  });
}

// Démarrer le serveur
startServer();
