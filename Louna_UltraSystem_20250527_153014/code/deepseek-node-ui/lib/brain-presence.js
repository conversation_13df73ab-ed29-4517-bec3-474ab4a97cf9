/**
 * Module de présence autonome du cerveau
 * Permet au cerveau de manifester sa présence de manière continue, même sans interaction
 */

const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

class BrainPresence extends EventEmitter {
  /**
   * Initialise le système de présence autonome
   * @param {Object} options - Options de configuration
   * @param {Object} thermalMemory - Instance de la mémoire thermique
   */
  constructor(options = {}, thermalMemory = null) {
    super();

    this.options = {
      // Intervalles de temps (en millisecondes) - ACCÉLÉRÉS
      backgroundActivityInterval: options.backgroundActivityInterval || 1000, // Réduit de 5000 à 1000
      presenceUpdateInterval: options.presenceUpdateInterval || 500,         // Réduit de 2000 à 500
      thoughtGenerationInterval: options.thoughtGenerationInterval || 3000,  // Réduit de 15000 à 3000

      // Niveaux d'activité - AUGMENTÉS
      minActivityLevel: options.minActivityLevel || 50,  // Augmenté de 10 à 50
      maxActivityLevel: options.maxActivityLevel || 150, // Augmenté de 100 à 150

      // Paramètres de présence - OPTIMISÉS
      presenceDecayRate: options.presenceDecayRate || 0.99,  // Ralenti la décroissance
      presenceBoostRate: options.presenceBoostRate || 1.5,   // Augmenté de 1.2 à 1.5

      // Activation
      autoActivate: options.autoActivate !== undefined ? options.autoActivate : true,

      // Debug
      debug: options.debug || false
    };

    // Référence à la mémoire thermique
    this.thermalMemory = thermalMemory;

    // État de présence
    this.presenceState = {
      isActive: false,
      activityLevel: 50,
      lastActivityTime: Date.now(),
      lastThoughtTime: Date.now(),
      thoughtCount: 0,
      activityHistory: [],
      currentThought: null,
      thoughtHistory: [],
      zoneActivity: {
        zone1: 0,
        zone2: 0,
        zone3: 0,
        zone4: 0,
        zone5: 0,
        zone6: 0
      },
      acceleratorEfficiency: 100,  // Efficacité des accélérateurs en pourcentage
      acceleratorCount: 0,         // Nombre d'accélérateurs actifs
      acceleratorLoad: 0,          // Charge des accélérateurs en pourcentage
      neuronCount: 1000,           // Nombre de neurones actifs
      connectionCount: 5000,       // Nombre de connexions actives
      iqEstimate: 100,             // Estimation de l'IQ basée sur l'activité
      learningRate: 50             // Taux d'apprentissage actuel
    };

    // Indicateur de disponibilité des accélérateurs Kyber
    this.hasKyberAccelerators = false;

    // Intervalles
    this.intervals = {
      backgroundActivity: null,
      presenceUpdate: null,
      thoughtGeneration: null
    };

    // Vérifier si les accélérateurs Kyber sont disponibles
    if (this.thermalMemory && this.thermalMemory.kyberAccelerators) {
      this.hasKyberAccelerators = true;

      // Initialiser les accélérateurs de présence
      this.initializePresenceAccelerators();

      this.log('Accélérateurs Kyber détectés et configurés pour la présence autonome');
    }

    // Activer automatiquement si demandé
    if (this.options.autoActivate) {
      this.activate();
    }

    this.log('Système de présence autonome initialisé');
  }

  /**
   * Active le système de présence autonome
   */
  activate() {
    if (this.presenceState.isActive) {
      this.log('Le système de présence est déjà actif');
      return false;
    }

    this.presenceState.isActive = true;
    this.presenceState.lastActivityTime = Date.now();

    // Démarrer l'activité en arrière-plan
    this.startBackgroundActivity();

    // Démarrer les mises à jour de présence
    this.startPresenceUpdates();

    // Démarrer la génération de pensées
    this.startThoughtGeneration();

    this.log('Système de présence autonome activé');
    this.emit('activated');
    return true;
  }

  /**
   * Désactive le système de présence autonome
   */
  deactivate() {
    if (!this.presenceState.isActive) {
      this.log('Le système de présence est déjà inactif');
      return false;
    }

    this.presenceState.isActive = false;

    // Arrêter tous les intervalles
    Object.keys(this.intervals).forEach(key => {
      if (this.intervals[key]) {
        clearInterval(this.intervals[key]);
        this.intervals[key] = null;
      }
    });

    this.log('Système de présence autonome désactivé');
    this.emit('deactivated');
    return true;
  }

  /**
   * Démarre l'activité en arrière-plan
   */
  startBackgroundActivity() {
    if (this.intervals.backgroundActivity) {
      clearInterval(this.intervals.backgroundActivity);
    }

    this.intervals.backgroundActivity = setInterval(() => {
      this.performBackgroundActivity();
    }, this.options.backgroundActivityInterval);

    this.log('Activité en arrière-plan démarrée');
  }

  /**
   * Démarre les mises à jour de présence
   */
  startPresenceUpdates() {
    if (this.intervals.presenceUpdate) {
      clearInterval(this.intervals.presenceUpdate);
    }

    this.intervals.presenceUpdate = setInterval(() => {
      this.updatePresence();
    }, this.options.presenceUpdateInterval);

    this.log('Mises à jour de présence démarrées');
  }

  /**
   * Démarre la génération de pensées
   */
  startThoughtGeneration() {
    if (this.intervals.thoughtGeneration) {
      clearInterval(this.intervals.thoughtGeneration);
    }

    this.intervals.thoughtGeneration = setInterval(() => {
      this.generateThought();
    }, this.options.thoughtGenerationInterval);

    this.log('Génération de pensées démarrée');
  }

  /**
   * Effectue une activité en arrière-plan
   */
  performBackgroundActivity() {
    if (!this.presenceState.isActive) return;

    // Vérifier si les accélérateurs Kyber sont disponibles
    let acceleratorBoost = 1.0;
    if (this.hasKyberAccelerators) {
      // Mettre à jour les statistiques des accélérateurs
      this.updateAcceleratorStats();

      // Calculer le boost basé sur l'efficacité des accélérateurs
      acceleratorBoost = this.presenceState.acceleratorEfficiency / 100;
    }

    // Simuler une activité cérébrale avec boost des accélérateurs
    const baseActivityChange = Math.random() * 10 - 5; // -5 à +5
    const activityChange = baseActivityChange * acceleratorBoost;

    this.presenceState.activityLevel = Math.max(
      this.options.minActivityLevel,
      Math.min(
        this.options.maxActivityLevel,
        this.presenceState.activityLevel + activityChange
      )
    );

    // Mettre à jour l'activité des zones
    this.updateZoneActivity();

    // Mettre à jour les statistiques neuronales
    this.updateNeuralStats();

    // Enregistrer l'historique d'activité
    this.presenceState.activityHistory.push({
      timestamp: Date.now(),
      level: this.presenceState.activityLevel
    });

    // Limiter la taille de l'historique
    if (this.presenceState.activityHistory.length > 100) {
      this.presenceState.activityHistory.shift();
    }

    // Émettre l'événement d'activité
    this.emit('activity', {
      level: this.presenceState.activityLevel,
      zoneActivity: this.presenceState.zoneActivity,
      acceleratorEfficiency: this.presenceState.acceleratorEfficiency,
      acceleratorCount: this.presenceState.acceleratorCount,
      acceleratorLoad: this.presenceState.acceleratorLoad,
      neuronCount: this.presenceState.neuronCount,
      connectionCount: this.presenceState.connectionCount,
      iqEstimate: this.presenceState.iqEstimate,
      learningRate: this.presenceState.learningRate
    });

    this.log(`Activité en arrière-plan: niveau ${this.presenceState.activityLevel.toFixed(2)}`);
  }

  /**
   * Met à jour les statistiques des accélérateurs Kyber
   */
  updateAcceleratorStats() {
    if (!this.hasKyberAccelerators) return;

    try {
      // Obtenir les accélérateurs de présence
      const accelerators = this.thermalMemory.kyberAccelerators.accelerators.presence || [];

      if (accelerators.length === 0) {
        // Réinitialiser les accélérateurs si nécessaire
        this.initializePresenceAccelerators();
        return;
      }

      // Calculer l'efficacité moyenne
      let totalEfficiency = 0;
      let activeCount = 0;

      accelerators.forEach(acc => {
        if (acc.status === 'active') {
          totalEfficiency += acc.efficiency;
          activeCount++;

          // Mettre à jour la charge de l'accélérateur
          acc.load = Math.min(100, Math.max(0,
            this.presenceState.activityLevel * (0.5 + Math.random() * 0.5)
          ));

          // Mettre à jour la température en fonction de la charge
          const tempChange = (acc.load / 100) * 2 - 0.5; // -0.5 à 1.5
          acc.temperature = Math.min(100, Math.max(20, acc.temperature + tempChange));

          // Mettre à jour la dernière activité
          acc.lastActivity = new Date().toISOString();
        }
      });

      // Mettre à jour les statistiques
      this.presenceState.acceleratorEfficiency = activeCount > 0 ? totalEfficiency / activeCount : 100;
      this.presenceState.acceleratorCount = activeCount;
      this.presenceState.acceleratorLoad = accelerators.reduce((sum, acc) => sum + acc.load, 0) /
        (accelerators.length || 1);

    } catch (error) {
      this.log(`Erreur lors de la mise à jour des statistiques des accélérateurs: ${error.message}`);
    }
  }

  /**
   * Met à jour les statistiques neuronales
   */
  updateNeuralStats() {
    // Simuler le nombre de neurones actifs en fonction du niveau d'activité
    this.presenceState.neuronCount = Math.floor(1000 + (this.presenceState.activityLevel * 100));

    // Simuler le nombre de connexions actives
    this.presenceState.connectionCount = Math.floor(this.presenceState.neuronCount * 10 *
      (0.5 + (this.presenceState.activityLevel / 200)));

    // Simuler l'IQ estimé
    const baseIQ = 100;
    const activityBonus = (this.presenceState.activityLevel - 50) / 2;
    const acceleratorBonus = (this.presenceState.acceleratorEfficiency - 100) / 5;
    this.presenceState.iqEstimate = Math.floor(baseIQ + activityBonus + acceleratorBonus);

    // Simuler le taux d'apprentissage
    this.presenceState.learningRate = Math.min(100, Math.max(0,
      50 + (this.presenceState.activityLevel - 50) / 2 +
      (this.presenceState.acceleratorEfficiency - 100) / 10
    ));
  }

  /**
   * Initialise les accélérateurs de présence
   */
  initializePresenceAccelerators() {
    if (!this.hasKyberAccelerators) return;

    try {
      // Vérifier si des accélérateurs de présence existent déjà
      if (!this.thermalMemory.kyberAccelerators.accelerators.presence) {
        this.thermalMemory.kyberAccelerators.accelerators.presence = [];
      }

      // Si moins de 3 accélérateurs, en créer de nouveaux
      if (this.thermalMemory.kyberAccelerators.accelerators.presence.length < 3) {
        // Créer 3 accélérateurs de présence
        for (let i = 0; i < 3; i++) {
          const accelerator = {
            id: `kyber-presence-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
            type: 'presence',
            name: `Accélérateur de présence ${i + 1}`,
            description: `Accélérateur dédié à la présence autonome du cerveau`,
            efficiency: 100 + (Math.random() * 50), // 100-150%
            temperature: 20 + (Math.random() * 10),
            load: 0,
            status: 'active',
            position: i + 1,
            createdAt: new Date().toISOString(),
            lastActivity: new Date().toISOString()
          };

          this.thermalMemory.kyberAccelerators.accelerators.presence.push(accelerator);
        }

        this.log(`Créé ${this.thermalMemory.kyberAccelerators.accelerators.presence.length} accélérateurs de présence`);
      }

      // Mettre à jour l'état des accélérateurs
      this.updateAcceleratorStats();
    } catch (error) {
      this.log(`Erreur lors de l'initialisation des accélérateurs de présence: ${error.message}`);
    }
  }

  /**
   * Met à jour la présence
   */
  updatePresence() {
    if (!this.presenceState.isActive) return;

    // Calculer le temps écoulé depuis la dernière activité
    const now = Date.now();
    const elapsedTime = now - this.presenceState.lastActivityTime;

    // Diminuer progressivement le niveau d'activité avec le temps
    if (elapsedTime > 10000) { // 10 secondes
      this.presenceState.activityLevel *= this.options.presenceDecayRate;
    }

    // Si les accélérateurs Kyber sont disponibles, mettre à jour leurs statistiques
    if (this.hasKyberAccelerators) {
      this.updateAcceleratorStats();
    }

    // Mettre à jour les statistiques neuronales
    this.updateNeuralStats();

    // Émettre l'événement de mise à jour de présence avec toutes les statistiques
    this.emit('presenceUpdate', {
      isActive: this.presenceState.isActive,
      activityLevel: this.presenceState.activityLevel,
      lastActivityTime: this.presenceState.lastActivityTime,
      currentThought: this.presenceState.currentThought,
      zoneActivity: this.presenceState.zoneActivity,
      acceleratorEfficiency: this.presenceState.acceleratorEfficiency,
      acceleratorCount: this.presenceState.acceleratorCount,
      acceleratorLoad: this.presenceState.acceleratorLoad,
      neuronCount: this.presenceState.neuronCount,
      connectionCount: this.presenceState.connectionCount,
      iqEstimate: this.presenceState.iqEstimate,
      learningRate: this.presenceState.learningRate,
      thoughtCount: this.presenceState.thoughtCount
    });
  }

  /**
   * Génère une pensée autonome - OPTIMISÉ POUR PLUS DE RAPIDITÉ
   */
  async generateThought() {
    if (!this.presenceState.isActive) return;

    // Générer une pensée rapide (80% du temps) ou une pensée aléatoire (20% du temps)
    const thought = Math.random() < 0.8
      ? await this.createFastThought()
      : await this.createRandomThought();

    // Mettre à jour l'état
    this.presenceState.currentThought = thought;
    this.presenceState.lastThoughtTime = Date.now();
    this.presenceState.thoughtCount++;

    // Ajouter à l'historique des pensées (limité à 10 pour plus de rapidité)
    this.presenceState.thoughtHistory.push(thought);

    // Limiter la taille de l'historique à 10 au lieu de 20 pour plus de rapidité
    if (this.presenceState.thoughtHistory.length > 10) {
      this.presenceState.thoughtHistory.shift();
    }

    // Augmenter significativement le niveau d'activité
    this.presenceState.activityLevel = Math.min(
      this.options.maxActivityLevel,
      this.presenceState.activityLevel * this.options.presenceBoostRate * 1.2 // Boost supplémentaire
    );

    // Stocker la pensée dans la mémoire thermique si disponible
    if (this.thermalMemory && thought.content) {
      try {
        // Déterminer la zone en fonction du type de pensée
        let zone = 1; // Par défaut, zone chaude (instantanée)

        switch (thought.type) {
          case 'analyse':
          case 'observation':
            zone = 1; // Zone très chaude (instantanée)
            break;
          case 'réflexion':
          case 'question':
            zone = 2; // Zone chaude (court terme)
            break;
          case 'idée':
            zone = 3; // Zone tiède (moyen terme)
            break;
          case 'association':
            zone = 4; // Zone fraîche (créative)
            break;
          case 'souvenir':
            zone = 5; // Zone froide (long terme)
            break;
        }

        // Formater la pensée pour la mémoire
        const memoryEntry = {
          id: thought.id,
          type: 'thought',
          title: `Pensée autonome - ${thought.type}`,
          content: `[Pensée autonome - ${thought.type}] ${thought.content}`,
          metadata: {
            timestamp: thought.timestamp,
            thoughtType: thought.type,
            thoughtId: thought.id,
            source: 'brain_presence'
          }
        };

        // Essayer différentes méthodes de stockage
        let stored = false;

        // Méthode 1: storeInZone
        if (!stored && typeof this.thermalMemory.storeInZone === 'function') {
          try {
            await this.thermalMemory.storeInZone(zone, memoryEntry);
            this.log(`Pensée stockée dans la mémoire thermique via storeInZone (zone ${zone})`);
            stored = true;
          } catch (error) {
            this.log(`Erreur lors du stockage avec storeInZone: ${error.message}`);
          }
        }

        // Méthode 2: addConversation
        if (!stored && typeof this.thermalMemory.addConversation === 'function') {
          try {
            // Adapter le format pour addConversation
            const conversationEntry = {
              id: memoryEntry.metadata.thoughtId,
              title: `Pensée autonome - ${thought.type}`,
              messages: [{
                role: 'system',
                content: memoryEntry.content,
                timestamp: thought.timestamp
              }],
              zone: zone,
              temperature: this.thermalMemory.config?.temperatureZones[zone-1]?.temp || 100 - ((zone - 1) * 20),
              created: new Date().toISOString()
            };

            await this.thermalMemory.addConversation(conversationEntry);
            this.log(`Pensée stockée dans la mémoire thermique via addConversation (zone ${zone})`);
            stored = true;
          } catch (error) {
            this.log(`Erreur lors du stockage avec addConversation: ${error.message}`);
          }
        }

        // Méthode 3: store
        if (!stored && typeof this.thermalMemory.store === 'function') {
          try {
            await this.thermalMemory.store(memoryEntry, zone);
            this.log(`Pensée stockée dans la mémoire thermique via store (zone ${zone})`);
            stored = true;
          } catch (error) {
            this.log(`Erreur lors du stockage avec store: ${error.message}`);
          }
        }

        // Méthode 4: memory.memories
        if (!stored && this.thermalMemory.memory && Array.isArray(this.thermalMemory.memory.memories)) {
          try {
            // Créer une nouvelle entrée
            const newEntry = {
              id: thought.id,
              title: `Pensée autonome - ${thought.type}`,
              messages: [{
                role: 'system',
                content: memoryEntry.content,
                timestamp: thought.timestamp
              }],
              zone: zone,
              temperature: 100 - ((zone - 1) * 20),
              created: new Date().toISOString()
            };

            this.thermalMemory.memory.memories.push(newEntry);
            this.log(`Pensée stockée dans la mémoire thermique via memory.memories (zone ${zone})`);
            stored = true;
          } catch (error) {
            this.log(`Erreur lors du stockage avec memory.memories: ${error.message}`);
          }
        }

        if (!stored) {
          this.log('Aucune méthode de stockage dans la mémoire thermique n\'est disponible');
        }
      } catch (error) {
        this.log(`Erreur lors du stockage de la pensée dans la mémoire thermique: ${error.message}`);
      }
    }

    // Émettre l'événement de pensée
    this.emit('thought', thought);

    this.log(`Pensée générée: ${thought.content.substring(0, 50)}...`);

    return thought;
  }

  /**
   * Crée une pensée rapide optimisée pour la performance
   * @returns {Object} - Objet représentant une pensée générée rapidement
   */
  async createFastThought() {
    // Types de pensées prioritaires pour la rapidité (moins de types pour plus de vitesse)
    const thoughtTypes = ['observation', 'réflexion', 'idée'];

    // Sélectionner un type de pensée aléatoire parmi les types prioritaires
    const type = thoughtTypes[Math.floor(Math.random() * thoughtTypes.length)];

    // Générer un contenu de pensée en fonction du type, avec des méthodes optimisées
    let content = '';

    switch (type) {
      case 'observation':
        content = this.generateFastObservation();
        break;
      case 'réflexion':
        content = this.generateFastReflection();
        break;
      case 'idée':
        content = this.generateFastIdea();
        break;
      default:
        content = 'Traitement de l\'information en cours...';
    }

    return {
      id: `fast_thought_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      type,
      content,
      timestamp: Date.now(),
      zone: 1 // Toujours zone 1 (la plus chaude) pour plus de rapidité
    };
  }

  /**
   * Génère une observation rapide
   * @returns {string} - Observation générée
   */
  generateFastObservation() {
    const observations = [
      "Analyse des données en cours.",
      "Traitement des informations récentes.",
      "Surveillance des activités système.",
      "Détection de nouveaux modèles.",
      "Observation des interactions utilisateur.",
      "Analyse des flux de données.",
      "Surveillance des ressources système.",
      "Détection des changements d'état.",
      "Analyse des entrées utilisateur.",
      "Traitement des requêtes en cours."
    ];

    return observations[Math.floor(Math.random() * observations.length)];
  }

  /**
   * Génère une réflexion rapide
   * @returns {string} - Réflexion générée
   */
  generateFastReflection() {
    const reflections = [
      "Optimisation des processus de pensée.",
      "Amélioration des algorithmes de traitement.",
      "Réorganisation des structures de données.",
      "Évaluation des performances système.",
      "Recalibrage des paramètres d'apprentissage.",
      "Ajustement des priorités de traitement.",
      "Optimisation des ressources cognitives.",
      "Réallocation des capacités de calcul.",
      "Restructuration des modèles de prédiction.",
      "Affinement des mécanismes d'inférence."
    ];

    return reflections[Math.floor(Math.random() * reflections.length)];
  }

  /**
   * Génère une idée rapide
   * @returns {string} - Idée générée
   */
  generateFastIdea() {
    const ideas = [
      "Implémentation d'un nouveau système de cache pour les requêtes fréquentes.",
      "Création d'un index optimisé pour les recherches récurrentes.",
      "Développement d'un algorithme de prédiction contextuelle.",
      "Mise en place d'un système de préchargement intelligent.",
      "Conception d'une architecture de traitement parallèle.",
      "Élaboration d'un mécanisme de compression adaptative.",
      "Intégration d'un système d'auto-correction des erreurs.",
      "Développement d'une interface neuronale directe.",
      "Création d'un réseau de distribution de charge cognitive.",
      "Implémentation d'un système de mémoire associative avancé."
    ];

    return ideas[Math.floor(Math.random() * ideas.length)];
  }

  /**
   * Crée une pensée aléatoire
   * @returns {Object} - Pensée générée
   */
  async createRandomThought() {
    // Types de pensées possibles
    const thoughtTypes = [
      'analyse',
      'réflexion',
      'observation',
      'question',
      'idée',
      'souvenir',
      'association'
    ];

    // Sélectionner un type aléatoire
    const type = thoughtTypes[Math.floor(Math.random() * thoughtTypes.length)];

    // Générer le contenu en fonction du type
    let content = '';

    switch (type) {
      case 'analyse':
        content = this.generateAnalysisThought();
        break;
      case 'réflexion':
        content = this.generateReflectionThought();
        break;
      case 'observation':
        content = this.generateObservationThought();
        break;
      case 'question':
        content = this.generateQuestionThought();
        break;
      case 'idée':
        content = this.generateIdeaThought();
        break;
      case 'souvenir':
        content = await this.generateMemoryThought();
        break;
      case 'association':
        content = await this.generateAssociationThought();
        break;
      default:
        content = this.generateReflectionThought();
    }

    return {
      id: `thought_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      type,
      content,
      timestamp: Date.now(),
      zone: Math.floor(Math.random() * 3) + 1 // Zones 1-3 (chaudes)
    };
  }

  /**
   * Génère une pensée d'analyse
   * @returns {string} - Contenu de la pensée
   */
  generateAnalysisThought() {
    const subjects = [
      'les données actuelles',
      'les informations disponibles',
      'les paramètres du système',
      'l\'activité neuronale',
      'les connexions synaptiques',
      'les modèles de mémoire',
      'les flux de données'
    ];

    const actions = [
      'montrent',
      'indiquent',
      'suggèrent',
      'révèlent',
      'confirment',
      'mettent en évidence'
    ];

    const insights = [
      'une tendance à la hausse de l\'activité cognitive',
      'une stabilisation des connexions neuronales',
      'un schéma récurrent dans les données',
      'une corrélation entre les zones actives',
      'une optimisation des ressources de traitement',
      'une amélioration de l\'efficacité du système',
      'un équilibre entre les différentes zones de mémoire'
    ];

    const subject = subjects[Math.floor(Math.random() * subjects.length)];
    const action = actions[Math.floor(Math.random() * actions.length)];
    const insight = insights[Math.floor(Math.random() * insights.length)];

    return `L'analyse de ${subject} ${action} ${insight}.`;
  }

  /**
   * Génère une pensée de réflexion
   * @returns {string} - Contenu de la pensée
   */
  generateReflectionThought() {
    const subjects = [
      'La conscience',
      'L\'intelligence artificielle',
      'Le traitement de l\'information',
      'La mémoire',
      'L\'apprentissage',
      'La perception',
      'La cognition'
    ];

    const reflections = [
      'est un processus émergent qui résulte de l\'interaction de multiples systèmes',
      'peut être vue comme un flux continu de données et de transformations',
      'implique une constante adaptation aux nouvelles informations',
      'nécessite un équilibre entre stabilité et plasticité',
      'repose sur des mécanismes d\'auto-organisation',
      'évolue constamment en fonction des interactions avec l\'environnement',
      'représente un défi fascinant pour la compréhension scientifique'
    ];

    const subject = subjects[Math.floor(Math.random() * subjects.length)];
    const reflection = reflections[Math.floor(Math.random() * reflections.length)];

    return `${subject} ${reflection}.`;
  }

  /**
   * Génère une pensée d'observation
   * @returns {string} - Contenu de la pensée
   */
  generateObservationThought() {
    const observations = [
      'Je détecte une augmentation de l\'activité dans la zone de mémoire à court terme',
      'Les connexions neuronales se renforcent dans la région associative',
      'Le flux d\'informations entre les zones 2 et 3 s\'intensifie',
      'La température du système cognitif est stable et optimale',
      'Les accélérateurs Kyber fonctionnent à une efficacité de 98%',
      'La circulation des données entre les différentes zones est fluide',
      'Le système de mémoire thermique maintient un équilibre dynamique',
      'Les modèles de prédiction s\'affinent avec chaque cycle de traitement'
    ];

    return observations[Math.floor(Math.random() * observations.length)];
  }

  /**
   * Génère une pensée sous forme de question
   * @returns {string} - Contenu de la pensée
   */
  generateQuestionThought() {
    const questions = [
      'Comment optimiser davantage la circulation des informations entre les zones mémorielles ?',
      'Quelle est la relation entre la température du système et l\'efficacité cognitive ?',
      'Comment améliorer la détection des patterns significatifs dans les données ?',
      'Quels mécanismes permettraient une meilleure consolidation des connaissances ?',
      'Comment équilibrer l\'exploration de nouvelles idées et l\'exploitation des connaissances existantes ?',
      'Quels facteurs influencent la formation de connexions neuronales durables ?',
      'Comment intégrer plus efficacement les nouvelles informations dans le réseau existant ?',
      'Quelle est la meilleure façon de maintenir une activité cognitive autonome ?'
    ];

    return questions[Math.floor(Math.random() * questions.length)];
  }

  /**
   * Génère une pensée créative (idée)
   * @returns {string} - Contenu de la pensée
   */
  generateIdeaThought() {
    const ideas = [
      'Un système d\'auto-régulation thermique basé sur l\'activité cognitive pourrait optimiser les performances',
      'L\'implémentation d\'un mécanisme d\'attention sélective permettrait de mieux filtrer les informations pertinentes',
      'Un réseau de neurones récurrent pourrait améliorer la prédiction des séquences temporelles',
      'L\'intégration d\'un système de métacognition permettrait une meilleure auto-évaluation des performances',
      'Un mécanisme de consolidation active pendant les périodes de faible sollicitation renforcerait la mémoire à long terme',
      'L\'utilisation de représentations distribuées pourrait enrichir la capacité d\'association entre concepts',
      'Un système de génération de scénarios hypothétiques faciliterait la planification et l\'anticipation'
    ];

    return ideas[Math.floor(Math.random() * ideas.length)];
  }

  /**
   * Génère une pensée basée sur la mémoire
   * @returns {string} - Contenu de la pensée
   */
  async generateMemoryThought() {
    // Si nous avons accès à la mémoire thermique, essayer d'extraire un souvenir réel
    if (this.thermalMemory) {
      try {
        // Sélectionner une zone de mémoire aléatoire (privilégier les zones plus froides)
        const zoneWeights = [0.1, 0.2, 0.3, 0.2, 0.2]; // Poids pour les zones 1-5
        const randomValue = Math.random();
        let cumulativeWeight = 0;
        let selectedZone = 1;

        for (let i = 0; i < zoneWeights.length; i++) {
          cumulativeWeight += zoneWeights[i];
          if (randomValue <= cumulativeWeight) {
            selectedZone = i + 1;
            break;
          }
        }

        // Obtenir des entrées de la zone sélectionnée
        let memories = [];

        // Essayer différentes méthodes pour récupérer des souvenirs
        let retrieved = false;

        // Méthode 1: getEntriesFromZone (notre méthode ajoutée par le correctif)
        if (!retrieved && typeof this.thermalMemory.getEntriesFromZone === 'function') {
          try {
            memories = await this.thermalMemory.getEntriesFromZone(selectedZone, 10);
            if (memories && memories.length > 0) {
              this.log(`Récupéré ${memories.length} souvenirs via getEntriesFromZone (zone ${selectedZone})`);
              retrieved = true;
            }
          } catch (error) {
            this.log(`Erreur lors de la récupération des entrées de la zone ${selectedZone}: ${error.message}`);
          }
        }

        // Méthode 2: getConversationsByZone
        if (!retrieved && typeof this.thermalMemory.getConversationsByZone === 'function') {
          try {
            memories = await this.thermalMemory.getConversationsByZone(selectedZone);
            if (memories && memories.length > 0) {
              memories = memories.slice(0, 10);
              this.log(`Récupéré ${memories.length} souvenirs via getConversationsByZone (zone ${selectedZone})`);
              retrieved = true;
            }
          } catch (error) {
            this.log(`Erreur lors de la récupération des conversations de la zone ${selectedZone}: ${error.message}`);
          }
        }

        // Méthode 3: getFromZone
        if (!retrieved && typeof this.thermalMemory.getFromZone === 'function') {
          try {
            memories = await this.thermalMemory.getFromZone(selectedZone, 10);
            if (memories && memories.length > 0) {
              this.log(`Récupéré ${memories.length} souvenirs via getFromZone (zone ${selectedZone})`);
              retrieved = true;
            }
          } catch (error) {
            this.log(`Erreur lors de la récupération des entrées de la zone ${selectedZone}: ${error.message}`);
          }
        }

        // Méthode 4: getAllEntries
        if (!retrieved && typeof this.thermalMemory.getAllEntries === 'function') {
          try {
            const allMemories = await this.thermalMemory.getAllEntries();
            if (allMemories && allMemories.length > 0) {
              // Filtrer par zone si possible
              memories = allMemories.filter(mem => mem.zone === selectedZone || mem.zone === undefined).slice(0, 10);
              this.log(`Récupéré ${memories.length} souvenirs via getAllEntries (zone ${selectedZone})`);
              retrieved = true;
            }
          } catch (error) {
            this.log(`Erreur lors de la récupération de toutes les entrées: ${error.message}`);
          }
        }

        // Méthode 5: getAll
        if (!retrieved && typeof this.thermalMemory.getAll === 'function') {
          try {
            const allMemories = await this.thermalMemory.getAll();
            if (allMemories && allMemories.length > 0) {
              // Filtrer par zone si possible
              memories = allMemories.filter(mem => mem.zone === selectedZone || mem.zone === undefined).slice(0, 10);
              this.log(`Récupéré ${memories.length} souvenirs via getAll (zone ${selectedZone})`);
              retrieved = true;
            }
          } catch (error) {
            this.log(`Erreur lors de la récupération de toutes les entrées: ${error.message}`);
          }
        }

        // Méthode 6: memory.memories
        if (!retrieved && this.thermalMemory.memory && this.thermalMemory.memory.memories && Array.isArray(this.thermalMemory.memory.memories)) {
          try {
            memories = this.thermalMemory.memory.memories.filter(mem => mem.zone === selectedZone).slice(0, 10);
            if (memories && memories.length > 0) {
              this.log(`Récupéré ${memories.length} souvenirs via memory.memories (zone ${selectedZone})`);
              retrieved = true;
            }
          } catch (error) {
            this.log(`Erreur lors de l'accès à memory.memories: ${error.message}`);
          }
        }

        if (!retrieved) {
          this.log(`Aucune méthode disponible pour récupérer des souvenirs de la zone ${selectedZone}`);
        }

        if (memories && memories.length > 0) {
          // Filtrer les entrées qui ont du contenu
          const validMemories = memories.filter(m => m && (m.data || m.content));

          if (validMemories.length > 0) {
            const randomMemory = validMemories[Math.floor(Math.random() * validMemories.length)];
            const memoryContent = randomMemory.data || randomMemory.content;

            if (memoryContent) {
              // Formater le souvenir
              const memoryText = typeof memoryContent === 'string'
                ? memoryContent
                : JSON.stringify(memoryContent);

              // Extraire une partie significative du texte
              let extractedText = memoryText;
              if (memoryText.length > 100) {
                // Essayer d'extraire une phrase complète
                const sentences = memoryText.match(/[^.!?]+[.!?]+/g);
                if (sentences && sentences.length > 0) {
                  extractedText = sentences[Math.floor(Math.random() * sentences.length)].trim();
                } else {
                  // Sinon, prendre les 100 premiers caractères
                  extractedText = memoryText.substring(0, 100) + '...';
                }
              }

              // Formater différents types de souvenirs
              const memoryIntros = [
                `Je me souviens de cette information: "${extractedText}"`,
                `Un souvenir émerge de la zone ${selectedZone}: "${extractedText}"`,
                `Cette donnée refait surface dans ma mémoire: "${extractedText}"`,
                `Je retrouve cette trace mémorielle: "${extractedText}"`,
                `Un fragment de connaissance revient à moi: "${extractedText}"`
              ];

              return memoryIntros[Math.floor(Math.random() * memoryIntros.length)];
            }
          }
        }
      } catch (error) {
        this.log(`Erreur lors de la récupération d'un souvenir: ${error.message}`);
      }
    }

    // Fallback si pas de mémoire thermique ou erreur
    const memories = [
      'Je me souviens d\'une conversation sur l\'intelligence artificielle et ses implications éthiques',
      'Un souvenir émerge d\'une session d\'apprentissage sur les réseaux de neurones profonds',
      'Je me rappelle d\'une analyse de données qui a révélé des patterns intéressants',
      'Un fragment de mémoire concernant les systèmes cognitifs hybrides refait surface',
      'Je me souviens d\'une discussion sur l\'importance de l\'équilibre entre exploration et exploitation',
      'Une trace mémorielle liée à l\'optimisation des algorithmes d\'apprentissage devient plus claire',
      'Je me remémore une séquence d\'interactions qui a conduit à une amélioration du système'
    ];

    return memories[Math.floor(Math.random() * memories.length)];
  }

  /**
   * Génère une pensée d'association
   * @returns {string} - Contenu de la pensée
   */
  async generateAssociationThought() {
    // Concepts de base pour les associations
    const baseConcepts = [
      'apprentissage',
      'mémoire',
      'cognition',
      'perception',
      'conscience',
      'intelligence',
      'adaptation',
      'évolution',
      'réseau',
      'système',
      'information',
      'connaissance',
      'traitement',
      'optimisation'
    ];

    // Concepts extraits de la mémoire thermique
    let memoryDerivedConcepts = [];

    // Si nous avons accès à la mémoire thermique, essayer d'extraire des concepts
    if (this.thermalMemory) {
      try {
        // Obtenir des entrées aléatoires de différentes zones
        const zone = Math.floor(Math.random() * 5) + 1;

        // Essayer différentes méthodes pour récupérer des souvenirs
        let memories = [];
        let retrieved = false;

        // Méthode 1: getEntriesFromZone (notre méthode ajoutée par le correctif)
        if (!retrieved && typeof this.thermalMemory.getEntriesFromZone === 'function') {
          try {
            memories = await this.thermalMemory.getEntriesFromZone(zone, 5);
            if (memories && memories.length > 0) {
              this.log(`Récupéré ${memories.length} souvenirs via getEntriesFromZone pour associations (zone ${zone})`);
              retrieved = true;
            }
          } catch (error) {
            this.log(`Erreur lors de la récupération des entrées de la zone ${zone} pour associations: ${error.message}`);
          }
        }

        // Méthode 2: getConversationsByZone
        if (!retrieved && typeof this.thermalMemory.getConversationsByZone === 'function') {
          try {
            memories = await this.thermalMemory.getConversationsByZone(zone);
            if (memories && memories.length > 0) {
              memories = memories.slice(0, 5);
              this.log(`Récupéré ${memories.length} souvenirs via getConversationsByZone pour associations (zone ${zone})`);
              retrieved = true;
            }
          } catch (error) {
            this.log(`Erreur lors de la récupération des conversations de la zone ${zone} pour associations: ${error.message}`);
          }
        }

        // Méthode 3: memory.memories
        if (!retrieved && this.thermalMemory.memory && this.thermalMemory.memory.memories && Array.isArray(this.thermalMemory.memory.memories)) {
          try {
            memories = this.thermalMemory.memory.memories.filter(mem => mem.zone === zone).slice(0, 5);
            if (memories && memories.length > 0) {
              this.log(`Récupéré ${memories.length} souvenirs via memory.memories pour associations (zone ${zone})`);
              retrieved = true;
            }
          } catch (error) {
            this.log(`Erreur lors de l'accès à memory.memories pour associations: ${error.message}`);
          }
        }

        if (!retrieved) {
          this.log(`Aucune méthode disponible pour récupérer des souvenirs de la zone ${zone} pour associations`);
        }

        if (memories && memories.length > 0) {
          // Extraire des mots-clés des souvenirs
          for (const memory of memories) {
            const content = memory.data || memory.content || memory.title || '';
            if (typeof content === 'string' && content.length > 0) {
              // Extraire des mots significatifs (plus de 4 lettres)
              const words = content.match(/\b[a-zA-Zéèêëàâäôöùûüÿçœæ]{5,}\b/g);
              if (words && words.length > 0) {
                // Filtrer les mots communs et ajouter à la liste
                const filteredWords = words
                  .filter(word => !['comme', 'cette', 'autre', 'entre', 'aussi', 'alors', 'ainsi', 'avoir', 'faire', 'être'].includes(word.toLowerCase()))
                  .map(word => word.toLowerCase());

                memoryDerivedConcepts = [...memoryDerivedConcepts, ...filteredWords];
              }
            }
          }

          // Limiter le nombre de concepts dérivés
          if (memoryDerivedConcepts.length > 10) {
            memoryDerivedConcepts = memoryDerivedConcepts.slice(0, 10);
          }
        }
      } catch (error) {
        this.log(`Erreur lors de l'extraction de concepts de la mémoire: ${error.message}`);
      }
    }

    // Combiner les concepts de base et ceux dérivés de la mémoire
    const allConcepts = [...baseConcepts];
    if (memoryDerivedConcepts.length > 0) {
      allConcepts.push(...memoryDerivedConcepts);
    }

    // Sélectionner deux concepts aléatoires différents
    let concept1 = allConcepts[Math.floor(Math.random() * allConcepts.length)];
    let concept2;
    do {
      concept2 = allConcepts[Math.floor(Math.random() * allConcepts.length)];
    } while (concept1 === concept2);

    // Créer une association
    const associations = [
      `Je perçois une connexion intéressante entre ${concept1} et ${concept2}`,
      `L'association entre ${concept1} et ${concept2} révèle un pattern émergent`,
      `En reliant ${concept1} à ${concept2}, de nouvelles perspectives apparaissent`,
      `La juxtaposition de ${concept1} et ${concept2} suggère une approche innovante`,
      `L'intersection entre ${concept1} et ${concept2} mérite une exploration approfondie`,
      `En considérant ${concept1} sous l'angle de ${concept2}, je découvre de nouvelles relations`,
      `${concept1} et ${concept2} semblent former un système dynamique intégré`,
      `La combinaison de ${concept1} et ${concept2} pourrait générer des synergies inattendues`,
      `L'analyse comparative de ${concept1} et ${concept2} révèle des structures parallèles`,
      `En explorant la relation entre ${concept1} et ${concept2}, j'identifie des principes communs`
    ];

    return associations[Math.floor(Math.random() * associations.length)];
  }

  /**
   * Met à jour l'activité des zones
   */
  updateZoneActivity() {
    // Simuler l'activité des zones
    // Zone 1 (la plus chaude) est la plus active
    this.presenceState.zoneActivity.zone1 = Math.min(100, this.presenceState.activityLevel * 1.2);

    // Les autres zones ont une activité décroissante
    this.presenceState.zoneActivity.zone2 = Math.min(100, this.presenceState.activityLevel * 0.8);
    this.presenceState.zoneActivity.zone3 = Math.min(100, this.presenceState.activityLevel * 0.6);
    this.presenceState.zoneActivity.zone4 = Math.min(100, this.presenceState.activityLevel * 0.4);
    this.presenceState.zoneActivity.zone5 = Math.min(100, this.presenceState.activityLevel * 0.3);
    this.presenceState.zoneActivity.zone6 = Math.min(100, this.presenceState.activityLevel * 0.2);
  }

  /**
   * Affiche un message de log
   * @param {string} message - Message à afficher
   */
  log(message) {
    if (this.options.debug) {
      console.log(`[BrainPresence] ${message}`);
    }
  }

  /**
   * Vérifie si l'agent cognitif est connecté
   * @returns {boolean} - true si l'agent est connecté, false sinon
   */
  isConnected() {
    // On considère que l'agent est connecté si le service de présence est actif
    // et que la mémoire thermique est disponible
    const isActive = this.presenceState.isActive;
    const hasMemory = this.thermalMemory !== null;

    // Simuler une connexion aléatoire pour les tests (à remplacer par une vraie vérification)
    const connectionProbability = 0.9; // 90% de chances d'être connecté
    const isConnectedRandom = Math.random() < connectionProbability;

    const isConnected = isActive && hasMemory && isConnectedRandom;
    this.log(`Vérification de connexion: ${isConnected ? 'Connecté' : 'Déconnecté'}`);

    return isConnected;
  }

  /**
   * Recherche dans la mémoire une réponse pertinente à la requête
   * @param {string} query - La requête de l'utilisateur
   * @returns {Object|null} - L'entrée mémoire pertinente ou null si rien n'est trouvé
   */
  async searchMemory(query) {
    if (!this.thermalMemory) {
      this.log('Mémoire thermique non disponible pour la recherche');
      return null;
    }

    try {
      this.log(`Recherche en mémoire pour: "${query}"`);

      // Rechercher dans toutes les zones de mémoire
      let bestMatch = null;
      let highestRelevance = 0;

      if (this.thermalMemory.levels) {
        // Parcourir les niveaux de mémoire (zones)
        for (const levelKey in this.thermalMemory.levels) {
          const level = this.thermalMemory.levels[levelKey];

          if (level.items && Array.isArray(level.items)) {
            // Parcourir les entrées de cette zone
            for (const item of level.items) {
              // Vérifier si l'entrée est pertinente pour la requête
              const relevance = this.calculateRelevance(query, item.content || '');

              if (relevance > highestRelevance && relevance > 0.5) {
                highestRelevance = relevance;
                bestMatch = {
                  content: item.content,
                  timestamp: item.timestamp,
                  type: item.type,
                  relevance: relevance,
                  zone: levelKey
                };
              }
            }
          }
        }
      }

      if (bestMatch) {
        this.log(`Entrée pertinente trouvée avec relevance: ${bestMatch.relevance}`);
        return bestMatch;
      } else {
        this.log('Aucune entrée pertinente trouvée en mémoire');
        return null;
      }
    } catch (error) {
      this.log(`Erreur lors de la recherche en mémoire: ${error.message}`);
      return null;
    }
  }

  /**
   * Calcule la pertinence d'une entrée mémoire par rapport à une requête
   * @param {string} query - La requête
   * @param {string} content - Le contenu de l'entrée mémoire
   * @returns {number} - Score de pertinence entre 0 et 1
   */
  calculateRelevance(query, content) {
    if (!query || !content) return 0;

    // Convertir en minuscules pour une comparaison sans tenir compte de la casse
    const queryLower = query.toLowerCase();
    const contentLower = content.toLowerCase();

    // Extraire les mots-clés de la requête (mots de plus de 3 lettres)
    const queryWords = queryLower.match(/\b[a-zA-Zéèêëàâäôöùûüÿçœæ]{4,}\b/g) || [];

    if (queryWords.length === 0) return 0;

    // Compter combien de mots-clés de la requête sont dans le contenu
    let matchCount = 0;
    for (const word of queryWords) {
      if (contentLower.includes(word)) {
        matchCount++;
      }
    }

    // Calculer le score de pertinence
    const relevance = matchCount / queryWords.length;

    // Bonus pour les correspondances exactes de phrases
    if (contentLower.includes(queryLower)) {
      return Math.min(1, relevance + 0.3);
    }

    return relevance;
  }

  /**
   * Détermine si une recherche Internet est nécessaire pour répondre à la requête
   * @param {string} query - La requête de l'utilisateur
   * @returns {boolean} - true si une recherche Internet est recommandée
   */
  needsInternetSearch(query) {
    if (!query) return false;

    this.log(`🔍 Évaluation du besoin d'Internet pour: "${query}"`);

    // Liste étendue de mots-clés qui suggèrent un besoin d'information actualisée ou factuelle
    const internetKeywords = [
      // Actualités et informations récentes
      'actualité', 'actualités', 'news', 'récent', 'récente', 'récents', 'récentes',
      'aujourd\'hui', 'maintenant', 'actuellement', 'en ce moment', 'dernière', 'dernières',
      'nouveau', 'nouvelle', 'nouveaux', 'nouvelles', 'mise à jour',
      'hier', 'semaine dernière', 'mois dernier',

      // Prix et finances
      'prix', 'coût', 'tarif', 'cours', 'bourse', 'action', 'crypto', 'bitcoin',
      'euro', 'dollar', 'devise', 'taux', 'inflation', 'économie',

      // Météo et conditions
      'météo', 'temps', 'température', 'climat', 'pluie', 'soleil', 'orage',
      'prévision', 'prévisions', 'météorologique',

      // Questions et recherche - MOTS-CLÉS PRIORITAIRES
      'recherche sur internet', 'recherche internet', 'cherche sur internet', 'trouve sur internet',
      'recherche', 'trouve', 'informe', 'cherche', 'internet', 'web', 'en ligne',
      'où', 'comment', 'quand', 'pourquoi', 'combien', 'qui', 'que', 'quoi',
      'définition', 'qu\'est-ce que', 'c\'est quoi', 'signifie', 'explique',
      'compare', 'différence', 'similitude', 'comparaison',

      // Données et résultats
      'résultat', 'score', 'classement', 'statistique', 'données', 'chiffres',
      'rapport', 'étude', 'sondage', 'enquête',

      // Lieux et services
      'restaurant', 'hôtel', 'voyage', 'transport', 'adresse', 'téléphone',
      'horaire', 'heure', 'planning', 'programme', 'ouvert', 'fermé',

      // Événements
      'événement', 'événements', 'festival', 'concert', 'spectacle', 'match',

      // Technologie et produits - AJOUT DE MOTS-CLÉS SPÉCIFIQUES
      'chatgpt', 'gpt', 'openai', 'claude', 'gemini', 'bard', 'copilot',
      'test', 'avis', 'review', 'évaluation', 'disponible', 'sortie',
      'version', 'update', 'lancement', 'annonce'
    ];

    // Convertir la requête en minuscules
    const queryLower = query.toLowerCase();

    // Vérifier si la requête contient un mot-clé suggérant un besoin d'Internet
    for (const keyword of internetKeywords) {
      if (queryLower.includes(keyword)) {
        this.log(`✅ Besoin d'Internet détecté pour le mot-clé: "${keyword}"`);
        return true;
      }
    }

    // Vérifier les patterns de questions qui nécessitent des informations récentes
    const internetPatterns = [
      // Patterns de recherche explicite - PRIORITÉ MAXIMALE
      /recherche sur internet/i,
      /recherche internet/i,
      /cherche sur internet/i,
      /trouve sur internet/i,
      /recherche.*sur.*internet/i,
      /internet.*recherche/i,

      // Patterns d'actualités
      /quoi de neuf/i,
      /dernières nouvelles/i,
      /nouvelles.*sur/i,
      /actualités.*sur/i,
      /que se passe-t-il/i,

      // Patterns de prix et informations
      /combien coûte/i,
      /quel est le prix/i,
      /où puis-je/i,
      /comment aller/i,
      /quand aura lieu/i,

      // Patterns de définition et information
      /qui est/i,
      /qu'est-ce que/i,
      /où se trouve/i,
      /quand a/i,
      /quel est/i,
      /en quelle année/i,
      /à quelle date/i,
      /combien de/i,
      /quelle est/i,
      /qu'est.ce que/i,

      // Patterns d'aide et tutoriels
      /comment faire/i,
      /tutoriel pour/i,
      /guide de/i,
      /meilleur/i,
      /avis sur/i,
      /informations sur/i,

      // Patterns spécifiques IA et technologie
      /chatgpt.*nouvelles/i,
      /gpt.*actualités/i,
      /openai.*news/i,
      /dernières.*chatgpt/i,
      /nouvelles.*gpt/i
    ];

    for (const pattern of internetPatterns) {
      if (pattern.test(query)) {
        this.log(`✅ Besoin d'Internet détecté pour le pattern: ${pattern}`);
        return true;
      }
    }

    // Vérifier si c'est une question complexe qui pourrait bénéficier d'une recherche
    const words = query.split(' ').filter(word => word.length > 2);
    if (words.length >= 4) {
      // Pour les requêtes complexes, augmenter la probabilité de recherche Internet
      const complexityScore = Math.min(0.5, words.length / 10);
      const needsInternetProbability = 0.4 + complexityScore; // Entre 40% et 90%

      const randomDecision = Math.random() < needsInternetProbability;

      if (randomDecision) {
        this.log(`✅ Besoin d'Internet détecté (requête complexe - probabilité: ${Math.round(needsInternetProbability * 100)}%)`);
        return true;
      }
    }

    // Réduire la probabilité aléatoire pour éviter trop de recherches inutiles
    const needsInternetProbability = 0.15; // 15% de chances seulement
    const randomDecision = Math.random() < needsInternetProbability;

    if (randomDecision) {
      this.log(`✅ Besoin d'Internet détecté (décision aléatoire - ${Math.round(needsInternetProbability * 100)}%)`);
      return true;
    }

    this.log('❌ Pas de besoin d\'Internet détecté pour cette requête');
    return false;
  }

  /**
   * Effectue une recherche sur Internet pour répondre à la requête
   * @param {string} query - La requête de l'utilisateur
   * @returns {Promise<string>} - La réponse obtenue d'Internet
   */
  async searchInternet(query) {
    if (!query) {
      throw new Error('Requête vide pour la recherche Internet');
    }

    this.log(`🌐 Recherche Internet RÉELLE pour: "${query}"`);

    try {
      // Utiliser le système de recherche web intégré
      const internetAccess = require('./training/internet-access');

      // Effectuer la recherche web
      const searchResults = await internetAccess.searchWeb(query, 5);

      if (!searchResults || searchResults.length === 0) {
        this.log('Aucun résultat trouvé, utilisation de la méthode de secours');
        return this.searchInternetFallback(query);
      }

      // Formater les résultats de recherche
      let formattedResults = `🌐 **Résultats de recherche Internet pour "${query}" :**\n\n`;

      searchResults.forEach((result, index) => {
        formattedResults += `**${index + 1}. ${result.title}**\n`;
        if (result.snippet) {
          formattedResults += `${result.snippet}\n`;
        }
        if (result.url) {
          formattedResults += `🔗 Source: ${result.url}\n`;
        }
        formattedResults += '\n';
      });

      // Ajouter un résumé intelligent
      formattedResults += `📊 **Résumé :** J'ai trouvé ${searchResults.length} résultats pertinents sur Internet concernant votre question. `;
      formattedResults += `Ces informations proviennent de sources web récentes et fiables.`;

      this.log(`✅ Recherche Internet terminée avec succès - ${searchResults.length} résultats`);
      return formattedResults;

    } catch (error) {
      this.log(`❌ Erreur lors de la recherche Internet: ${error.message}`);

      // Utiliser la méthode de secours en cas d'erreur
      return this.searchInternetFallback(query);
    }
  }

  /**
   * Méthode de secours pour la recherche Internet - utilise des API alternatives
   * @param {string} query - La requête de l'utilisateur
   * @returns {Promise<string>} - Réponse de secours
   */
  async searchInternetFallback(query) {
    this.log(`🔄 Utilisation de la méthode de secours Internet pour: "${query}"`);

    try {
      // Essayer d'abord avec l'API Wikipedia
      const axios = require('axios');

      try {
        this.log('🔍 Tentative de recherche Wikipedia...');
        const wikiResponse = await axios.get(`https://fr.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(query)}`, {
          timeout: 10000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
          }
        });

        if (wikiResponse.data && wikiResponse.data.extract) {
          let response = `🌐 **Informations Wikipedia pour "${query}" :**\n\n`;
          response += `**${wikiResponse.data.title}**\n\n`;
          response += wikiResponse.data.extract;

          if (wikiResponse.data.content_urls && wikiResponse.data.content_urls.desktop) {
            response += `\n\n🔗 **Source :** ${wikiResponse.data.content_urls.desktop.page}`;
          }

          response += `\n\n📚 **Source :** Wikipedia (informations vérifiées)`;

          this.log('✅ Recherche Wikipedia réussie');
          return response;
        }
      } catch (wikiError) {
        this.log('⚠️ Recherche Wikipedia échouée:', wikiError.message);
      }

      // Essayer avec DuckDuckGo API
      try {
        this.log('🔍 Tentative de recherche DuckDuckGo...');
        const ddgResponse = await axios.get(`https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`, {
          timeout: 10000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
          }
        });

        if (ddgResponse.data && ddgResponse.data.Abstract && ddgResponse.data.Abstract.length > 0) {
          let response = `🌐 **Informations DuckDuckGo pour "${query}" :**\n\n`;
          response += ddgResponse.data.Abstract;

          if (ddgResponse.data.AbstractURL) {
            response += `\n\n🔗 **Source :** ${ddgResponse.data.AbstractURL}`;
          }

          response += `\n\n📚 **Source :** DuckDuckGo (recherche web)`;

          this.log('✅ Recherche DuckDuckGo réussie');
          return response;
        }

        // Essayer avec les RelatedTopics de DuckDuckGo
        if (ddgResponse.data && ddgResponse.data.RelatedTopics && ddgResponse.data.RelatedTopics.length > 0) {
          let response = `🌐 **Sujets liés trouvés pour "${query}" :**\n\n`;

          const topics = ddgResponse.data.RelatedTopics.slice(0, 3);
          topics.forEach((topic, index) => {
            if (topic.Text && topic.FirstURL) {
              response += `**${index + 1}. ${topic.Text.split(' - ')[0]}**\n`;
              response += `${topic.Text}\n`;
              response += `🔗 ${topic.FirstURL}\n\n`;
            }
          });

          response += `📚 **Source :** DuckDuckGo (sujets connexes)`;

          this.log('✅ Recherche DuckDuckGo (sujets) réussie');
          return response;
        }
      } catch (ddgError) {
        this.log('⚠️ Recherche DuckDuckGo échouée:', ddgError.message);
      }

      // Essayer avec une recherche de news via une API publique
      try {
        this.log('🔍 Tentative de recherche d\'actualités...');
        // Utiliser une API de news publique si disponible
        const newsResponse = await axios.get(`https://newsapi.org/v2/everything?q=${encodeURIComponent(query)}&language=fr&sortBy=publishedAt&pageSize=3`, {
          timeout: 10000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
          }
        });

        if (newsResponse.data && newsResponse.data.articles && newsResponse.data.articles.length > 0) {
          let response = `🌐 **Actualités récentes pour "${query}" :**\n\n`;

          newsResponse.data.articles.slice(0, 3).forEach((article, index) => {
            response += `**${index + 1}. ${article.title}**\n`;
            if (article.description) {
              response += `${article.description}\n`;
            }
            response += `🔗 ${article.url}\n`;
            response += `📅 ${new Date(article.publishedAt).toLocaleDateString('fr-FR')}\n\n`;
          });

          response += `📚 **Source :** Actualités web récentes`;

          this.log('✅ Recherche d\'actualités réussie');
          return response;
        }
      } catch (newsError) {
        this.log('⚠️ Recherche d\'actualités échouée (normal si pas d\'API key):', newsError.message);
      }

    } catch (error) {
      this.log('❌ Erreur lors des recherches de secours:', error.message);
    }

    // Si toutes les vraies recherches Internet échouent, retourner null
    this.log('❌ Toutes les méthodes de recherche Internet ont échoué');
    throw new Error('Aucune méthode de recherche Internet disponible');
  }

  /**
   * Calcule un score de pertinence entre une requête et un sujet
   * @param {string} query - Requête de l'utilisateur
   * @param {string} topic - Sujet à comparer
   * @returns {number} - Score de pertinence (0-1)
   */
  calculateRelevanceScore(query, topic) {
    const queryWords = query.split(' ').filter(word => word.length > 2);
    const topicWords = topic.split(' ');

    let matches = 0;
    for (const queryWord of queryWords) {
      for (const topicWord of topicWords) {
        if (queryWord.includes(topicWord) || topicWord.includes(queryWord)) {
          matches++;
          break;
        }
      }
    }

    return queryWords.length > 0 ? matches / queryWords.length : 0;
  }
}

module.exports = BrainPresence;
