/**
 * Intégration entre Ollama et la mémoire thermique
 *
 * Ce module permet d'enrichir les prompts envoyés à Ollama avec des informations
 * pertinentes de la mémoire thermique et de stocker les échanges dans la mémoire.
 */

const axios = require('axios');
const { getThermalMemory } = require('./thermal_memory');

/**
 * Classe OllamaIntegration - Gère l'intégration entre Ollama et la mémoire thermique
 */
class OllamaIntegration {
  /**
   * Initialise l'intégration Ollama-Mémoire thermique
   * @param {Object} config - Configuration de l'intégration
   */
  constructor(config = {}) {
    this.config = {
      ollamaApiUrl: config.ollamaApiUrl || 'http://localhost:11434/api',
      useMemory: config.useMemory !== undefined ? config.useMemory : true,
      maxContextItems: config.maxContextItems || 5,
      memoryImportance: config.memoryImportance || 0.7
    };

    // Initialiser la mémoire thermique
    if (this.config.useMemory) {
      this.thermalMemory = getThermalMemory();
      console.log('Mémoire thermique connectée à l\'intégration Ollama');
    } else {
      this.thermalMemory = null;
      console.log('Intégration Ollama sans mémoire thermique');
    }

    // Statistiques
    this.stats = {
      requests: 0,
      successfulRequests: 0,
      memoryEnrichedRequests: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    };
  }

  /**
   * Vérifie si Ollama est disponible
   * @returns {Promise<boolean>} - True si Ollama est disponible
   */
  async isOllamaAvailable() {
    try {
      await axios.get(`${this.config.ollamaApiUrl}/version`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la vérification de la disponibilité d\'Ollama:', error.message);
      return false;
    }
  }

  /**
   * Appelle l'API Ollama avec un prompt enrichi
   * @param {string} prompt - Prompt à envoyer à Ollama
   * @param {Array} history - Historique de la conversation
   * @param {string} modelName - Nom du modèle à utiliser
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Object>} - Réponse d'Ollama
   */
  async callOllamaApi(prompt, history = [], modelName = 'deepseek-r1:7b', options = {}) {
    try {
      const startTime = Date.now();
      this.stats.requests++;

      // Vérifier si Ollama est disponible
      if (!await this.isOllamaAvailable()) {
        return { error: 'Ollama n\'est pas disponible' };
      }

      // Enrichir le prompt avec la mémoire thermique si demandé
      let enhancedPrompt = prompt;
      let memoryData = [];

      if (options.useMemory && this.thermalMemory) {
        try {
          // Récupérer les souvenirs pertinents
          memoryData = await this.getRelevantMemories(prompt, options.memoryLimit || 5);

          if (memoryData.length > 0) {
            const memoryContext = memoryData.map(memory =>
              `[Mémoire ${memory.zone}] ${memory.content}`
            ).join('\n');

            enhancedPrompt = `Contexte de mémoire:\n${memoryContext}\n\nQuestion: ${prompt}`;
          }
        } catch (error) {
          console.warn('Erreur lors de la récupération de la mémoire:', error.message);
        }
      }

      // Préparer la requête pour Ollama
      const requestData = {
        model: modelName,
        prompt: enhancedPrompt,
        stream: false,
        options: {
          temperature: options.temperature || 0.7,
          top_p: options.top_p || 0.9,
          top_k: options.top_k || 40,
          num_predict: options.max_tokens || 2048
        }
      };

      // Ajouter l'historique de conversation si fourni
      if (history && history.length > 0) {
        requestData.context = this.formatConversationHistory(history);
      }

      // Appeler l'API Ollama
      const response = await fetch(`${this.config.ollamaUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`Ollama API Error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      // Mettre à jour les statistiques
      this.stats.successfulRequests++;
      this.stats.totalTokens += result.eval_count || 0;
      this.stats.averageResponseTime = (this.stats.averageResponseTime + (Date.now() - startTime)) / 2;

      // Stocker la réponse en mémoire si demandé
      if (options.storeInMemory && this.thermalMemory) {
        await this.storeResponseInMemory(prompt, result.response, memoryData);
      }

      return {
        success: true,
        response: result.response,
        model: modelName,
        tokens_used: result.eval_count || 0,
        response_time: Date.now() - startTime,
        memory_used: memoryData.length > 0,
        context_length: enhancedPrompt.length
      };
    } catch (error) {
      console.error('Erreur lors de l\'appel à l\'API Ollama:', error.message);
      this.stats.failedRequests++;

      return {
        success: false,
        error: `Erreur de communication avec Ollama: ${error.message}`,
        response_time: Date.now() - startTime
      };
    }
  }

  /**
   * Récupère les souvenirs pertinents de la mémoire thermique
   */
  async getRelevantMemories(prompt, limit = 5) {
    if (!this.thermalMemory) return [];

    try {
      // Rechercher dans la mémoire thermique
      const memories = this.thermalMemory.search(prompt, { limit, minTemperature: 0.3 });

      return memories.map(memory => ({
        content: memory.data || memory.content,
        zone: memory.zone || 'unknown',
        temperature: memory.temperature || 0,
        timestamp: memory.timestamp
      }));
    } catch (error) {
      console.warn('Erreur lors de la récupération des souvenirs:', error.message);
      return [];
    }
  }

  /**
   * Stocke une réponse dans la mémoire thermique
   */
  async storeResponseInMemory(prompt, response, contextMemories = []) {
    if (!this.thermalMemory) return;

    try {
      const memoryEntry = {
        type: 'conversation',
        prompt: prompt,
        response: response,
        timestamp: new Date().toISOString(),
        context_used: contextMemories.length > 0,
        context_count: contextMemories.length
      };

      // Calculer l'importance basée sur la longueur et le contexte
      const importance = Math.min(1.0,
        0.5 +
        (response.length / 1000) * 0.3 +
        (contextMemories.length > 0 ? 0.2 : 0)
      );

      this.thermalMemory.addEntry({
        key: `ollama_conversation_${Date.now()}`,
        data: memoryEntry,
        importance: importance,
        category: 'ai_conversation'
      });

      this.stats.memoriesStored++;
    } catch (error) {
      console.warn('Erreur lors du stockage en mémoire:', error.message);
    }
  }

  /**
   * Formate l'historique de conversation pour Ollama
   */
  formatConversationHistory(history) {
    return history.map(entry => {
      if (typeof entry === 'string') {
        return { role: 'user', content: entry };
      }
      return entry;
    }).slice(-10); // Garder seulement les 10 derniers échanges
  }

  /**
   * Obtient une réponse simple à un prompt
   * @param {string} prompt - Prompt à envoyer à Ollama
   * @param {string} modelName - Nom du modèle à utiliser
   * @returns {Promise<string>} - Réponse d'Ollama
   */
  async getResponse(prompt, modelName = 'deepseek-r1:7b') {
    const response = await this.callOllamaApi(prompt, [], modelName);

    if (response.error) {
      return `Erreur: ${response.error}`;
    }

    return response.message ? response.message.content : 'Pas de réponse';
  }

  /**
   * Génère un état du cerveau pour la visualisation
   * @returns {Object} - État du cerveau
   */
  getBrainState() {
    // Obtenir les statistiques de la mémoire thermique
    const memoryStats = this.thermalMemory ? this.thermalMemory.getStats() : null;

    // Obtenir les informations CPU
    const cpuInfo = this.getCpuInfo();

    // Calculer les activités basées sur les statistiques de mémoire et CPU
    const frontalActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.instantEntries * 5 : 0) +
      (cpuInfo.usage * 0.5) +
      50
    ));

    const parietalActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.shortTermEntries * 3 : 0) +
      (cpuInfo.usage * 0.3) +
      45
    ));

    const temporalActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.workingMemoryEntries * 2 : 0) +
      (cpuInfo.usage * 0.4) +
      55
    ));

    const occipitalActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.mediumTermEntries * 1 : 0) +
      (cpuInfo.usage * 0.6) +
      40
    ));

    const limbicActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.longTermEntries * 0.5 : 0) +
      (cpuInfo.usage * 0.2) +
      60
    ));

    const creativeActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.dreamMemoryEntries * 4 : 0) +
      (cpuInfo.temperature * 5) +
      65
    ));

    // Calculer l'activité globale comme moyenne pondérée
    const globalActivity = Math.round(
      (frontalActivity + parietalActivity + temporalActivity +
       occipitalActivity + limbicActivity + creativeActivity) / 6
    );

    // Calculer les températures basées sur les statistiques CPU
    const baseTemp = cpuInfo.temperature || 36.5;

    // Calculer le score d'évolution basé sur les statistiques de mémoire
    const evolutionScore = memoryStats ?
      Math.round((60 +
        (memoryStats.totalEntries / 10) +
        (memoryStats.averageTemperature * 20) +
        (memoryStats.cyclesPerformed * 0.5)
      ) * 10) / 10 : 70;

    return {
      zones: {
        frontal: {
          activity: frontalActivity,
          temperature: Math.round((baseTemp + 0.2) * 10) / 10,
          entries: memoryStats ? memoryStats.instantEntries : 0
        },
        parietal: {
          activity: parietalActivity,
          temperature: Math.round((baseTemp - 0.1) * 10) / 10,
          entries: memoryStats ? memoryStats.shortTermEntries : 0
        },
        temporal: {
          activity: temporalActivity,
          temperature: Math.round((baseTemp + 0.3) * 10) / 10,
          entries: memoryStats ? memoryStats.workingMemoryEntries : 0
        },
        occipital: {
          activity: occipitalActivity,
          temperature: Math.round((baseTemp - 0.2) * 10) / 10,
          entries: memoryStats ? memoryStats.mediumTermEntries : 0
        },
        limbic: {
          activity: limbicActivity,
          temperature: Math.round((baseTemp + 0.1) * 10) / 10,
          entries: memoryStats ? memoryStats.longTermEntries : 0
        },
        creative: {
          activity: creativeActivity,
          temperature: Math.round((baseTemp + 0.4) * 10) / 10,
          entries: memoryStats ? memoryStats.dreamMemoryEntries : 0
        }
      },
      globalActivity,
      globalTemperature: Math.round(baseTemp * 10) / 10,
      evolutionScore,
      memoryStats,
      cpuInfo,
      kyber: this.thermalMemory ? this.thermalMemory.getKyberState() : null
    };
  }

  /**
   * Obtient les informations CPU
   * @returns {Object} - Informations CPU
   */
  getCpuInfo() {
    try {
      // Simuler les informations CPU (dans une vraie implémentation, on utiliserait os-utils ou systeminformation)
      const usage = Math.min(100, Math.round(50 + Math.random() * 30));
      const temperature = Math.round((36.5 + (usage / 100) * 3) * 10) / 10;
      const cores = 8;
      const memory = {
        total: 16384, // MB
        free: Math.round(4096 + Math.random() * 2048), // MB
        used: Math.round(8192 + Math.random() * 2048) // MB
      };

      return {
        usage,
        temperature,
        cores,
        memory
      };
    } catch (error) {
      console.error('Error getting CPU info:', error);
      return {
        usage: 50,
        temperature: 36.5,
        cores: 8,
        memory: {
          total: 16384,
          free: 8192,
          used: 8192
        }
      };
    }
  }

  /**
   * Retourne les statistiques de l'intégration
   * @returns {Object} - Statistiques de l'intégration
   */
  getStats() {
    return {
      ...this.stats,
      memoryStats: this.thermalMemory ? this.thermalMemory.getStats() : null
    };
  }
}

// Instance unique de l'intégration Ollama
let ollamaIntegrationInstance = null;

/**
 * Obtient l'instance unique de l'intégration Ollama
 * @param {Object} config - Configuration de l'intégration
 * @returns {OllamaIntegration} - Instance de l'intégration Ollama
 */
function getOllamaIntegration(config = {}) {
  if (!ollamaIntegrationInstance) {
    ollamaIntegrationInstance = new OllamaIntegration(config);
  }
  return ollamaIntegrationInstance;
}

module.exports = {
  OllamaIntegration,
  getOllamaIntegration
};
