/**
 * <PERSON><PERSON><PERSON> d'accès Internet pour la formation
 * Permet à l'assistant d'accéder à Internet pendant la formation pour obtenir des informations à jour
 */

const axios = require('axios');
const cheerio = require('cheerio');
const { JSDOM } = require('jsdom');
const puppeteer = require('puppeteer');

// Cache pour éviter de refaire les mêmes requêtes
const requestCache = new Map();
const CACHE_EXPIRY = 3600000; // 1 heure en millisecondes

// Détection des sujets de programmation
const PROGRAMMING_KEYWORDS = [
  'code', 'programming', 'javascript', 'python', 'java', 'c++', 'c#', 'ruby', 'php', 'html', 'css',
  'sql', 'database', 'api', 'framework', 'library', 'git', 'github', 'npm', 'yarn', 'webpack',
  'react', 'angular', 'vue', 'node', 'express', 'django', 'flask', 'spring', 'laravel', 'rails',
  'algorithm', 'data structure', 'function', 'class', 'object', 'variable', 'loop', 'condition',
  'compiler', 'interpreter', 'runtime', 'debug', 'error', 'exception', 'try catch', 'async', 'await',
  'promise', 'callback', 'event', 'listener', 'dom', 'json', 'xml', 'http', 'rest', 'graphql',
  'développement', 'programmation', 'algorithme', 'fonction', 'classe', 'objet', 'variable', 'boucle',
  'condition', 'erreur', 'exception', 'événement', 'écouteur', 'requête', 'serveur', 'client'
];

/**
 * Effectue une recherche sur Internet en utilisant une méthode alternative
 * @param {string} query - La requête de recherche
 * @param {number} limit - Nombre de résultats à retourner
 * @returns {Promise<Array>} - Les résultats de la recherche
 */
async function searchWeb(query, limit = 5) {
  try {
    console.log(`Recherche web pour la formation: "${query}"`);

    // Vérifier le cache
    const cacheKey = `search:${query}:${limit}`;
    if (requestCache.has(cacheKey)) {
      const cachedData = requestCache.get(cacheKey);
      if (Date.now() - cachedData.timestamp < CACHE_EXPIRY) {
        console.log(`Utilisation des résultats en cache pour: "${query}"`);
        return cachedData.data;
      }
    }

    // Méthode alternative: utiliser une recherche directe avec puppeteer
    // Cette méthode est plus fiable que l'API DuckDuckGo
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Utiliser Bing pour la recherche
    await page.goto(`https://www.bing.com/search?q=${encodeURIComponent(query)}`);

    // Attendre que les résultats se chargent
    await page.waitForSelector('.b_algo', { timeout: 5000 });

    // Extraire les résultats
    const results = await page.evaluate((resultLimit) => {
      const searchResults = [];
      const resultElements = document.querySelectorAll('.b_algo');

      for (let i = 0; i < Math.min(resultElements.length, resultLimit); i++) {
        const element = resultElements[i];
        const titleElement = element.querySelector('h2 a');
        const snippetElement = element.querySelector('.b_caption p');

        if (titleElement) {
          searchResults.push({
            title: titleElement.textContent.trim(),
            url: titleElement.href,
            snippet: snippetElement ? snippetElement.textContent.trim() : ''
          });
        }
      }

      return searchResults;
    }, limit);

    await browser.close();

    console.log(`Recherche web terminée avec ${results.length} résultats pour: "${query}"`);

    // Mettre en cache les résultats
    requestCache.set(cacheKey, {
      timestamp: Date.now(),
      data: results
    });

    return results;
  } catch (error) {
    console.error('Erreur lors de la recherche web:', error);
    // En cas d'erreur, essayer une méthode de secours
    return searchWebFallback(query, limit);
  }
}

/**
 * Méthode de secours pour la recherche web - utilise DuckDuckGo API
 * @param {string} query - La requête de recherche
 * @param {number} limit - Nombre de résultats à retourner
 * @returns {Promise<Array>} - Les résultats de la recherche
 */
async function searchWebFallback(query, limit = 5) {
  try {
    console.log(`Utilisation de la méthode de secours DuckDuckGo pour la recherche: "${query}"`);

    // Essayer d'abord avec l'API DuckDuckGo
    try {
      const response = await axios.get(`https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }
      });

      if (response.data && response.data.RelatedTopics && response.data.RelatedTopics.length > 0) {
        const results = response.data.RelatedTopics
          .filter(topic => topic.Text && topic.FirstURL)
          .slice(0, limit)
          .map(topic => ({
            title: topic.Text.split(' - ')[0] || topic.Text.substring(0, 100),
            url: topic.FirstURL,
            snippet: topic.Text
          }));

        if (results.length > 0) {
          console.log(`✅ DuckDuckGo API a retourné ${results.length} résultats`);
          return results;
        }
      }
    } catch (apiError) {
      console.log('⚠️ API DuckDuckGo échouée, tentative avec Google Custom Search');
    }

    // Méthode de secours alternative : utiliser Google avec Puppeteer
    try {
      const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const page = await browser.newPage();

      // Utiliser Google comme alternative
      await page.goto(`https://www.google.com/search?q=${encodeURIComponent(query)}`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });

      // Attendre que les résultats se chargent
      await page.waitForSelector('.g', { timeout: 5000 });

      // Extraire les résultats
      const results = await page.evaluate((resultLimit) => {
        const searchResults = [];
        const resultElements = document.querySelectorAll('.g');

        for (let i = 0; i < Math.min(resultElements.length, resultLimit); i++) {
          const element = resultElements[i];
          const titleElement = element.querySelector('h3');
          const linkElement = element.querySelector('a[href]');
          const snippetElement = element.querySelector('.VwiC3b, .s3v9rd, .st');

          if (titleElement && linkElement) {
            searchResults.push({
              title: titleElement.textContent.trim(),
              url: linkElement.href,
              snippet: snippetElement ? snippetElement.textContent.trim() : ''
            });
          }
        }

        return searchResults;
      }, limit);

      await browser.close();

      if (results.length > 0) {
        console.log(`✅ Google search a retourné ${results.length} résultats`);
        return results;
      }
    } catch (googleError) {
      console.log('⚠️ Google search échouée également');
    }

    // Dernière méthode de secours : recherche Wikipedia
    try {
      const wikiResponse = await axios.get(`https://fr.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(query)}`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }
      });

      if (wikiResponse.data && wikiResponse.data.extract) {
        console.log(`✅ Wikipedia a retourné des informations pour: "${query}"`);
        return [{
          title: wikiResponse.data.title || query,
          url: wikiResponse.data.content_urls ? wikiResponse.data.content_urls.desktop.page : `https://fr.wikipedia.org/wiki/${encodeURIComponent(query)}`,
          snippet: wikiResponse.data.extract
        }];
      }
    } catch (wikiError) {
      console.log('⚠️ Wikipedia search échouée également');
    }

    // Si tout échoue, retourner un résultat générique mais réel
    console.log('⚠️ Toutes les méthodes de recherche ont échoué, retour d\'un lien de recherche direct');
    return [{
      title: `Recherche pour: ${query}`,
      url: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
      snippet: `Cliquez pour effectuer une recherche Google pour "${query}". Toutes les méthodes automatiques de recherche ont temporairement échoué.`
    }];

  } catch (error) {
    console.error('Erreur lors de la recherche web de secours:', error);
    return [{
      title: `Recherche pour: ${query}`,
      url: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
      snippet: `Erreur lors de la recherche automatique. Cliquez pour effectuer une recherche manuelle.`
    }];
  }
}

/**
 * Récupère le contenu d'une page web de manière plus fiable en utilisant Puppeteer
 * @param {string} url - L'URL de la page à récupérer
 * @param {string} format - Le format de sortie ('text', 'html', 'markdown')
 * @returns {Promise<string>} - Le contenu de la page
 */
async function fetchWebPage(url, format = 'text') {
  try {
    console.log(`Récupération de la page web: ${url}`);

    // Vérifier le cache
    const cacheKey = `fetch:${url}:${format}`;
    if (requestCache.has(cacheKey)) {
      const cachedData = requestCache.get(cacheKey);
      if (Date.now() - cachedData.timestamp < CACHE_EXPIRY) {
        console.log(`Utilisation du contenu en cache pour: ${url}`);
        return cachedData.data;
      }
    }

    // Utiliser Puppeteer pour récupérer le contenu de la page
    // Cette méthode est plus fiable car elle exécute JavaScript et rend la page complètement
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Définir un timeout raisonnable
    await page.setDefaultNavigationTimeout(10000);

    // Naviguer vers l'URL
    await page.goto(url, { waitUntil: 'networkidle2' });

    let content = '';

    if (format === 'html') {
      // Récupérer le HTML complet
      content = await page.content();
    } else if (format === 'text') {
      // Extraire le texte principal en évitant les menus, footers, etc.
      content = await page.evaluate(() => {
        // Fonction pour déterminer si un élément est probablement du contenu principal
        const isMainContent = (element) => {
          const tagName = element.tagName.toLowerCase();
          // Ignorer les éléments qui sont généralement des menus, footers, etc.
          if (['nav', 'footer', 'header', 'aside'].includes(tagName)) {
            return false;
          }

          // Vérifier les attributs et classes qui indiquent du contenu principal
          const classAndId = (element.className + ' ' + element.id).toLowerCase();
          if (classAndId.includes('menu') ||
              classAndId.includes('nav') ||
              classAndId.includes('footer') ||
              classAndId.includes('header') ||
              classAndId.includes('sidebar')) {
            return false;
          }

          return true;
        };

        // Trouver les éléments qui contiennent probablement le contenu principal
        const mainElements = Array.from(document.querySelectorAll('article, main, .content, #content, .main, #main, .article, #article'));

        // Si des éléments de contenu principal sont trouvés, extraire leur texte
        if (mainElements.length > 0) {
          return mainElements
            .map(el => el.textContent)
            .join('\n\n')
            .replace(/\s+/g, ' ')
            .trim();
        }

        // Sinon, extraire le texte de tous les paragraphes et titres
        const contentElements = Array.from(document.querySelectorAll('p, h1, h2, h3, h4, h5, h6'))
          .filter(isMainContent);

        return contentElements
          .map(el => el.textContent)
          .join('\n\n')
          .replace(/\s+/g, ' ')
          .trim();
      });
    } else if (format === 'markdown') {
      // Extraire le contenu et le convertir en markdown
      content = await page.evaluate(() => {
        const title = document.title;
        let markdown = `# ${title}\n\n`;

        // Fonction pour déterminer si un élément est probablement du contenu principal
        const isMainContent = (element) => {
          const tagName = element.tagName.toLowerCase();
          if (['nav', 'footer', 'header', 'aside'].includes(tagName)) {
            return false;
          }

          const classAndId = (element.className + ' ' + element.id).toLowerCase();
          if (classAndId.includes('menu') ||
              classAndId.includes('nav') ||
              classAndId.includes('footer') ||
              classAndId.includes('header') ||
              classAndId.includes('sidebar')) {
            return false;
          }

          return true;
        };

        // Extraire les titres et paragraphes
        const elements = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6, p'))
          .filter(isMainContent);

        elements.forEach(el => {
          const tagName = el.tagName.toLowerCase();
          const text = el.textContent.trim();

          if (text) {
            if (tagName.startsWith('h')) {
              const level = parseInt(tagName.substring(1));
              markdown += `${'#'.repeat(level)} ${text}\n\n`;
            } else if (tagName === 'p') {
              markdown += `${text}\n\n`;
            }
          }
        });

        return markdown;
      });
    }

    await browser.close();

    // Limiter la taille du contenu pour éviter des problèmes de mémoire
    if (content.length > 5000) {
      content = content.substring(0, 5000) + '...';
    }

    // Mettre en cache le contenu
    requestCache.set(cacheKey, {
      timestamp: Date.now(),
      data: content
    });

    return content;
  } catch (error) {
    console.error(`Erreur lors de la récupération de la page web ${url}:`, error);
    // En cas d'erreur, essayer la méthode de secours
    return fetchWebPageFallback(url, format);
  }
}

/**
 * Méthode de secours pour récupérer le contenu d'une page web
 * @param {string} url - L'URL de la page à récupérer
 * @param {string} format - Le format de sortie ('text', 'html', 'markdown')
 * @returns {Promise<string>} - Le contenu de la page
 */
async function fetchWebPageFallback(url, format = 'text') {
  try {
    console.log(`Utilisation de la méthode de secours pour récupérer la page: ${url}`);

    // Utiliser axios comme méthode de secours
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 5000
    });

    let content = '';

    if (format === 'html') {
      content = response.data;
    } else if (format === 'text' || format === 'markdown') {
      // Extraire le texte avec Cheerio
      const $ = cheerio.load(response.data);

      // Supprimer les scripts, styles et autres éléments non pertinents
      $('script, style, meta, link, noscript, nav, footer, header, aside').remove();

      if (format === 'text') {
        // Extraire le texte du corps
        content = $('body').text()
          .replace(/\s+/g, ' ')
          .trim();
      } else {
        // Format markdown
        const title = $('title').text();
        content = `# ${title}\n\n`;

        // Extraire les sous-titres et paragraphes
        $('h1, h2, h3, h4, h5, h6, p').each((i, el) => {
          const tagName = $(el).prop('tagName').toLowerCase();
          const text = $(el).text().trim();

          if (text) {
            if (tagName.startsWith('h')) {
              const level = parseInt(tagName.substring(1));
              content += `${'#'.repeat(level)} ${text}\n\n`;
            } else if (tagName === 'p') {
              content += `${text}\n\n`;
            }
          }
        });
      }
    }

    // Limiter la taille du contenu
    if (content.length > 5000) {
      content = content.substring(0, 5000) + '...';
    }

    return content;
  } catch (error) {
    console.error(`Erreur lors de la récupération de secours de la page web ${url}:`, error);
    return `Impossible de récupérer le contenu de ${url}. Veuillez visiter directement le site pour plus d'informations.`;
  }
}

/**
 * Récupère une vidéo YouTube et extrait les informations pertinentes
 * @param {string} query - La requête de recherche
 * @returns {Promise<Object>} - Les informations sur la vidéo
 */
async function searchYouTubeVideo(query) {
  try {
    console.log(`Recherche de vidéo YouTube pour: "${query}"`);

    // Vérifier le cache
    const cacheKey = `youtube:${query}`;
    if (requestCache.has(cacheKey)) {
      const cachedData = requestCache.get(cacheKey);
      if (Date.now() - cachedData.timestamp < CACHE_EXPIRY) {
        console.log(`Utilisation des résultats YouTube en cache pour: "${query}"`);
        return cachedData.data;
      }
    }

    // Lancer un navigateur headless pour accéder à YouTube
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Définir un timeout raisonnable
    await page.setDefaultNavigationTimeout(15000);

    // Naviguer vers YouTube avec la requête de recherche
    await page.goto(`https://www.youtube.com/results?search_query=${encodeURIComponent(query)}`, {
      waitUntil: 'networkidle2'
    });

    // Attendre que les résultats se chargent
    try {
      await page.waitForSelector('ytd-video-renderer, ytd-compact-video-renderer', { timeout: 10000 });
    } catch (error) {
      console.log('Timeout en attendant les résultats YouTube, tentative de continuer quand même');
    }

    // Extraire les informations des 3 premières vidéos
    const videoInfos = await page.evaluate(() => {
      const videoElements = document.querySelectorAll('ytd-video-renderer, ytd-compact-video-renderer');
      if (!videoElements || videoElements.length === 0) return [];

      // Limiter à 3 vidéos maximum
      const maxVideos = Math.min(3, videoElements.length);
      const results = [];

      for (let i = 0; i < maxVideos; i++) {
        const videoElement = videoElements[i];

        const titleElement = videoElement.querySelector('#video-title, .title');
        const channelElement = videoElement.querySelector('.ytd-channel-name a, #channel-name a, #channel-name');
        const viewsElement = videoElement.querySelector('#metadata-line span:first-child, .metadata-line span:first-child');
        const timeElement = videoElement.querySelector('#metadata-line span:nth-child(2), .metadata-line span:nth-child(2)');
        const linkElement = videoElement.querySelector('a#thumbnail, a.thumbnail');
        const thumbnailElement = videoElement.querySelector('img#img, img.yt-img-shadow');

        // Extraire l'ID de la vidéo à partir de l'URL
        let videoId = '';
        if (linkElement && linkElement.href) {
          const match = linkElement.href.match(/[?&]v=([^&]+)/);
          if (match) videoId = match[1];
        }

        results.push({
          title: titleElement ? titleElement.textContent.trim() : 'Vidéo sans titre',
          channel: channelElement ? channelElement.textContent.trim() : 'Chaîne inconnue',
          views: viewsElement ? viewsElement.textContent.trim() : 'Vues inconnues',
          time: timeElement ? timeElement.textContent.trim() : 'Durée inconnue',
          url: videoId ? `https://www.youtube.com/watch?v=${videoId}` : '',
          thumbnailUrl: thumbnailElement && thumbnailElement.src ? thumbnailElement.src : '',
          videoId: videoId
        });
      }

      return results;
    });

    await browser.close();

    // Si aucune vidéo n'est trouvée, essayer la méthode de secours
    if (!videoInfos || videoInfos.length === 0) {
      console.log('Aucune vidéo trouvée, utilisation de la méthode de secours');
      return searchYouTubeVideoFallback(query);
    }

    // Mettre en cache les résultats
    requestCache.set(cacheKey, {
      timestamp: Date.now(),
      data: videoInfos[0] // Retourner la première vidéo comme avant
    });

    return videoInfos[0];
  } catch (error) {
    console.error('Erreur lors de la recherche YouTube:', error);
    return searchYouTubeVideoFallback(query);
  }
}

/**
 * Méthode de secours pour la recherche de vidéos YouTube
 * @param {string} query - La requête de recherche
 * @returns {Promise<Object>} - Les informations sur la vidéo
 */
async function searchYouTubeVideoFallback(query) {
  try {
    console.log(`Utilisation de la méthode de secours pour la recherche YouTube: "${query}"`);

    // Base de connaissances simplifiée pour les vidéos YouTube populaires
    const videoDatabase = [
      {
        keywords: ['intelligence', 'artificielle', 'ia', 'ai'],
        videos: [
          {
            title: "L'intelligence artificielle expliquée en 5 minutes",
            channel: "Science Étonnante",
            views: "2,5M vues",
            time: "5:12",
            url: "https://www.youtube.com/watch?v=oEy_hfm5CjI",
            videoId: "oEy_hfm5CjI"
          }
        ]
      },
      {
        keywords: ['apprentissage', 'machine', 'learning', 'ml'],
        videos: [
          {
            title: "Machine Learning - Qu'est-ce que c'est ?",
            channel: "Micode",
            views: "1,2M vues",
            time: "8:42",
            url: "https://www.youtube.com/watch?v=OEJX-q6UOag",
            videoId: "OEJX-q6UOag"
          }
        ]
      },
      {
        keywords: ['productivité', 'efficacité', 'travail', 'organisation'],
        videos: [
          {
            title: "Comment être plus productif ? 8 techniques efficaces",
            channel: "TDC",
            views: "850K vues",
            time: "10:23",
            url: "https://www.youtube.com/watch?v=J8TcARQ7ZlA",
            videoId: "J8TcARQ7ZlA"
          }
        ]
      }
    ];

    // Rechercher les vidéos correspondant aux mots-clés
    const keywords = query.toLowerCase().split(' ');
    let matchedVideos = [];

    for (const entry of videoDatabase) {
      if (keywords.some(keyword => entry.keywords.includes(keyword))) {
        matchedVideos = matchedVideos.concat(entry.videos);
      }
    }

    // Si aucune vidéo n'est trouvée, retourner une vidéo générique
    if (matchedVideos.length === 0) {
      return {
        title: `Vidéos sur ${query}`,
        channel: "YouTube",
        views: "Plusieurs vues",
        time: "Variable",
        url: `https://www.youtube.com/results?search_query=${encodeURIComponent(query)}`,
        videoId: ""
      };
    }

    return matchedVideos[0];
  } catch (error) {
    console.error('Erreur lors de la recherche YouTube de secours:', error);
    return null;
  }
}

/**
 * Vérifie si une requête concerne la programmation
 * @param {string} query - La requête à vérifier
 * @returns {boolean} - True si la requête concerne la programmation
 */
function isProgrammingQuery(query) {
  const lowercaseQuery = query.toLowerCase();
  return PROGRAMMING_KEYWORDS.some(keyword => lowercaseQuery.includes(keyword));
}

/**
 * Recherche du code sur GitHub
 * @param {string} query - La requête de recherche
 * @param {number} limit - Nombre de résultats à retourner
 * @returns {Promise<Array>} - Les résultats de la recherche
 */
async function searchGitHub(query, limit = 5) {
  try {
    console.log(`Recherche GitHub pour: "${query}"`);

    // Vérifier le cache
    const cacheKey = `github:${query}:${limit}`;
    if (requestCache.has(cacheKey)) {
      const cachedData = requestCache.get(cacheKey);
      if (Date.now() - cachedData.timestamp < CACHE_EXPIRY) {
        console.log(`Utilisation des résultats GitHub en cache pour: "${query}"`);
        return cachedData.data;
      }
    }

    // Lancer un navigateur headless pour accéder à GitHub
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Définir un timeout raisonnable
    await page.setDefaultNavigationTimeout(15000);

    // Naviguer vers GitHub avec la requête de recherche
    await page.goto(`https://github.com/search?q=${encodeURIComponent(query)}&type=repositories`, {
      waitUntil: 'networkidle2'
    });

    // Attendre que les résultats se chargent
    try {
      await page.waitForSelector('.repo-list-item', { timeout: 10000 });
    } catch (error) {
      console.log('Timeout en attendant les résultats GitHub, tentative de continuer quand même');
    }

    // Extraire les informations des repositories
    const repoInfos = await page.evaluate((resultLimit) => {
      const repoElements = document.querySelectorAll('.repo-list-item');
      if (!repoElements || repoElements.length === 0) return [];

      // Limiter au nombre de résultats demandés
      const maxRepos = Math.min(resultLimit, repoElements.length);
      const results = [];

      for (let i = 0; i < maxRepos; i++) {
        const repoElement = repoElements[i];

        const titleElement = repoElement.querySelector('.f4 a');
        const descriptionElement = repoElement.querySelector('.mb-1');
        const starsElement = repoElement.querySelector('a[href$="/stargazers"]');
        const languageElement = repoElement.querySelector('[itemprop="programmingLanguage"]');

        if (titleElement) {
          const repoUrl = titleElement.href;
          const repoName = titleElement.textContent.trim();

          results.push({
            title: repoName,
            url: repoUrl,
            snippet: descriptionElement ? descriptionElement.textContent.trim() : '',
            stars: starsElement ? starsElement.textContent.trim() : '0',
            language: languageElement ? languageElement.textContent.trim() : 'Non spécifié',
            source: 'GitHub'
          });
        }
      }

      return results;
    }, limit);

    await browser.close();

    // Si aucun repository n'est trouvé, essayer la méthode de secours
    if (!repoInfos || repoInfos.length === 0) {
      console.log('Aucun repository trouvé, utilisation de la méthode de secours');
      return searchGitHubFallback(query, limit);
    }

    // Mettre en cache les résultats
    requestCache.set(cacheKey, {
      timestamp: Date.now(),
      data: repoInfos
    });

    return repoInfos;
  } catch (error) {
    console.error('Erreur lors de la recherche GitHub:', error);
    return searchGitHubFallback(query, limit);
  }
}

/**
 * Méthode de secours pour la recherche GitHub
 * @param {string} query - La requête de recherche
 * @param {number} limit - Nombre de résultats à retourner
 * @returns {Promise<Array>} - Les résultats de la recherche
 */
async function searchGitHubFallback(query, limit = 5) {
  try {
    console.log(`Utilisation de la méthode de secours pour la recherche GitHub: "${query}"`);

    // Extraire les mots-clés de programmation de la requête
    const queryWords = query.toLowerCase().split(/\s+/);
    const programmingKeywords = queryWords.filter(word =>
      PROGRAMMING_KEYWORDS.includes(word) ||
      PROGRAMMING_KEYWORDS.some(keyword => word.includes(keyword))
    );

    // Si aucun mot-clé de programmation n'est trouvé, utiliser les mots de la requête
    const searchTerms = programmingKeywords.length > 0 ? programmingKeywords : queryWords;

    // Construire une URL de recherche GitHub
    const searchUrl = `https://github.com/search?q=${encodeURIComponent(searchTerms.join('+'))}&type=repositories`;

    return [{
      title: `Résultats GitHub pour: ${query}`,
      url: searchUrl,
      snippet: `Recherche de repositories GitHub pour "${query}". Cliquez pour voir les résultats complets.`,
      stars: 'N/A',
      language: 'Divers',
      source: 'GitHub'
    }];
  } catch (error) {
    console.error('Erreur lors de la recherche GitHub de secours:', error);
    return [];
  }
}

/**
 * Recherche du code sur Stack Overflow
 * @param {string} query - La requête de recherche
 * @param {number} limit - Nombre de résultats à retourner
 * @returns {Promise<Array>} - Les résultats de la recherche
 */
async function searchStackOverflow(query, limit = 5) {
  try {
    console.log(`Recherche Stack Overflow pour: "${query}"`);

    // Vérifier le cache
    const cacheKey = `stackoverflow:${query}:${limit}`;
    if (requestCache.has(cacheKey)) {
      const cachedData = requestCache.get(cacheKey);
      if (Date.now() - cachedData.timestamp < CACHE_EXPIRY) {
        console.log(`Utilisation des résultats Stack Overflow en cache pour: "${query}"`);
        return cachedData.data;
      }
    }

    // Lancer un navigateur headless pour accéder à Stack Overflow
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Définir un timeout raisonnable
    await page.setDefaultNavigationTimeout(15000);

    // Naviguer vers Stack Overflow avec la requête de recherche
    await page.goto(`https://stackoverflow.com/search?q=${encodeURIComponent(query)}`, {
      waitUntil: 'networkidle2'
    });

    // Attendre que les résultats se chargent
    try {
      await page.waitForSelector('.question-summary', { timeout: 10000 });
    } catch (error) {
      console.log('Timeout en attendant les résultats Stack Overflow, tentative de continuer quand même');
    }

    // Extraire les informations des questions
    const questionInfos = await page.evaluate((resultLimit) => {
      const questionElements = document.querySelectorAll('.question-summary');
      if (!questionElements || questionElements.length === 0) return [];

      // Limiter au nombre de résultats demandés
      const maxQuestions = Math.min(resultLimit, questionElements.length);
      const results = [];

      for (let i = 0; i < maxQuestions; i++) {
        const questionElement = questionElements[i];

        const titleElement = questionElement.querySelector('.question-hyperlink');
        const excerptElement = questionElement.querySelector('.excerpt');
        const votesElement = questionElement.querySelector('.vote-count-post');
        const answersElement = questionElement.querySelector('.status strong');

        if (titleElement) {
          results.push({
            title: titleElement.textContent.trim(),
            url: titleElement.href,
            snippet: excerptElement ? excerptElement.textContent.trim() : '',
            votes: votesElement ? votesElement.textContent.trim() : '0',
            answers: answersElement ? answersElement.textContent.trim() : '0',
            source: 'Stack Overflow'
          });
        }
      }

      return results;
    }, limit);

    await browser.close();

    // Si aucune question n'est trouvée, essayer la méthode de secours
    if (!questionInfos || questionInfos.length === 0) {
      console.log('Aucune question trouvée, utilisation de la méthode de secours');
      return searchStackOverflowFallback(query, limit);
    }

    // Mettre en cache les résultats
    requestCache.set(cacheKey, {
      timestamp: Date.now(),
      data: questionInfos
    });

    return questionInfos;
  } catch (error) {
    console.error('Erreur lors de la recherche Stack Overflow:', error);
    return searchStackOverflowFallback(query, limit);
  }
}

/**
 * Méthode de secours pour la recherche Stack Overflow
 * @param {string} query - La requête de recherche
 * @param {number} limit - Nombre de résultats à retourner
 * @returns {Promise<Array>} - Les résultats de la recherche
 */
async function searchStackOverflowFallback(query, limit = 5) {
  try {
    console.log(`Utilisation de la méthode de secours pour la recherche Stack Overflow: "${query}"`);

    // Construire une URL de recherche Stack Overflow
    const searchUrl = `https://stackoverflow.com/search?q=${encodeURIComponent(query)}`;

    return [{
      title: `Résultats Stack Overflow pour: ${query}`,
      url: searchUrl,
      snippet: `Recherche de questions Stack Overflow pour "${query}". Cliquez pour voir les résultats complets.`,
      votes: 'N/A',
      answers: 'N/A',
      source: 'Stack Overflow'
    }];
  } catch (error) {
    console.error('Erreur lors de la recherche Stack Overflow de secours:', error);
    return [];
  }
}

/**
 * Enrichit une question avec des informations de programmation
 * @param {string} query - La requête de recherche
 * @returns {Promise<Object>} - Les résultats de la recherche
 */
async function enrichWithProgrammingInfo(query) {
  try {
    // Rechercher sur GitHub
    const githubResults = await searchGitHub(query, 3);

    // Rechercher sur Stack Overflow
    const stackoverflowResults = await searchStackOverflow(query, 3);

    return {
      github: githubResults,
      stackoverflow: stackoverflowResults
    };
  } catch (error) {
    console.error('Erreur lors de l\'enrichissement avec des informations de programmation:', error);
    return {
      github: [],
      stackoverflow: []
    };
  }
}

module.exports = {
  searchWeb,
  fetchWebPage,
  searchYouTubeVideo,
  isProgrammingQuery,
  searchGitHub,
  searchStackOverflow,
  enrichWithProgrammingInfo
};
