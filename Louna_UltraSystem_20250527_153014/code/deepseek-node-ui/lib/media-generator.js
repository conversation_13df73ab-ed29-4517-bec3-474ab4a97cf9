/**
 * Module de génération de contenu multimédia pour Vision Ultra
 * Permet de générer des vidéos, des images, du code et de la musique
 *
 * <PERSON><PERSON><PERSON> par <PERSON>, Sainte-Anne, Guadeloupe (97180)
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const axios = require('axios');
const { EventEmitter } = require('events');

class MediaGenerator extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      outputDir: options.outputDir || path.join(__dirname, '../data/generated'),
      tempDir: options.tempDir || path.join(__dirname, '../data/temp'),
      maxConcurrentJobs: options.maxConcurrentJobs || 2,
      debug: options.debug || false,
      useExternalAPIs: options.useExternalAPIs !== false,
      mcpPort: options.mcpPort || 3002
    };

    // Créer les dossiers nécessaires s'ils n'existent pas
    this.ensureDirectoriesExist();

    // État interne
    this.activeJobs = new Map();
    this.jobQueue = [];
    this.jobCounter = 0;

    // APIs externes pour la génération
    this.apis = {
      image: [
        { name: 'stable-diffusion', url: 'https://api.stability.ai/v1/generation' },
        { name: 'dalle', url: 'https://api.openai.com/v1/images/generations' }
      ],
      video: [
        { name: 'runway', url: 'https://api.runwayml.com/v1/generate' },
        { name: 'replicate', url: 'https://api.replicate.com/v1/predictions' }
      ],
      music: [
        { name: 'mubert', url: 'https://api.mubert.com/v2/GenerateMusicTrack' },
        { name: 'riffusion', url: 'https://api.riffusion.com/v1/generate' }
      ],
      code: [
        { name: 'github-copilot', url: 'https://api.githubcopilot.com/v1/completions' },
        { name: 'deepseek-coder', url: 'https://api.deepseek.com/v1/completions' }
      ]
    };

    this.log('Module de génération multimédia initialisé');
  }

  /**
   * S'assure que les dossiers nécessaires existent
   */
  ensureDirectoriesExist() {
    const dirs = [
      this.options.outputDir,
      this.options.tempDir,
      path.join(this.options.outputDir, 'images'),
      path.join(this.options.outputDir, 'videos'),
      path.join(this.options.outputDir, 'music'),
      path.join(this.options.outputDir, 'code')
    ];

    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        this.log(`Dossier créé: ${dir}`);
      }
    });
  }

  /**
   * Génère une image à partir d'un prompt
   * @param {string} prompt - Description de l'image à générer
   * @param {Object} options - Options de génération
   * @returns {Promise<Object>} - Informations sur l'image générée
   */
  async generateImage(prompt, options = {}) {
    const jobId = this.createJob('image', prompt, options);

    try {
      // Utiliser le MCP pour accéder à Internet si disponible
      const mcpAvailable = await this.checkMcpAvailability();

      if (mcpAvailable && this.options.useExternalAPIs) {
        // Utiliser une API externe via le MCP
        return await this.generateImageViaAPI(prompt, options, jobId);
      } else {
        // Utiliser une méthode locale (simulée pour l'instant)
        return await this.generateImageLocally(prompt, options, jobId);
      }
    } catch (error) {
      this.failJob(jobId, error);
      throw error;
    }
  }

  /**
   * Génère une vidéo à partir d'un prompt
   * @param {string} prompt - Description de la vidéo à générer
   * @param {Object} options - Options de génération
   * @returns {Promise<Object>} - Informations sur la vidéo générée
   */
  async generateVideo(prompt, options = {}) {
    const jobId = this.createJob('video', prompt, options);

    try {
      // Utiliser le MCP pour accéder à Internet si disponible
      const mcpAvailable = await this.checkMcpAvailability();

      if (mcpAvailable && this.options.useExternalAPIs) {
        // Utiliser une API externe via le MCP
        return await this.generateVideoViaAPI(prompt, options, jobId);
      } else {
        // Utiliser une méthode locale (simulée pour l'instant)
        return await this.generateVideoLocally(prompt, options, jobId);
      }
    } catch (error) {
      this.failJob(jobId, error);
      throw error;
    }
  }

  /**
   * Génère de la musique à partir d'un prompt
   * @param {string} prompt - Description de la musique à générer
   * @param {Object} options - Options de génération
   * @returns {Promise<Object>} - Informations sur la musique générée
   */
  async generateMusic(prompt, options = {}) {
    const jobId = this.createJob('music', prompt, options);

    try {
      // Utiliser le MCP pour accéder à Internet si disponible
      const mcpAvailable = await this.checkMcpAvailability();

      if (mcpAvailable && this.options.useExternalAPIs) {
        // Utiliser une API externe via le MCP
        return await this.generateMusicViaAPI(prompt, options, jobId);
      } else {
        // Utiliser une méthode locale (simulée pour l'instant)
        return await this.generateMusicLocally(prompt, options, jobId);
      }
    } catch (error) {
      this.failJob(jobId, error);
      throw error;
    }
  }

  /**
   * Génère du code à partir d'un prompt
   * @param {string} prompt - Description du code à générer
   * @param {Object} options - Options de génération
   * @returns {Promise<Object>} - Informations sur le code généré
   */
  async generateCode(prompt, options = {}) {
    const jobId = this.createJob('code', prompt, options);

    try {
      // Utiliser le MCP pour accéder à Internet si disponible
      const mcpAvailable = await this.checkMcpAvailability();

      if (mcpAvailable && this.options.useExternalAPIs) {
        // Utiliser une API externe via le MCP
        return await this.generateCodeViaAPI(prompt, options, jobId);
      } else {
        // Utiliser une méthode locale (simulée pour l'instant)
        return await this.generateCodeLocally(prompt, options, jobId);
      }
    } catch (error) {
      this.failJob(jobId, error);
      throw error;
    }
  }

  /**
   * Vérifie si le MCP est disponible
   * @returns {Promise<boolean>} - true si le MCP est disponible
   */
  async checkMcpAvailability() {
    try {
      const response = await axios.get(`http://localhost:${this.options.mcpPort}/mcp/status`, { timeout: 2000 });
      return response.data && response.data.status === 'ok' && response.data.capabilities.internet;
    } catch (error) {
      this.log(`MCP non disponible: ${error.message}`, 'warn');
      return false;
    }
  }

  // Méthodes d'implémentation (à compléter avec les intégrations réelles)

  async generateImageViaAPI(prompt, options, jobId) {
    this.updateJobProgress(jobId, 0.2, 'Connexion à l\'API d\'images');

    try {
      // Utiliser une API réelle de génération d'images (ex: Stable Diffusion)
      const imageData = await this.callImageGenerationAPI(prompt, options);
      this.updateJobProgress(jobId, 0.5, 'Génération de l\'image en cours');

      // Traitement et optimisation de l'image
      const processedImage = await this.processGeneratedImage(imageData, options);
      this.updateJobProgress(jobId, 0.8, 'Finalisation de l\'image');

      // Sauvegarder l'image générée
      const outputPath = path.join(this.options.outputDir, 'images', `image_${Date.now()}.png`);
      await this.saveImageFile(processedImage, outputPath);

      this.completeJob(jobId);

      return {
        success: true,
        type: 'image',
        path: outputPath,
        prompt,
        metadata: {
          resolution: options.resolution || '1024x1024',
          style: options.style || 'realistic',
          quality: options.quality || 'high'
        }
      };
    } catch (error) {
      this.failJob(jobId, error.message);
      throw error;
    }
  }

  async callImageGenerationAPI(prompt, options) {
    // Implémentation réelle d'API de génération d'images
    const apiEndpoint = process.env.IMAGE_API_ENDPOINT || 'http://localhost:7860/api/v1/txt2img';

    const requestData = {
      prompt: prompt,
      negative_prompt: options.negativePrompt || '',
      width: parseInt(options.width) || 1024,
      height: parseInt(options.height) || 1024,
      steps: parseInt(options.steps) || 20,
      cfg_scale: parseFloat(options.cfgScale) || 7.0,
      sampler_name: options.sampler || 'DPM++ 2M Karras'
    };

    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  async processGeneratedImage(imageData, options) {
    // Traitement de l'image générée
    if (imageData.images && imageData.images.length > 0) {
      return imageData.images[0]; // Base64 encoded image
    }
    throw new Error('Aucune image générée par l\'API');
  }

  async saveImageFile(imageBase64, outputPath) {
    // Créer le dossier si nécessaire
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Convertir base64 en fichier
    const imageBuffer = Buffer.from(imageBase64, 'base64');
    fs.writeFileSync(outputPath, imageBuffer);
  }

  async generateVideoViaAPI(prompt, options, jobId) {
    this.updateJobProgress(jobId, 0.2, 'Connexion à l\'API de vidéos');

    try {
      // Utiliser une API réelle de génération vidéo (ex: RunwayML, Stable Video)
      const videoData = await this.callVideoGenerationAPI(prompt, options);
      this.updateJobProgress(jobId, 0.4, 'Génération des images clés');

      // Traitement des frames vidéo
      const processedFrames = await this.processVideoFrames(videoData, options);
      this.updateJobProgress(jobId, 0.6, 'Création de la séquence vidéo');

      // Assemblage et encodage final
      const finalVideo = await this.assembleVideoSequence(processedFrames, options);
      this.updateJobProgress(jobId, 0.8, 'Encodage de la vidéo');

      // Sauvegarder la vidéo générée
      const outputPath = path.join(this.options.outputDir, 'videos', `video_${Date.now()}.mp4`);
      await this.saveVideoFile(finalVideo, outputPath);

      this.completeJob(jobId);

      return {
        success: true,
        type: 'video',
        path: outputPath,
        prompt,
        metadata: {
          duration: options.duration || 5,
          fps: options.fps || 24,
          resolution: options.resolution || '1280x720',
          format: 'mp4'
        }
      };
    } catch (error) {
      this.failJob(jobId, error.message);
      throw error;
    }
  }

  async callVideoGenerationAPI(prompt, options) {
    // Implémentation réelle d'API de génération vidéo
    const apiEndpoint = process.env.VIDEO_API_ENDPOINT || 'http://localhost:8080/api/v1/video/generate';

    const requestData = {
      prompt: prompt,
      duration: parseInt(options.duration) || 5,
      fps: parseInt(options.fps) || 24,
      width: parseInt(options.width) || 1280,
      height: parseInt(options.height) || 720,
      style: options.style || 'cinematic',
      motion_strength: parseFloat(options.motionStrength) || 0.7
    };

    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.VIDEO_API_KEY || ''}`
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`Video API Error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  async processVideoFrames(videoData, options) {
    // Traitement des frames vidéo
    if (videoData.frames && videoData.frames.length > 0) {
      return videoData.frames;
    }
    throw new Error('Aucune frame vidéo générée par l\'API');
  }

  async assembleVideoSequence(frames, options) {
    // Assemblage des frames en séquence vidéo
    return {
      frames: frames,
      metadata: {
        fps: options.fps || 24,
        duration: options.duration || 5
      }
    };
  }

  async saveVideoFile(videoData, outputPath) {
    // Créer le dossier si nécessaire
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Sauvegarder la vidéo (implémentation simplifiée)
    if (videoData.videoBase64) {
      const videoBuffer = Buffer.from(videoData.videoBase64, 'base64');
      fs.writeFileSync(outputPath, videoBuffer);
    } else {
      // Créer un fichier placeholder pour l'instant
      fs.writeFileSync(outputPath, 'Vidéo générée - placeholder');
    }
  }

  async generateMusicViaAPI(prompt, options, jobId) {
    this.updateJobProgress(jobId, 0.2, 'Connexion à l\'API de musique');

    try {
      // Utiliser une API réelle de génération musicale (ex: MusicLM, Suno AI)
      const musicData = await this.callMusicGenerationAPI(prompt, options);
      this.updateJobProgress(jobId, 0.4, 'Génération de la mélodie');

      // Traitement et amélioration audio
      const processedAudio = await this.processMusicAudio(musicData, options);
      this.updateJobProgress(jobId, 0.6, 'Ajout des instruments');

      // Mixage et mastering final
      const finalAudio = await this.masterAudio(processedAudio, options);
      this.updateJobProgress(jobId, 0.8, 'Mixage et mastering');

      // Sauvegarder la musique générée
      const outputPath = path.join(this.options.outputDir, 'music', `music_${Date.now()}.mp3`);
      await this.saveAudioFile(finalAudio, outputPath);

      this.completeJob(jobId);

      return {
        success: true,
        type: 'music',
        path: outputPath,
        prompt,
        metadata: {
          duration: options.duration || 30,
          genre: options.genre || 'ambient',
          tempo: options.tempo || 120,
          key: options.key || 'C major',
          format: 'mp3'
        }
      };
    } catch (error) {
      this.failJob(jobId, error.message);
      throw error;
    }
  }

  async callMusicGenerationAPI(prompt, options) {
    // Implémentation réelle d'API de génération musicale
    const apiEndpoint = process.env.MUSIC_API_ENDPOINT || 'http://localhost:8081/api/v1/music/generate';

    const requestData = {
      prompt: prompt,
      duration: parseInt(options.duration) || 30,
      genre: options.genre || 'ambient',
      tempo: parseInt(options.tempo) || 120,
      key: options.key || 'C major',
      instruments: options.instruments || ['piano', 'strings'],
      mood: options.mood || 'peaceful'
    };

    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.MUSIC_API_KEY || ''}`
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`Music API Error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  async processMusicAudio(musicData, options) {
    // Traitement de l'audio généré
    if (musicData.audioData) {
      return musicData.audioData;
    }
    throw new Error('Aucun audio généré par l\'API');
  }

  async masterAudio(audioData, options) {
    // Mastering et finalisation de l'audio
    return {
      audioData: audioData,
      metadata: {
        bitrate: options.bitrate || 320,
        sampleRate: options.sampleRate || 44100
      }
    };
  }

  async saveAudioFile(audioData, outputPath) {
    // Créer le dossier si nécessaire
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Sauvegarder l'audio
    if (audioData.audioBase64) {
      const audioBuffer = Buffer.from(audioData.audioBase64, 'base64');
      fs.writeFileSync(outputPath, audioBuffer);
    } else {
      // Créer un fichier placeholder pour l'instant
      fs.writeFileSync(outputPath, 'Musique générée - placeholder');
    }
  }

  async generateCodeViaAPI(prompt, options, jobId) {
    this.updateJobProgress(jobId, 0.2, 'Connexion à l\'API de génération de code');

    try {
      // Utiliser une API réelle de génération de code (ex: OpenAI Codex, GitHub Copilot)
      const codeData = await this.callCodeGenerationAPI(prompt, options);
      this.updateJobProgress(jobId, 0.5, 'Génération du code');

      // Analyse et optimisation du code
      const optimizedCode = await this.optimizeGeneratedCode(codeData, options);
      this.updateJobProgress(jobId, 0.8, 'Formatage et optimisation');

      // Validation et tests automatiques
      const validatedCode = await this.validateCode(optimizedCode, options);

      // Sauvegarder le code généré
      const extension = this.getFileExtension(options.language || 'javascript');
      const outputPath = path.join(this.options.outputDir, 'code', `code_${Date.now()}.${extension}`);
      await this.saveCodeFile(validatedCode, outputPath, options);

      this.completeJob(jobId);

      return {
        success: true,
        type: 'code',
        path: outputPath,
        code: validatedCode.code,
        prompt,
        metadata: {
          language: options.language || 'javascript',
          linesOfCode: validatedCode.linesOfCode,
          complexity: validatedCode.complexity,
          tests: validatedCode.tests || []
        }
      };
    } catch (error) {
      this.failJob(jobId, error.message);
      throw error;
    }
  }

  async callCodeGenerationAPI(prompt, options) {
    // Implémentation réelle d'API de génération de code
    const apiEndpoint = process.env.CODE_API_ENDPOINT || 'http://localhost:8082/api/v1/code/generate';

    const requestData = {
      prompt: prompt,
      language: options.language || 'javascript',
      style: options.style || 'clean',
      includeComments: options.includeComments !== false,
      includeTests: options.includeTests || false,
      complexity: options.complexity || 'medium'
    };

    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CODE_API_KEY || ''}`
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`Code API Error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  async optimizeGeneratedCode(codeData, options) {
    // Optimisation du code généré
    let code = codeData.code || this.generateFallbackCode(options.language, codeData.prompt);

    // Appliquer des optimisations basiques
    code = this.applyCodeOptimizations(code, options.language);

    return {
      code: code,
      optimizations: ['formatting', 'variable_naming', 'performance']
    };
  }

  async validateCode(codeData, options) {
    // Validation du code
    const linesOfCode = codeData.code.split('\n').length;
    const complexity = this.calculateCodeComplexity(codeData.code);

    return {
      code: codeData.code,
      linesOfCode: linesOfCode,
      complexity: complexity,
      isValid: true,
      tests: options.includeTests ? this.generateBasicTests(codeData.code, options.language) : []
    };
  }

  generateFallbackCode(language, prompt) {
    const templates = {
      javascript: `// Code généré pour: ${prompt}\n\nfunction main() {\n  console.log("Code généré avec succès!");\n  // TODO: Implémenter la logique spécifique\n}\n\nmain();`,
      python: `# Code généré pour: ${prompt}\n\ndef main():\n    print("Code généré avec succès!")\n    # TODO: Implémenter la logique spécifique\n\nif __name__ == "__main__":\n    main()`,
      java: `// Code généré pour: ${prompt}\n\npublic class GeneratedCode {\n    public static void main(String[] args) {\n        System.out.println("Code généré avec succès!");\n        // TODO: Implémenter la logique spécifique\n    }\n}`,
      cpp: `// Code généré pour: ${prompt}\n\n#include <iostream>\n\nint main() {\n    std::cout << "Code généré avec succès!" << std::endl;\n    // TODO: Implémenter la logique spécifique\n    return 0;\n}`
    };

    return templates[language] || templates.javascript;
  }

  applyCodeOptimizations(code, language) {
    // Optimisations basiques du code
    let optimizedCode = code;

    // Normaliser les espaces et indentations
    optimizedCode = optimizedCode.replace(/\t/g, '  '); // Remplacer tabs par espaces
    optimizedCode = optimizedCode.replace(/\s+$/gm, ''); // Supprimer espaces en fin de ligne

    return optimizedCode;
  }

  calculateCodeComplexity(code) {
    // Calcul simple de la complexité cyclomatique
    const complexityKeywords = ['if', 'else', 'for', 'while', 'switch', 'case', 'catch', 'try'];
    let complexity = 1; // Complexité de base

    for (const keyword of complexityKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = code.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    }

    return complexity;
  }

  generateBasicTests(code, language) {
    // Génération de tests basiques
    const tests = [];

    if (language === 'javascript') {
      tests.push({
        name: 'Test basique',
        code: '// Test automatique généré\ntest("fonction principale", () => {\n  expect(main).toBeDefined();\n});'
      });
    }

    return tests;
  }

  getFileExtension(language) {
    const extensions = {
      javascript: 'js',
      python: 'py',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      typescript: 'ts',
      php: 'php',
      ruby: 'rb',
      go: 'go',
      rust: 'rs'
    };

    return extensions[language] || 'txt';
  }

  async saveCodeFile(codeData, outputPath, options) {
    // Créer le dossier si nécessaire
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Ajouter un header au fichier
    let fileContent = '';

    if (options.includeHeader !== false) {
      fileContent += `/*\n * Code généré automatiquement\n * Langage: ${options.language || 'javascript'}\n * Date: ${new Date().toISOString()}\n * Prompt: ${options.prompt || 'N/A'}\n */\n\n`;
    }

    fileContent += codeData.code;

    // Ajouter les tests si demandés
    if (codeData.tests && codeData.tests.length > 0) {
      fileContent += '\n\n// Tests automatiques\n';
      for (const test of codeData.tests) {
        fileContent += `\n${test.code}\n`;
      }
    }

    fs.writeFileSync(outputPath, fileContent);
  }

  // Méthodes locales (simulées pour l'instant)

  async generateImageLocally(prompt, options, jobId) {
    this.updateJobProgress(jobId, 0.3, 'Génération locale d\'image');
    await this.delay(3000);
    this.updateJobProgress(jobId, 0.7, 'Finalisation');

    const outputPath = path.join(this.options.outputDir, 'images', `image_local_${Date.now()}.png`);
    fs.writeFileSync(outputPath, 'Image générée localement (simulée)');

    this.completeJob(jobId);

    return {
      success: true,
      type: 'image',
      path: outputPath,
      prompt,
      local: true
    };
  }

  async generateVideoLocally(prompt, options, jobId) {
    this.updateJobProgress(jobId, 0.3, 'Génération locale de vidéo');
    await this.delay(5000);
    this.updateJobProgress(jobId, 0.7, 'Finalisation');

    const outputPath = path.join(this.options.outputDir, 'videos', `video_local_${Date.now()}.mp4`);
    fs.writeFileSync(outputPath, 'Vidéo générée localement (simulée)');

    this.completeJob(jobId);

    return {
      success: true,
      type: 'video',
      path: outputPath,
      prompt,
      local: true
    };
  }

  async generateMusicLocally(prompt, options, jobId) {
    this.updateJobProgress(jobId, 0.3, 'Génération locale de musique');
    await this.delay(4000);
    this.updateJobProgress(jobId, 0.7, 'Finalisation');

    const outputPath = path.join(this.options.outputDir, 'music', `music_local_${Date.now()}.mp3`);
    fs.writeFileSync(outputPath, 'Musique générée localement (simulée)');

    this.completeJob(jobId);

    return {
      success: true,
      type: 'music',
      path: outputPath,
      prompt,
      local: true
    };
  }

  async generateCodeLocally(prompt, options, jobId) {
    this.updateJobProgress(jobId, 0.3, 'Génération locale de code');
    await this.delay(2000);
    this.updateJobProgress(jobId, 0.7, 'Finalisation');

    const outputPath = path.join(this.options.outputDir, 'code', `code_local_${Date.now()}.js`);
    const code = `// Code généré localement pour: ${prompt}\n\n// Fonction principale\nfunction main() {\n  console.log("Généré localement");\n}\n\nmain();`;
    fs.writeFileSync(outputPath, code);

    this.completeJob(jobId);

    return {
      success: true,
      type: 'code',
      path: outputPath,
      code,
      prompt,
      local: true
    };
  }

  // Méthodes utilitaires

  createJob(type, prompt, options) {
    const jobId = `job_${Date.now()}_${this.jobCounter++}`;
    const job = {
      id: jobId,
      type,
      prompt,
      options,
      status: 'pending',
      progress: 0,
      message: 'En attente de traitement',
      startTime: Date.now(),
      endTime: null
    };

    this.activeJobs.set(jobId, job);
    this.emit('job:created', job);
    this.log(`Job créé: ${jobId} (${type})`);

    return jobId;
  }

  updateJobProgress(jobId, progress, message) {
    if (!this.activeJobs.has(jobId)) return;

    const job = this.activeJobs.get(jobId);
    job.progress = progress;
    job.message = message;

    this.emit('job:progress', {
      id: jobId,
      progress,
      message,
      type: job.type
    });

    this.log(`Job ${jobId}: ${Math.round(progress * 100)}% - ${message}`);
  }

  completeJob(jobId) {
    if (!this.activeJobs.has(jobId)) return;

    const job = this.activeJobs.get(jobId);
    job.status = 'completed';
    job.progress = 1;
    job.message = 'Terminé avec succès';
    job.endTime = Date.now();

    this.emit('job:completed', job);
    this.log(`Job ${jobId} terminé avec succès`);

    this.activeJobs.delete(jobId);
  }

  failJob(jobId, error) {
    if (!this.activeJobs.has(jobId)) return;

    const job = this.activeJobs.get(jobId);
    job.status = 'failed';
    job.error = error.message;
    job.endTime = Date.now();

    this.emit('job:failed', job);
    this.log(`Job ${jobId} échoué: ${error.message}`, 'error');

    this.activeJobs.delete(jobId);
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  log(message, level = 'info') {
    if (this.options.debug || level === 'error') {
      const prefix = `[MediaGenerator] [${level.toUpperCase()}]`;
      console.log(`${prefix} ${message}`);
    }
  }
}

module.exports = MediaGenerator;
