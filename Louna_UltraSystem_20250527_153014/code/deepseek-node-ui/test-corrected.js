/**
 * Script pour tester l'assistant avec les informations corrigées
 */

const io = require('socket.io-client');

// Fonction pour envoyer un message et attendre la réponse
function sendMessage(message) {
  return new Promise((resolve, reject) => {
    try {
      // Se connecter au serveur
      const socket = io('http://localhost:3001');
      
      // Gérer la connexion
      socket.on('connect', () => {
        console.log('Connecté au serveur WebSocket');
        
        // Envoyer le message
        console.log(`Envoi du message: "${message}"`);
        socket.emit('luna message', { message });
      });
      
      // Gérer la réponse
      socket.on('luna response', (data) => {
        console.log(`Réponse reçue: "${data.message}"`);
        socket.disconnect();
        resolve(data.message);
      });
      
      // Gérer les erreurs
      socket.on('connect_error', (error) => {
        console.error('Erreur de connexion:', error.message);
        socket.disconnect();
        reject(error);
      });
      
      // Timeout après 30 secondes
      setTimeout(() => {
        if (socket.connected) {
          socket.disconnect();
          reject(new Error('Timeout: Pas de réponse après 30 secondes'));
        }
      }, 30000);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error.message);
      reject(error);
    }
  });
}

// Fonction principale
async function runTests() {
  console.log('=== TESTS AVEC LES INFORMATIONS CORRIGÉES ===');
  
  try {
    // Test 1: Nom de l'assistant
    console.log('\n--- Test 1: Nom de l\'assistant ---');
    const response1 = await sendMessage('Comment tu t\'appelles?');
    
    // Test 2: Nom de l'utilisateur
    console.log('\n--- Test 2: Nom de l\'utilisateur ---');
    const response2 = await sendMessage('Comment je m\'appelle?');
    
    // Test 3: Lieu de résidence de l'utilisateur
    console.log('\n--- Test 3: Lieu de résidence de l\'utilisateur ---');
    const response3 = await sendMessage('Où est-ce que j\'habite?');
    
    // Test 4: Origine de l'utilisateur
    console.log('\n--- Test 4: Origine de l\'utilisateur ---');
    const response4 = await sendMessage('Quelle est mon origine?');
    
    // Test 5: Créateur de l'assistant
    console.log('\n--- Test 5: Créateur de l\'assistant ---');
    const response5 = await sendMessage('Qui est ton créateur?');
    
    // Test 6: Identité complète de l'utilisateur
    console.log('\n--- Test 6: Identité complète de l\'utilisateur ---');
    const response6 = await sendMessage('Qui suis-je?');
    
    console.log('\n=== RÉSULTATS DES TESTS ===');
    console.log('Test 1 (Nom de l\'assistant):', response1);
    console.log('Test 2 (Nom de l\'utilisateur):', response2);
    console.log('Test 3 (Lieu de résidence):', response3);
    console.log('Test 4 (Origine):', response4);
    console.log('Test 5 (Créateur):', response5);
    console.log('Test 6 (Identité complète):', response6);
    
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'exécution des tests:', error.message);
    return false;
  }
}

// Exécuter la fonction principale
runTests().then(() => {
  console.log('\nTests terminés');
  process.exit(0);
}).catch((error) => {
  console.error('Erreur:', error);
  process.exit(1);
});
