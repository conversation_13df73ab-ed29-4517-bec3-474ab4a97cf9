/**
 * Serveur Louna - Interface cognitive avancée
 * Version simplifiée pour tester la mémoire thermique
 */

const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const path = require('path');
const fs = require('fs');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const ThermalMemory = require('./modules/ThermalMemory');
// const BrainConnection = require('./thermal-memory/brain-connection');
// const ThermalAccelerators = require('./thermal-memory/thermal-accelerators');
// const KyberAccelerators = require('./thermal-memory/kyber-accelerators');
const { config } = require('./config');
const PORT = process.env.PORT || 3005;

// Configuration
const app = express();
const server = http.createServer(app);
const io = new Server(server);

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Importer les routes du générateur de code
const codeGeneratorRoutes = require('./routes/code-generator');

// Importer l'API de mémoire thermique
const thermalMemoryApiRouter = require('./routes/thermal-memory-api');

// Middleware pour rediriger les requêtes de /luna/* vers /louna/*
app.use((req, res, next) => {
  if (req.path === '/luna') {
    // Rediriger /luna vers /louna
    console.log(`Redirection de ${req.path} vers /louna`);
    return res.redirect('/louna');
  } else if (req.path.startsWith('/luna/')) {
    // Remplacer /luna/ par /louna/ dans le chemin
    const newPath = req.path.replace('/luna/', '/louna/');
    console.log(`Redirection de ${req.path} vers ${newPath}`);
    return res.redirect(newPath);
  }
  next();
});

// Servir l'interface directement à la racine
app.get('/', (req, res) => {
  res.redirect('/louna');
});
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Créer les dossiers nécessaires s'ils n'existent pas
const MEMORY_DIR = path.join(__dirname, 'data/memory');
if (!fs.existsSync(MEMORY_DIR)) {
  fs.mkdirSync(MEMORY_DIR, { recursive: true });
}

// Charger les services
// const BrainPresence = require('./services/brain-presence');

// Initialisation du vrai système de mémoire thermique avec MongoDB
// const RealThermalMemory = require('./modules/RealThermalMemory');
// const HardwareSensors = require('./modules/HardwareSensors');
// const ThermalAcceleratorsController = require('./modules/ThermalAcceleratorsController');

// Vérifier si MongoDB est disponible
const mongoUrl = process.env.MONGO_URL || 'mongodb://localhost:27017/thermal_memory';

// Définir thermalMemory dans la portée globale
let thermalMemory;

// Utiliser un système de mémoire thermique simple basé sur un fichier JSON
console.log('Initialisation du système de mémoire thermique simple...');
thermalMemory = new ThermalMemory({
  storagePath: path.join(__dirname, 'data/memory/thermal_memory.json'),
  autosaveInterval: 5 * 60 * 1000, // 5 minutes
  cooldownInterval: 30 * 60 * 1000, // 30 minutes
  maxEntries: 5000
});

console.log('Mémoire thermique initialisée');

// S'assurer que thermalMemory est défini avant de l'utiliser
if (thermalMemory) {
  // Initialiser le service de présence cérébrale
  // const brainPresence = new BrainPresence(thermalMemory);
  console.log('Service de présence cérébrale initialisé');

  // Activer le service de présence cérébrale
  // brainPresence.initialize();
  console.log('Service de présence cérébrale activé');
} else {
  console.error('ERREUR: La mémoire thermique n\'a pas été initialisée correctement.');
}

// Route principale
app.get('/louna', (req, res) => {
  res.render('luna-home', {
    title: 'Louna - Interface Cognitive',
    page: 'home'
  });
});

// Route pour la page d'accueil
app.get('/louna/home', (req, res) => {
  res.render('luna-home', {
    title: 'Louna - Accueil',
    page: 'home'
  });
});

// Route pour la page de chat
app.get('/louna/chat', (req, res) => {
  res.render('luna-chat', {
    title: 'Louna - Chat',
    page: 'chat'
  });
});

// Route pour la page de mémoire
app.get('/louna/memory', (req, res) => {
  res.render('luna-memory', {
    title: 'Louna - Mémoire',
    page: 'memory'
  });
});

// Route pour la page de formation
app.get('/louna/training', (req, res) => {
  res.render('luna-training', {
    title: 'Louna - Formation',
    page: 'training'
  });
});

// Route pour la page de code
app.get('/louna/code', (req, res) => {
  res.render('luna-code', {
    title: 'Louna - Code',
    page: 'code'
  });
});

// Route pour la page de sécurité
app.get('/louna/security', (req, res) => {
  res.render('luna-security', {
    title: 'Louna - Sécurité',
    page: 'security'
  });
});

// Route pour la page de sauvegarde
app.get('/louna/backup', (req, res) => {
  res.render('luna-backup', {
    title: 'Louna - Sauvegarde',
    page: 'backup'
  });
});

// Route pour la page de surveillance
app.get('/louna/monitor', (req, res) => {
  res.render('luna-monitor', {
    title: 'Louna - Surveillance',
    page: 'monitor'
  });
});

// Route pour la page des accélérateurs
app.get('/louna/accelerators', (req, res) => {
  res.render('luna-accelerators', {
    title: 'Louna - Accélérateurs',
    page: 'accelerators'
  });
});

// Route pour la page des statistiques
app.get('/louna/stats', (req, res) => {
  res.render('luna-stats', {
    title: 'Louna - Statistiques',
    page: 'stats'
  });
});

// Route pour la page des paramètres
app.get('/louna/settings', (req, res) => {
  res.render('luna-settings', {
    title: 'Louna - Paramètres',
    page: 'settings'
  });
});

// Route pour la page des modèles
app.get('/louna/models', (req, res) => {
  res.render('luna-models', {
    title: 'Louna - Modèles',
    page: 'models'
  });
});

// Route pour la page des documents
app.get('/louna/documents', (req, res) => {
  res.render('luna-documents', {
    title: 'Louna - Documents',
    page: 'documents'
  });
});

// Route pour la page des prompts
app.get('/louna/prompts', (req, res) => {
  res.render('luna-prompts', {
    title: 'Louna - Prompts',
    page: 'prompts'
  });
});

// Route pour la page MCP
app.get('/louna/mcp', (req, res) => {
  res.render('luna-mcp', {
    title: 'Louna - MCP',
    page: 'mcp'
  });
});

// Route pour la page Internet
app.get('/louna/internet', (req, res) => {
  res.render('luna-internet', {
    title: 'Louna - Internet',
    page: 'internet'
  });
});

// Route pour la page VPN
app.get('/louna/vpn', (req, res) => {
  res.render('luna-vpn', {
    title: 'Louna - VPN',
    page: 'vpn'
  });
});

// Route pour la page Antivirus
app.get('/louna/antivirus', (req, res) => {
  res.render('luna-antivirus', {
    title: 'Louna - Antivirus',
    page: 'antivirus'
  });
});

// Route pour la page Cognitive
app.get('/louna/cognitive', (req, res) => {
  res.render('luna-cognitive', {
    title: 'Louna - Cognitive',
    page: 'cognitive'
  });
});

// Route pour la page d'accueil du cerveau
app.get('/louna/brain/welcome', (req, res) => {
  // Récupérer les statistiques de la mémoire thermique
  let memoryStats = {
    totalEntries: 0,
    averageTemperature: 0,
    activeZones: 0
  };

  try {
    if (thermalMemory && thermalMemory.memory && thermalMemory.memory.memories) {
      // Calcul des statistiques de base
      const memories = thermalMemory.memory.memories;
      memoryStats.totalEntries = memories.length;

      // Calcul de la température moyenne
      if (memories.length > 0) {
        const totalTemp = memories.reduce((sum, entry) => sum + (entry.temperature || 0), 0);
        memoryStats.averageTemperature = (totalTemp / memories.length).toFixed(1);
      }

      // Nombre de zones actives
      const activeZones = new Set(memories.map(m => m.zone)).size;
      memoryStats.activeZones = activeZones;
    }
  } catch (err) {
    console.error('Erreur lors de la récupération des statistiques de mémoire:', err);
  }

  res.render('luna-welcome', {
    title: 'Bienvenue dans le Cerveau Luna',
    page: 'brain',
    memoryStats: memoryStats
  });
});

// Route pour la page Brain
app.get('/louna/brain', (req, res) => {
  // Si l'utilisateur arrive pour la première fois, le rediriger vers la page d'accueil
  const hasVisitedBrain = req.cookies && req.cookies.has_visited_brain;

  if (!hasVisitedBrain && !req.query.direct) {
    // Définir un cookie pour ne plus rediriger à l'avenir
    res.cookie('has_visited_brain', 'true', { maxAge: 30 * 24 * 60 * 60 * 1000 }); // 30 jours
    return res.redirect('/louna/brain/welcome');
  }

  res.render('luna-brain', {
    title: 'Louna - Cerveau',
    page: 'brain'
  });
});

// Route pour la page d'analyse du cerveau
app.get('/louna/brain/analysis', (req, res) => {
  // Récupérer les statistiques de la mémoire thermique pour l'analyse
  let memoryStats = {
    totalEntries: 0,
    averageTemperature: 0,
    activeZones: 0,
    entriesByZone: {}
  };

  try {
    if (thermalMemory && thermalMemory.memory && thermalMemory.memory.memories) {
      // Calcul des statistiques
      const memories = thermalMemory.memory.memories;
      memoryStats.totalEntries = memories.length;

      // Calcul de la température moyenne
      if (memories.length > 0) {
        const totalTemp = memories.reduce((sum, entry) => sum + (entry.temperature || 0), 0);
        memoryStats.averageTemperature = (totalTemp / memories.length).toFixed(1);
      }

      // Nombre de zones actives et répartition par zone
      const zoneSet = new Set();
      memoryStats.entriesByZone = {};

      for (let zone = 1; zone <= 6; zone++) {
        const zoneEntries = memories.filter(entry => entry.zone === zone);
        memoryStats.entriesByZone[`zone${zone}`] = zoneEntries.length;

        if (zoneEntries.length > 0) {
          zoneSet.add(zone);
        }
      }

      memoryStats.activeZones = zoneSet.size;
    }
  } catch (err) {
    console.error('Erreur lors de la récupération des statistiques de mémoire pour l\'analyse:', err);
  }

  // Préparer les données pour le script
  const scriptData = `<script>
    window.memoryStats = ${JSON.stringify(memoryStats)};
  </script>`;

  res.render('luna-brain-analysis', {
    title: 'Louna - Analyse du Cerveau',
    page: 'brain-analysis',
    memoryStats: memoryStats,
    scriptData: scriptData
  });
});

// Route pour la visualisation des zones de mémoire
app.get('/louna/brain/zones', (req, res) => {
  // Récupérer les données des zones
  let zonesData = {
    zones: [],
    totalEntries: 0,
    distribution: {}
  };

  try {
    if (thermalMemory && thermalMemory.memory && thermalMemory.memory.memories) {
      const memories = thermalMemory.memory.memories;
      zonesData.totalEntries = memories.length;

      // Préparer les données pour chaque zone
      for (let zone = 1; zone <= 6; zone++) {
        // Répartition des entrées entre les zones pour démo
        let zoneEntries;
        if (zone === 1) {
          // Zone 1: 20% des entrées les plus récentes
          zoneEntries = memories.slice(0, Math.floor(memories.length * 0.2));
        } else if (zone === 2) {
          // Zone 2: 30% des entrées
          zoneEntries = memories.slice(Math.floor(memories.length * 0.2), Math.floor(memories.length * 0.5));
        } else if (zone === 3) {
          // Zone 3: 25% des entrées
          zoneEntries = memories.slice(Math.floor(memories.length * 0.5), Math.floor(memories.length * 0.75));
        } else if (zone === 4) {
          // Zone 4: 15% des entrées
          zoneEntries = memories.slice(Math.floor(memories.length * 0.75), Math.floor(memories.length * 0.9));
        } else if (zone === 5) {
          // Zone 5: 5% des entrées
          zoneEntries = memories.slice(Math.floor(memories.length * 0.9), Math.floor(memories.length * 0.95));
        } else {
          // Zone 6: 5% des entrées
          zoneEntries = memories.slice(Math.floor(memories.length * 0.95));
        }

        // S'assurer que ces entrées temporaires ont la bonne zone assignée pour les rendres filtrable par zone
        zoneEntries = zoneEntries.map(entry => ({
          ...entry,
          zone: zone
        }));

        // Calculer la température moyenne de la zone
        let avgTemp = 0;
        if (zoneEntries.length > 0) {
          avgTemp = zoneEntries.reduce((sum, entry) => sum + (entry.temperature || 0), 0) / zoneEntries.length;
        }

        // Nom et description de la zone
        const zoneName = {
          1: 'Récente',
          2: 'Chaude',
          3: 'Tiède',
          4: 'Froide',
          5: 'Froide',
          6: 'Archive'
        }[zone] || `Zone ${zone}`;

        // Ajouter les données de la zone
        zonesData.zones.push({
          id: zone,
          name: zoneName,
          temperature: avgTemp.toFixed(1),
          count: zoneEntries.length,
          entries: zoneEntries.map(entry => ({
            id: entry.id,
            type: entry.type || 'memory',
            content: entry.content || 'Contenu non disponible',
            temperature: entry.temperature,
            timestamp: entry.timestamp,
            accessCount: entry._accessCount || 0
          }))
        });

        // Stocker la distribution
        zonesData.distribution[`zone${zone}`] = zoneEntries.length;
      }
    }
  } catch (err) {
    console.error('Erreur lors de la récupération des zones de mémoire:', err);
  }

  // Préparer les données pour le script
  const scriptData = `<script>
    window.zonesData = ${JSON.stringify(zonesData)};
  </script>`;

  res.render('luna-brain-zones', {
    title: 'Louna - Zones de Mémoire',
    page: 'brain-zones',
    zonesData: zonesData,
    scriptData: scriptData
  });
});

// Route pour les analyses et statistiques
app.get('/louna/brain/analytics', (req, res) => {
  // Préparer les données d'analyse
  let analyticsData = {
    totalStats: {
      entries: 0,
      avgTemperature: 0,
      activeZones: 0,
      memoryTypes: {}
    },
    timeSeriesData: [],
    zoneDistribution: {},
    temperatureDistribution: []
  };

  try {
    if (thermalMemory && thermalMemory.memory && thermalMemory.memory.memories) {
      const memories = thermalMemory.memory.memories;

      // Statistiques générales
      analyticsData.totalStats.entries = memories.length;

      if (memories.length > 0) {
        // Température moyenne
        const totalTemp = memories.reduce((sum, entry) => sum + (entry.temperature || 0), 0);
        analyticsData.totalStats.avgTemperature = (totalTemp / memories.length).toFixed(1);

        // Compter les types de mémoire
        const typeCountMap = {};
        memories.forEach(entry => {
          const type = entry.type || 'unknown';
          typeCountMap[type] = (typeCountMap[type] || 0) + 1;
        });
        analyticsData.totalStats.memoryTypes = typeCountMap;

        // Zones actives
        const zonesSet = new Set(memories.map(entry => entry.zone));
        analyticsData.totalStats.activeZones = zonesSet.size;

        // Distribution par zone - avec répartition similaire à la route zone
        const zoneDistribution = {
          1: Math.floor(memories.length * 0.2),  // 20%
          2: Math.floor(memories.length * 0.3),  // 30%
          3: Math.floor(memories.length * 0.25), // 25%
          4: Math.floor(memories.length * 0.15), // 15%
          5: Math.floor(memories.length * 0.05), // 5%
          6: memories.length - Math.floor(memories.length * 0.95) // reste (~5%)
        };

        for (let zone = 1; zone <= 6; zone++) {
          analyticsData.zoneDistribution[`zone${zone}`] = zoneDistribution[zone] || 0;
        }

        // Distribution par température
        const tempRanges = [
          { min: 0, max: 20, label: '0-20°C' },
          { min: 21, max: 40, label: '21-40°C' },
          { min: 41, max: 60, label: '41-60°C' },
          { min: 61, max: 80, label: '61-80°C' },
          { min: 81, max: 100, label: '81-100°C' }
        ];

        tempRanges.forEach(range => {
          const count = memories.filter(entry => {
            const temp = entry.temperature || 0;
            return temp >= range.min && temp <= range.max;
          }).length;

          analyticsData.temperatureDistribution.push({
            range: range.label,
            count: count
          });
        });

        // Générer des données de série temporelle pour la démonstration
        // Dans une implémentation réelle, cela viendrait d'un historique réel
        const now = new Date();
        for (let i = 0; i < 24; i++) {
          const timestamp = new Date(now.getTime() - (i * 60 * 60 * 1000));
          const entry = {
            timestamp: timestamp.toISOString(),
            totalEntries: Math.max(0, memories.length - Math.floor(Math.random() * i * 10)),
            avgTemperature: Math.max(20, Math.min(100, parseFloat(analyticsData.totalStats.avgTemperature) - i * Math.random()))
          };
          analyticsData.timeSeriesData.push(entry);
        }
        // Inverser pour avoir l'ordre chronologique
        analyticsData.timeSeriesData.reverse();
      }
    }
  } catch (err) {
    console.error('Erreur lors de la préparation des données d\'analyse:', err);
  }

  // Préparer les données pour le script inline
  const scriptData = `<script>
    window.analyticsData = ${JSON.stringify(analyticsData)};
  </script>`;

  res.render('luna-brain-analytics', {
    title: 'Louna - Analyse de Mémoire',
    page: 'brain-analytics',
    analyticsData: analyticsData,
    scriptData: scriptData
  });
});

// Route pour la page Cognitive-Fixed
app.get('/louna/cognitive-fixed', (req, res) => {
  res.render('luna-cognitive-fixed', {
    title: 'Louna - Cognitive Fixé',
    page: 'cognitive-fixed'
  });
});

// Route pour la page Présence
app.get('/louna/presence', (req, res) => {
  res.render('luna-presence', {
    title: 'Louna - Présence',
    page: 'presence'
  });
});

// Route pour la page Réflexion
app.get('/louna/reflection', (req, res) => {
  res.render('luna-reflection', {
    title: 'Louna - Réflexion',
    page: 'reflection'
  });
});

// Route pour la page Code-Fixer
app.get('/louna/code-fixer', (req, res) => {
  res.render('luna-code-fixer', {
    title: 'Louna - Correcteur de Code',
    page: 'code-fixer'
  });
});

// Route pour la page VSCode
app.get('/louna/vscode', (req, res) => {
  res.render('luna-vscode', {
    title: 'Louna - VS Code',
    page: 'vscode'
  });
});

// Route pour la page Media
app.get('/louna/media', (req, res) => {
  res.render('luna-media', {
    title: 'Louna - Média',
    page: 'media'
  });
});

// Route pour la page Multimedia
app.get('/louna/multimedia', (req, res) => {
  res.render('luna-multimedia', {
    title: 'Louna - Multimédia',
    page: 'multimedia'
  });
});

// Route pour la page Multimedia-Studio
app.get('/louna/multimedia-studio', (req, res) => {
  res.render('luna-multimedia-studio', {
    title: 'Louna - Studio Multimédia',
    page: 'multimedia-studio'
  });
});

// Route pour la page Connectivity
app.get('/louna/connectivity', (req, res) => {
  res.render('luna-connectivity', {
    title: 'Louna - Connectivité',
    page: 'connectivity'
  });
});

// Route pour la page Meeting
app.get('/louna/meeting', (req, res) => {
  res.render('luna-meeting', {
    title: 'Louna - Réunion',
    page: 'meeting'
  });
});

// Route pour la page Presentation
app.get('/louna/presentation', (req, res) => {
  res.render('luna-presentation', {
    title: 'Louna - Présentation',
    page: 'presentation'
  });
});

// Route pour la page Program-Knowledge
app.get('/louna/program-knowledge', (req, res) => {
  res.render('luna-program-knowledge', {
    title: 'Louna - Connaissance des Programmes',
    page: 'program-knowledge'
  });
});

// Route pour la page Sleep-Mode
app.get('/louna/sleep-mode', (req, res) => {
  res.render('luna-sleep-mode', {
    title: 'Louna - Mode Veille',
    page: 'sleep-mode'
  });
});

// Route pour la page Thermal
app.get('/louna/thermal', (req, res) => {
  res.render('luna-thermal', {
    title: 'Louna - Mémoire Thermique',
    page: 'thermal'
  });
});

// Route pour la page Accelerators-Hierarchy
app.get('/louna/accelerators-hierarchy', (req, res) => {
  res.render('luna-accelerators-hierarchy', {
    title: 'Louna - Hiérarchie des Accélérateurs',
    page: 'accelerators-hierarchy'
  });
});

// Route pour la page Accelerators-New
app.get('/louna/accelerators-new', (req, res) => {
  res.render('luna-accelerators-new', {
    title: 'Louna - Nouveaux Accélérateurs',
    page: 'accelerators-new'
  });
});

// Route pour la page Accelerators-Updated
app.get('/louna/accelerators-updated', (req, res) => {
  res.render('luna-accelerators-updated', {
    title: 'Louna - Accélérateurs Mis à Jour',
    page: 'accelerators-updated'
  });
});

// Route pour la page Memory-Optimizer
app.get('/louna/memory-optimizer', (req, res) => {
  res.render('memory-optimizer', {
    title: 'Louna - Optimiseur de Mémoire',
    page: 'memory-optimizer'
  });
});

// Route pour la page Thermal-Accelerators
app.get('/louna/thermal-accelerators', (req, res) => {
  res.render('thermal-accelerators', {
    title: 'Louna - Accélérateurs Thermiques',
    page: 'thermal-accelerators'
  });
});

// Route pour la page Cognitive-System
app.get('/louna/cognitive-system', (req, res) => {
  res.render('cognitive-system', {
    title: 'Louna - Système Cognitif',
    page: 'cognitive-system'
  });
});

// Route pour la page Test-Chat
app.get('/louna/test-chat', (req, res) => {
  res.render('test-chat', {
    title: 'Louna - Test Chat',
    page: 'test-chat'
  });
});

// Route pour la page Code-Interface
app.get('/louna/code-interface', (req, res) => {
  res.render('code-interface', {
    title: 'Louna - Interface de Code',
    page: 'code-interface'
  });
});

// Route pour la page Book-Analysis
app.get('/louna/book-analysis', (req, res) => {
  res.render('book-analysis', {
    title: 'Louna - Analyse de Livre',
    page: 'book-analysis'
  });
});

// Route pour la page Brain-Presence (partiel)
app.get('/louna/brain-presence', (req, res) => {
  res.render('partials/brain-presence', {
    title: 'Louna - Présence Cérébrale',
    page: 'brain-presence'
  });
});

// Configurer les routes du générateur de code
app.use('/api/code-generator', codeGeneratorRoutes);

// Enregistrer l'API de mémoire thermique avec accès aux données réelles
app.use('/api/thermal-memory', thermalMemoryApiRouter(thermalMemory));

// Gestionnaire de socket pour les messages
io.on('connection', (socket) => {
  console.log('Nouvelle connexion WebSocket');

  // Gérer les messages de l'utilisateur
  socket.on('louna message', (data) => {
    console.log('===== NOUVEAU MESSAGE REÇU =====');
    console.log('Message reçu:', data.message);
    console.log('Historique de conversation:', data.history);
    console.log('================================');

    // Stocker le message dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'input',
      content: data.message,
      timestamp: new Date().toISOString()
    });

    // Générer une réponse immédiate pour tester la communication
    const responseText = `Je suis Louna, votre assistant cognitif. J'ai bien reçu votre message : "${data.message}". La communication fonctionne correctement !`;

    console.log('Envoi de la réponse au client:', responseText);

    const response = {
      message: responseText,
      timestamp: new Date().toISOString()
    };

    // Stocker la réponse dans la mémoire thermique
    thermalMemory.addEntry({
      type: 'output',
      content: response.message,
      timestamp: response.timestamp
    });

    // Envoyer la réponse au client immédiatement
    socket.emit('louna response', response);

    // Envoyer également une notification de log
    console.log('Réponse envoyée au client avec succès');
  });

  // Gestionnaires d'événements pour le générateur de code
  socket.on('generate code', async (data) => {
    console.log('Demande de génération de code reçue:', data);

    try {
      // Validation des données
      if (!data.description || !data.language) {
        throw new Error('Description et langage sont requis');
      }

      // Sélection du service de génération selon les paramètres
      const { generateCodeWithDeepseek, generateCodeWithFallbackModel } = require('./services/code-generation-service');

      let generatedCode;

      if (data.useDeepseek) {
        console.log('Génération de code avec DeepSeek...');
        generatedCode = await generateCodeWithDeepseek(data.description, data.language, data.style, data.comments);
      } else {
        console.log('Génération de code avec le modèle de secours...');
        generatedCode = await generateCodeWithFallbackModel(data.description, data.language, data.style, data.comments);
      }

      // Envoi du code généré au client
      socket.emit('code generated', {
        code: generatedCode,
        language: data.language,
        description: data.description,
        timestamp: new Date().toISOString()
      });

      console.log('Code généré et envoyé avec succès');
    } catch (error) {
      console.error('Erreur lors de la génération de code:', error);
      socket.emit('code generation error', { error: error.message || 'Erreur inconnue' });
    }
  });

  // Vérification de la disponibilité de DeepSeek
  socket.on('check deepseek', async () => {
    try {
      // Vérifier si DeepSeek est disponible (configuration et connexion)
      const isAvailable = config.deepseek && config.deepseek.apiKey;
      socket.emit('deepseek status', { available: !!isAvailable });
      console.log('Statut DeepSeek envoyé:', !!isAvailable);
    } catch (error) {
      console.error('Erreur lors de la vérification de DeepSeek:', error);
      socket.emit('deepseek status', { available: false, error: error.message });
    }
  });

  // Gérer la déconnexion
  socket.on('disconnect', () => {
    console.log('Déconnexion WebSocket');
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Serveur Louna démarré sur le port ${PORT}`);
  console.log(`Interface accessible à l'adresse http://localhost:${PORT}`);
});
