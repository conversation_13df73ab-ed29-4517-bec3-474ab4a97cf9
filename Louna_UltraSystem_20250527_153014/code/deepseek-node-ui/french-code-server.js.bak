const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const os = require('os');
const fileUpload = require('express-fileupload');
const { v4: uuidv4 } = require('uuid');
const ThermalMemory = require('./thermal-memory/thermal-memory');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000
});
const PORT = process.env.PORT || 3000;

// Middleware pour le traitement des fichiers
app.use(fileUpload({
  limits: { fileSize: 50 * 1024 * 1024 }, // Limite de 50 MB
}));

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Initialiser la mémoire thermique avec accélérateurs Kyber
const thermalMemory = new ThermalMemory();
// Intégrer les accélérateurs Kyber pour toutes les zones
const KyberAccelerators = require('./thermal-memory/kyber-accelerators');
const kyberAccelerators = new KyberAccelerators(thermalMemory);
// Ajouter les accélérateurs au module de mémoire thermique
thermalMemory.kyberAccelerators = kyberAccelerators;

// Intégrer le processeur de documents pour la mémoire thermique
const DocumentProcessor = require('./thermal-memory/document-processor');
const documentProcessor = new DocumentProcessor(thermalMemory);
// Ajouter le processeur de documents au module de mémoire thermique
thermalMemory.documentProcessor = documentProcessor;

// Initialiser le système cognitif
const { createCognitiveSystem } = require('./cognitive-system');
const cognitiveSystem = createCognitiveSystem({
  name: 'DeepSeek Assistant',
  language: 'fr-FR',
  thermalMemory: thermalMemory,
  debugMode: true
});
// Ajouter le système cognitif à la mémoire thermique
thermalMemory.cognitiveSystem = cognitiveSystem;

console.log('Mémoire thermique, réseau d\'accélérateurs Kyber, processeur de documents et système cognitif initialisés');

// Créer le dossier de conversations s'il n'existe pas
const CONVERSATIONS_DIR = path.join(__dirname, 'conversations');
if (!fs.existsSync(CONVERSATIONS_DIR)) {
  fs.mkdirSync(CONVERSATIONS_DIR, { recursive: true });
}

// Créer le dossier de documents s'il n'existe pas
const DOCUMENTS_DIR = path.join(__dirname, 'documents');
if (!fs.existsSync(DOCUMENTS_DIR)) {
  fs.mkdirSync(DOCUMENTS_DIR, { recursive: true });
}

// Fonction pour construire un prompt en français
function buildFrenchPrompt(message) {
  return `Voici un message en français: "${message}". Réponds en français.`;
}

// Routes
app.get('/', (req, res) => {
  res.render('index', { title: 'DeepSeek Français' });
});

// Routes pour l'upload de documents
app.post('/api/upload-document', (req, res) => {
  try {
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Aucun fichier n\'a été téléchargé'
      });
    }
    
    const uploadedFile = req.files.document;
    const uploadPath = path.join(DOCUMENTS_DIR, uploadedFile.name);
    
    uploadedFile.mv(uploadPath, async (err) => {
      if (err) {
        console.error('Erreur lors de l\'upload du fichier:', err);
        return res.status(500).json({
          success: false,
          message: `Erreur lors de l'upload: ${err.message}`
        });
      }
      
      try {
        // Traiter le document avec le processeur de documents
        const docInfo = await documentProcessor.processDocument(uploadPath, {
          originalFilename: uploadedFile.name,
          fileType: uploadedFile.mimetype,
          fileSize: uploadedFile.size
        });
        
        res.json({
          success: true,
          message: 'Document téléchargé et traité avec succès',
          document: docInfo
        });
      } catch (processError) {
        console.error('Erreur lors du traitement du document:', processError);
        res.status(500).json({
          success: false,
          message: `Erreur lors du traitement: ${processError.message}`
        });
      }
    });
  } catch (error) {
    console.error('Erreur lors de l\'upload:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Récupérer tous les documents
app.get('/api/documents', (req, res) => {
  try {
    const documents = documentProcessor.getAllDocuments();
    res.json({
      success: true,
      documents: documents
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des documents:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Rechercher des documents par mots-clés
app.get('/api/documents/search', (req, res) => {
  try {
    const { query } = req.query;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Le paramètre de recherche est requis'
      });
    }
    
    const documents = documentProcessor.searchDocuments(query);
    res.json({
      success: true,
      query: query,
      documents: documents
    });
  } catch (error) {
    console.error('Erreur lors de la recherche de documents:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Récupérer un document spécifique
app.get('/api/documents/:id', (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'L\'ID du document est requis'
      });
    }
    
    const document = documentProcessor.getDocument(id);
    
    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }
    
    res.json({
      success: true,
      document: document
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du document:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Routes pour le système cognitif
// Activer le système cognitif
app.get('/api/cognitive/activate', (req, res) => {
  try {
    const result = cognitiveSystem.activate();
    res.json({
      success: true,
      active: result,
      message: 'Système cognitif activé'
    });
  } catch (error) {
    console.error('Erreur lors de l\'activation du système cognitif:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Désactiver le système cognitif
app.get('/api/cognitive/deactivate', (req, res) => {
  try {
    const result = cognitiveSystem.deactivate();
    res.json({
      success: true,
      active: !result,
      message: 'Système cognitif désactivé'
    });
  } catch (error) {
    console.error('Erreur lors de la désactivation du système cognitif:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Faire parler le système cognitif
app.post('/api/cognitive/speak', (req, res) => {
  try {
    const { text } = req.body;
    
    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'Le texte à prononcer est requis'
      });
    }
    
    const result = cognitiveSystem.speak(text);
    res.json({
      success: true,
      speaking: result,
      text: text,
      message: 'Synthèse vocale démarrée'
    });
  } catch (error) {
    console.error('Erreur lors de la synthèse vocale:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Démarrer l'écoute vocale
app.get('/api/cognitive/listen/start', (req, res) => {
  try {
    const result = cognitiveSystem.listen();
    res.json({
      success: true,
      listening: result,
      message: 'Écoute vocale démarrée'
    });
  } catch (error) {
    console.error('Erreur lors du démarrage de l\'écoute vocale:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Arrêter l'écoute vocale
app.get('/api/cognitive/listen/stop', (req, res) => {
  try {
    const result = cognitiveSystem.stopListening();
    res.json({
      success: true,
      listening: !result,
      message: 'Écoute vocale arrêtée'
    });
  } catch (error) {
    console.error('Erreur lors de l\'arrêt de l\'écoute vocale:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Observer l'environnement
app.get('/api/cognitive/observe', (req, res) => {
  try {
    const result = cognitiveSystem.observe();
    res.json({
      success: true,
      observing: result,
      message: 'Observation de l\'environnement démarrée'
    });
  } catch (error) {
    console.error('Erreur lors de l\'observation de l\'environnement:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Obtenir l'état du système cognitif
app.get('/api/cognitive/state', (req, res) => {
  try {
    const state = cognitiveSystem.getState();
    res.json({
      success: true,
      state: state
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'état du système cognitif:', error);
    res.status(500).json({
      success: false,
      message: `Erreur: ${error.message}`
    });
  }
});

// Socket.io pour les communications en temps réel
io.on('connection', (socket) => {
  console.log('Nouveau client connecté:', socket.id);
  
  // Variables pour suivre la conversation actuelle
  let currentConversationId = null;
  let currentMessages = [];
  
  // Charger une conversation existante
  socket.on('load conversation', async (data) => {
    try {
      const { conversationId } = data;
      
      if (!conversationId) {
        return socket.emit('error', { message: 'ID de conversation requis' });
      }
      
      const filePath = path.join(CONVERSATIONS_DIR, `${conversationId}.json`);
      
      if (fs.existsSync(filePath)) {
        const conversationData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        currentConversationId = conversationId;
        currentMessages = conversationData.messages || [];
        
        socket.emit('conversation loaded', {
          id: conversationId,
          title: conversationData.title,
          messages: currentMessages
        });
        
        // Ajouter à la mémoire thermique
        thermalMemory.addConversation(conversationData);
      } else {
        socket.emit('error', { message: 'Conversation non trouvée' });
      }
    } catch (error) {
      console.error('Erreur lors de la restauration de la conversation:', error);
      socket.emit('error', { message: `Erreur: ${error.message}` });
    }
  });

  // Traitement des messages
  socket.on('chat message', async (data) => {
    console.log('Message reçu du client:', data);
    
    try {
      socket.emit('processing', true);
      
      // Créer une nouvelle conversation si aucune n'est active
      if (!currentConversationId) {
        currentConversationId = uuidv4();
        currentMessages = [];
        
        const now = new Date().toISOString();
        const conversation = {
          id: currentConversationId,
          title: 'Nouvelle conversation',
          created: now,
          updated: now,
          messages: []
        };
        
        const filePath = path.join(CONVERSATIONS_DIR, `${currentConversationId}.json`);
        fs.writeFileSync(filePath, JSON.stringify(conversation, null, 2), 'utf8');
        
        socket.emit('conversation created', {
          id: currentConversationId,
          title: conversation.title
        });
      }
      
      // Enregistrer le message de l'utilisateur
      const userMessage = {
        role: 'user',
        content: data.message,
        timestamp: new Date().toISOString()
      };
      currentMessages.push(userMessage);
      
      // Modifier le message pour forcer la réponse en français
      const frenchPrompt = buildFrenchPrompt(data.message);
      
      // Utiliser exactement le même format que la commande curl qui a fonctionné
      const requestData = {
        model: 'deepseek-r1:7b',
        messages: [
          ...currentMessages.map(msg => ({
            role: msg.role,
            content: msg.content
          }))
        ],
        stream: true,
        options: {
          temperature: 0.7,
          top_p: 0.95,
          frequency_penalty: 0,
          presence_penalty: 0,
          max_tokens: 1000
        }
      };
      
      try {
        console.log('Envoi de la requête à Ollama');
        const response = await axios.post('http://localhost:11434/api/chat', requestData, {
          responseType: 'stream'
        });
        
        let fullResponse = '';
        let isInThinkBlock = false;
        
        console.log('Réception du stream de réponse');
        
        // Traiter la réponse streaming
        response.data.on('data', (chunk) => {
          const lines = chunk.toString().split('\n').filter(line => line.trim() !== '');
          
          for (const line of lines) {
            try {
              const data = JSON.parse(line);
              
              // Fin du streaming
              if (data.done) {
                socket.emit('partial response', { done: true });
                console.log('Streaming terminé');
                continue;
              }
              
              // Extraire le contenu
              const content = data.message?.content || '';
              
              // Vérifier si nous entrons ou sortons d'un bloc think
              if (content.includes('```think')) {
                isInThinkBlock = true;
              } else if (isInThinkBlock && content.includes('```')) {
                isInThinkBlock = false;
              }
              
              // Envoyer la mise à jour partielle au client
              socket.emit('partial response', {
                content: content,
                done: false
              });
              
              // N'ajouter le contenu que si nous ne sommes pas dans un bloc think
              if (!isInThinkBlock) {
                fullResponse += content;
              }
            }
          try {
          } catch (e) {
            console.warn('Impossible de parser la ligne:', line);
          }
        });
        
        response.data.on('end', () => {
          console.log('Réponse extraite et nettoyée');
          
          // Vérifier si le message original contient une demande de création de dossier
          const createFolderRegex = /cr[ée]{1,2}[er]?\s+(?:un\s+)?(?:dossier|r[ée]pertoire)\s+(?:qui\s+s['']appelle\s+)?(?:nomm[ée]\s+)?["']?([^"'.,!?]+)["']?/i;
          const match = data.message.match(createFolderRegex);
          
          if (match && match[1]) {
            const folderName = match[1].trim();
            console.log(`Détection d'une demande de création de dossier: "${folderName}"`);
            
            try {
              const desktopPath = path.join(os.homedir(), 'Desktop');
              const folderPath = path.join(desktopPath, folderName);
              
              if (!fs.existsSync(folderPath)) {
                fs.mkdirSync(folderPath, { recursive: true });
                const successMessage = `J'ai créé le dossier "${folderName}" sur votre bureau.`;
                fullResponse += "\n\n" + successMessage;
              } else {
                const existsMessage = `Le dossier "${folderName}" existe déjà sur votre bureau.`;
                fullResponse += "\n\n" + existsMessage;
              }
            } catch (error) {
              console.error('Erreur lors de la création du dossier:', error);
              const errorMessage = `Je n'ai pas pu créer le dossier. Erreur: ${error.message}`;
              fullResponse += "\n\n" + errorMessage;
            }
          }
          
          // Enregistrer la réponse de l'assistant
          const assistantMessage = {
            role: 'assistant',
            content: fullResponse.trim(),
            timestamp: new Date().toISOString()
          };
          currentMessages.push(assistantMessage);
          
          // Mettre à jour le fichier de conversation
          const filePath = path.join(CONVERSATIONS_DIR, `${currentConversationId}.json`);
          
          // Déterminer le titre de la conversation à partir du premier message
          let title = 'Nouvelle conversation';
          if (currentMessages.length >= 2 && currentMessages[0].role === 'user') {
            const firstMsg = currentMessages[0].content;
            title = firstMsg.length > 50 ? firstMsg.substring(0, 50) + '...' : firstMsg;
          }
          
          const conversation = {
            id: currentConversationId,
            title: title,
            created: fs.existsSync(filePath) ? 
              JSON.parse(fs.readFileSync(filePath, 'utf8')).created : 
              new Date().toISOString(),
            updated: new Date().toISOString(),
            messages: currentMessages
          };
          
          fs.writeFileSync(filePath, JSON.stringify(conversation, null, 2), 'utf8');
          
          // Envoyer la réponse au client
          socket.emit('chat response', {
            message: assistantMessage,
            conversationId: currentConversationId
          });
          
          // Informer le client de la mise à jour du titre
          if (title !== 'Nouvelle conversation') {
            socket.emit('conversation title updated', {
              id: currentConversationId,
              title: title
            });
          }
          
          // Ajouter à la mémoire thermique
          thermalMemory.addConversation(conversation);
          
          socket.emit('processing', false);
        });
      } catch (error) {
        console.error('Erreur lors du traitement du message:', error);
        
        // Plus de détails sur l'erreur
        if (error.response) {
          console.error('Statut de la réponse:', error.response.status);
          console.error('Données de la réponse:', error.response.data);
        }
        
        const errorMessage = {
          role: 'assistant',
          content: `Erreur lors du traitement de votre message: ${error.message}`,
          timestamp: new Date().toISOString()
        };
        
        currentMessages.push(errorMessage);
        
        socket.emit('chat response', {
          message: errorMessage,
          conversationId: currentConversationId
        });
        
        socket.emit('processing', false);
      }
    } catch (error) {
      console.error('Erreur générale:', error);
      socket.emit('error', { message: `Erreur: ${error.message}` });
      socket.emit('processing', false);
    }
  });
  
  // Déconnexion du client
  socket.on('disconnect', () => {
    console.log('Client déconnecté:', socket.id);
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Serveur démarré sur http://localhost:${PORT}`);
  console.log('Version française avec coloration syntaxique pour le code');
});
