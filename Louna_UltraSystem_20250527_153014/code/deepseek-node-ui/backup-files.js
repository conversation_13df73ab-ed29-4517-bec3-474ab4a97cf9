/**
 * Script de sauvegarde automatique des fichiers critiques
 * Ce script crée des copies de sauvegarde des fichiers importants
 */

const fs = require('fs');
const path = require('path');

// Liste des fichiers critiques à sauvegarder
const criticalFiles = [
  'server-luna.js',
  'routes/luna.js',
  'routes/luna-models.js',
  'routes/luna-memory.js',
  'routes/luna-prompts.js',
  'routes/luna-mcp.js',
  'routes/reflection-accelerators.js',
  'public/js/luna-interface.js'
];

// Fonction pour créer une sauvegarde d'un fichier
function backupFile(filePath) {
  const now = new Date();
  const dateStr = now.toISOString().replace(/:/g, '-').replace(/\..+/, '');
  
  // Créer le répertoire de sauvegarde s'il n'existe pas
  const backupDir = path.join(__dirname, 'backups');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  // Créer le répertoire pour le fichier spécifique s'il n'existe pas
  const fileBackupDir = path.join(backupDir, path.dirname(filePath));
  if (!fs.existsSync(fileBackupDir)) {
    fs.mkdirSync(fileBackupDir, { recursive: true });
  }
  
  // Nom du fichier de sauvegarde
  const fileName = path.basename(filePath);
  const backupFilePath = path.join(fileBackupDir, `${fileName}.${dateStr}.bak`);
  
  // Copier le fichier
  try {
    fs.copyFileSync(path.join(__dirname, filePath), backupFilePath);
    console.log(`✅ Sauvegarde créée: ${backupFilePath}`);
    return true;
  } catch (error) {
    console.error(`❌ Erreur lors de la sauvegarde de ${filePath}:`, error.message);
    return false;
  }
}

// Fonction pour sauvegarder tous les fichiers critiques
function backupAllFiles() {
  console.log('📦 Sauvegarde des fichiers critiques...');
  
  let successCount = 0;
  let failCount = 0;
  
  criticalFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    
    // Vérifier si le fichier existe
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️ Le fichier ${file} n'existe pas et ne sera pas sauvegardé.`);
      return;
    }
    
    // Créer une sauvegarde
    if (backupFile(file)) {
      successCount++;
    } else {
      failCount++;
    }
  });
  
  console.log(`📊 Sauvegarde terminée: ${successCount} fichiers sauvegardés, ${failCount} échecs.`);
  return { successCount, failCount };
}

// Exporter les fonctions pour une utilisation dans d'autres scripts
module.exports = {
  backupFile,
  backupAllFiles
};

// Si le script est exécuté directement, sauvegarder tous les fichiers
if (require.main === module) {
  backupAllFiles();
}
