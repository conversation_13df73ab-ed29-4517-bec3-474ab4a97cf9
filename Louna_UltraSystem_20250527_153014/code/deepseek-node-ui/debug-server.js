/**
 * Serveur de débogage pour l'interface DeepSeek r1
 * Ce serveur enregistre tous les détails des erreurs et des communications avec Ollama
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const { promisify } = require('util');
const util = require('util');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server);
const PORT = process.env.PORT || 3000;

// Configuration du journal de débogage
const DEBUG_LOG_PATH = path.join(__dirname, 'debug.log');
const writeFileAsync = promisify(fs.writeFile);
const appendFileAsync = promisify(fs.appendFile);

// Fonction pour enregistrer les messages de débogage
async function logDebug(message, data = null) {
  const timestamp = new Date().toISOString();
  let logMessage = `[${timestamp}] ${message}\n`;
  
  if (data) {
    if (typeof data === 'object') {
      logMessage += `${util.inspect(data, { depth: null, colors: false })}\n`;
    } else {
      logMessage += `${data}\n`;
    }
  }
  
  console.log(logMessage);
  
  try {
    await appendFileAsync(DEBUG_LOG_PATH, logMessage);
  } catch (error) {
    console.error('Erreur lors de l\'écriture dans le journal de débogage:', error);
  }
}

// Initialiser le journal de débogage
async function initDebugLog() {
  try {
    await writeFileAsync(DEBUG_LOG_PATH, `=== JOURNAL DE DÉBOGAGE DÉMARRÉ LE ${new Date().toISOString()} ===\n\n`);
    logDebug('Journal de débogage initialisé');
  } catch (error) {
    console.error('Erreur lors de l\'initialisation du journal de débogage:', error);
  }
}

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// URL de l'API Ollama
const OLLAMA_API_URL = 'http://localhost:11434';
const OLLAMA_API_URL_ALT = 'http://127.0.0.1:11434';

// Variables globales
let selectedModel = 'deepseek-r1:7b';
let isOllamaRunning = false;
let ollamaApiUrl = OLLAMA_API_URL;

// Agent simulé pour les tests
const simulatedAgent = {
  name: 'DeepSeek r1 (Simulé)',
  respond: (message) => {
    const responses = [
      `Je suis un agent DeepSeek r1 simulé. Votre message était : "${message}"`,
      `En tant qu'agent simulé, je peux vous dire que j'ai bien reçu votre message : "${message}"`,
      `Bonjour ! Je suis l'agent DeepSeek r1 en mode simulé. Je ne suis pas connecté à Ollama, mais je peux quand même vous répondre de manière basique.`,
      `Merci pour votre message. Je suis actuellement en mode simulé car Ollama n'est pas disponible.`,
      `Je suis désolé, mais je ne peux pas traiter votre demande de manière complète car je suis en mode simulé.`
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  }
};

// Fonction pour vérifier si Ollama est en cours d'exécution
async function checkOllama() {
  logDebug('Vérification si Ollama est en cours d\'exécution...');
  
  // Essayer l'URL principale
  try {
    logDebug(`Tentative de connexion à ${ollamaApiUrl}/api/version`);
    const response = await axios.get(`${ollamaApiUrl}/api/version`, { timeout: 5000 });
    logDebug('Ollama est en cours d\'exécution (API)', response.data);
    return true;
  } catch (apiError) {
    logDebug(`Erreur lors de la connexion à ${ollamaApiUrl}/api/version`, apiError.message);
    
    // Essayer l'URL principale avec /version
    try {
      logDebug(`Tentative de connexion à ${ollamaApiUrl}/version`);
      const response = await axios.get(`${ollamaApiUrl}/version`, { timeout: 5000 });
      logDebug('Ollama est en cours d\'exécution (version)', response.data);
      return true;
    } catch (versionError) {
      logDebug(`Erreur lors de la connexion à ${ollamaApiUrl}/version`, versionError.message);
      
      // Essayer l'URL alternative
      try {
        logDebug(`Tentative de connexion à ${OLLAMA_API_URL_ALT}/api/version`);
        const response = await axios.get(`${OLLAMA_API_URL_ALT}/api/version`, { timeout: 5000 });
        logDebug('Ollama est en cours d\'exécution (API alternative)', response.data);
        ollamaApiUrl = OLLAMA_API_URL_ALT;
        return true;
      } catch (altApiError) {
        logDebug(`Erreur lors de la connexion à ${OLLAMA_API_URL_ALT}/api/version`, altApiError.message);
        
        // Essayer l'URL alternative avec /version
        try {
          logDebug(`Tentative de connexion à ${OLLAMA_API_URL_ALT}/version`);
          const response = await axios.get(`${OLLAMA_API_URL_ALT}/version`, { timeout: 5000 });
          logDebug('Ollama est en cours d\'exécution (version alternative)', response.data);
          ollamaApiUrl = OLLAMA_API_URL_ALT;
          return true;
        } catch (altVersionError) {
          logDebug(`Erreur lors de la connexion à ${OLLAMA_API_URL_ALT}/version`, altVersionError.message);
          logDebug('Ollama n\'est pas en cours d\'exécution');
          return false;
        }
      }
    }
  }
}

// Fonction pour obtenir les modèles disponibles
async function getAvailableModels() {
  logDebug('Récupération des modèles disponibles...');
  
  try {
    logDebug(`Tentative de récupération des modèles via ${ollamaApiUrl}/api/tags`);
    const response = await axios.get(`${ollamaApiUrl}/api/tags`, { timeout: 5000 });
    logDebug('Modèles récupérés (tags)', response.data);
    return response.data.models || [];
  } catch (tagsError) {
    logDebug(`Erreur lors de la récupération des modèles via ${ollamaApiUrl}/api/tags`, tagsError.message);
    
    try {
      logDebug(`Tentative de récupération des modèles via ${ollamaApiUrl}/api/models`);
      const response = await axios.get(`${ollamaApiUrl}/api/models`, { timeout: 5000 });
      logDebug('Modèles récupérés (models)', response.data);
      return response.data.models || [];
    } catch (modelsError) {
      logDebug(`Erreur lors de la récupération des modèles via ${ollamaApiUrl}/api/models`, modelsError.message);
      return [];
    }
  }
}

// Routes
app.get('/', (req, res) => {
  logDebug('Accès à la page d\'accueil');
  res.render('index', { title: 'DeepSeek r1 Interface (Debug)' });
});

// Route pour la mémoire thermique
app.get('/memory', (req, res) => {
  logDebug('Accès à la page de mémoire thermique');
  res.render('memory', { title: 'Mémoire Thermique - DeepSeek r1' });
});

// Route pour l'analyse de livres
app.get('/books', (req, res) => {
  logDebug('Accès à la page d\'analyse de livres');
  res.render('book-analysis', { title: 'Analyse de Livres - DeepSeek r1' });
});

// Route pour la gestion des modèles
app.get('/models', (req, res) => {
  logDebug('Accès à la page de gestion des modèles');
  res.render('models', { title: 'Gestion des Modèles - DeepSeek r1' });
});

// Route pour les paramètres
app.get('/settings', (req, res) => {
  logDebug('Accès à la page de paramètres');
  res.render('settings', { title: 'Paramètres - DeepSeek r1' });
});

// Route pour obtenir le journal de débogage
app.get('/api/debug-log', (req, res) => {
  logDebug('Demande du journal de débogage');
  
  fs.readFile(DEBUG_LOG_PATH, 'utf8', (err, data) => {
    if (err) {
      logDebug('Erreur lors de la lecture du journal de débogage', err);
      return res.status(500).json({ error: 'Erreur lors de la lecture du journal de débogage' });
    }
    
    res.setHeader('Content-Type', 'text/plain');
    res.send(data);
  });
});

// Socket.io pour les communications en temps réel
io.on('connection', async (socket) => {
  logDebug('Nouveau client connecté', { socketId: socket.id });

  // Vérifier si Ollama est en cours d'exécution
  isOllamaRunning = await checkOllama();
  socket.emit('ollama status', { isRunning: isOllamaRunning });
  logDebug('Statut d\'Ollama envoyé au client', { isRunning: isOllamaRunning });

  // Récupérer les modèles disponibles
  if (isOllamaRunning) {
    const models = await getAvailableModels();
    socket.emit('models', { models });
    logDebug('Modèles envoyés au client', { models });
  }

  // Gérer les messages de chat
  socket.on('chat message', async (data) => {
    try {
      logDebug('Message reçu du client', data);
      const { message, history, modelName, temperature, maxTokens } = data;

      if (!message) {
        logDebug('Message vide');
        return socket.emit('chat response', {
          error: 'Message cannot be empty'
        });
      }

      // Vérifier si Ollama est en cours d'exécution
      if (!isOllamaRunning) {
        isOllamaRunning = await checkOllama();
      }

      if (!isOllamaRunning) {
        // Si Ollama n'est pas en cours d'exécution, utiliser l'agent simulé
        logDebug('Ollama n\'est pas en cours d\'exécution, utilisation de l\'agent simulé');
        const simulatedResponse = simulatedAgent.respond(message);
        logDebug('Réponse simulée générée', { response: simulatedResponse });
        return socket.emit('chat response', {
          message: {
            role: 'assistant',
            content: simulatedResponse
          }
        });
      }

      logDebug('Envoi du message à Ollama', {
        model: modelName || selectedModel,
        message,
        temperature: parseFloat(temperature || 0.7),
        maxTokens: parseInt(maxTokens || 1000)
      });

      try {
        // Préparer les données pour l'API Ollama
        const requestData = {
          model: modelName || selectedModel,
          messages: [...(history || []), { role: 'user', content: message }],
          options: {
            temperature: parseFloat(temperature || 0.7),
            num_predict: parseInt(maxTokens || 1000)
          }
        };

        logDebug('Données de la requête', requestData);

        // Appeler l'API Ollama
        try {
          // Essayer d'abord l'API de chat
          logDebug(`Appel de l'API de chat: ${ollamaApiUrl}/api/chat`);
          const response = await axios.post(`${ollamaApiUrl}/api/chat`, requestData, { timeout: 30000 });
          logDebug('Réponse reçue de l\'API de chat', response.data);

          // Extraire la réponse finale
          let finalResponse = {
            message: {
              role: 'assistant',
              content: ''
            }
          };

          if (response.data && response.data.message && response.data.message.content) {
            finalResponse.message.content = response.data.message.content;
          } else if (typeof response.data === 'string') {
            // Si la réponse est une chaîne JSON, la parser
            try {
              logDebug('Réponse sous forme de chaîne, tentative de parsing');
              // Diviser la chaîne en lignes
              const lines = response.data.split('\n').filter(line => line.trim());
              logDebug('Lignes extraites', { lines });

              // Extraire le contenu de chaque ligne
              let fullContent = '';
              for (const line of lines) {
                try {
                  const jsonObj = JSON.parse(line);
                  if (jsonObj.message && jsonObj.message.content) {
                    fullContent += jsonObj.message.content;
                  }
                } catch (e) {
                  logDebug('Erreur lors du parsing d\'une ligne', { line, error: e.message });
                  // Ignorer les lignes qui ne sont pas du JSON valide
                }
              }

              // Nettoyer le contenu
              fullContent = fullContent.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
              logDebug('Contenu extrait', { fullContent });

              finalResponse.message.content = fullContent;
            } catch (e) {
              logDebug('Erreur lors du traitement de la réponse', e);
              finalResponse.message.content = "Erreur lors du traitement de la réponse.";
            }
          }

          logDebug('Réponse finale', finalResponse);
          socket.emit('chat response', finalResponse);
        } catch (chatError) {
          logDebug('Erreur avec l\'API de chat, tentative avec l\'API de génération', chatError.message);

          // Si l'API de chat échoue, essayer l'API de génération
          try {
            const generateRequestData = {
              model: modelName || selectedModel,
              prompt: message,
              options: {
                temperature: parseFloat(temperature || 0.7),
                num_predict: parseInt(maxTokens || 1000)
              }
            };

            logDebug('Données de la requête de génération', generateRequestData);
            logDebug(`Appel de l'API de génération: ${ollamaApiUrl}/api/generate`);
            const generateResponse = await axios.post(`${ollamaApiUrl}/api/generate`, generateRequestData, { timeout: 30000 });
            logDebug('Réponse reçue de l\'API de génération', generateResponse.data);

            // Convertir la réponse de l'API de génération au format de l'API de chat
            const finalResponse = {
              message: {
                role: 'assistant',
                content: generateResponse.data.response || "Pas de réponse"
              }
            };

            logDebug('Réponse finale (génération)', finalResponse);
            socket.emit('chat response', finalResponse);
          } catch (generateError) {
            logDebug('Erreur avec l\'API de génération', generateError.message);

            // Si les deux APIs échouent, créer une réponse simulée
            const errorResponse = {
              message: {
                role: 'assistant',
                content: `Je suis désolé, mais je ne peux pas traiter votre demande pour le moment. Erreur: ${chatError.message}`
              }
            };
            logDebug('Envoi d\'une réponse d\'erreur', errorResponse);
            socket.emit('chat response', errorResponse);
          }
        }
      } catch (error) {
        logDebug('Erreur lors de l\'appel à l\'API Ollama', error.message);

        const errorResponse = {
          message: {
            role: 'assistant',
            content: `Erreur lors de la communication avec Ollama: ${error.message}`
          }
        };
        logDebug('Envoi d\'une réponse d\'erreur', errorResponse);
        socket.emit('chat response', errorResponse);
      }
    } catch (error) {
      logDebug('Erreur lors du traitement du message', error);

      const errorResponse = {
        message: {
          role: 'assistant',
          content: `Une erreur s'est produite: ${error.message}`
        }
      };
      logDebug('Envoi d\'une réponse d\'erreur', errorResponse);
      socket.emit('chat response', errorResponse);
    }
  });

  // Gérer la déconnexion
  socket.on('disconnect', () => {
    logDebug('Client déconnecté', { socketId: socket.id });
  });
});

// Démarrer le serveur
async function startServer() {
  await initDebugLog();
  
  server.listen(PORT, () => {
    logDebug(`Serveur démarré sur http://localhost:${PORT}`);
  });
}

// Démarrer le serveur
startServer();
