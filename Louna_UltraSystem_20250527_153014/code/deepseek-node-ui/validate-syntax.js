/**
 * Script de validation de syntaxe pour les fichiers JavaScript
 * Ce script vérifie la syntaxe des fichiers JavaScript avant de démarrer le serveur
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Liste des fichiers critiques à vérifier
const criticalFiles = [
  'server-luna.js',
  'routes/luna.js',
  'routes/luna-models.js',
  'routes/luna-memory.js',
  'routes/luna-prompts.js',
  'routes/luna-mcp.js',
  'routes/reflection-accelerators.js',
  'public/js/luna-interface.js'
];

console.log('🔍 Validation de la syntaxe des fichiers JavaScript...');

let hasErrors = false;

// Vérifier chaque fichier
criticalFiles.forEach(file => {
  const filePath = path.join(__dirname, file);

  // Vérifier si le fichier existe
  if (!fs.existsSync(filePath)) {
    console.error(`❌ Erreur: Le fichier ${file} n'existe pas.`);
    hasErrors = true;
    return;
  }

  try {
    // Utiliser Node.js pour vérifier la syntaxe du fichier
    execSync(`node --check "${filePath}"`, { stdio: 'pipe' });
    console.log(`✅ ${file}: Syntaxe valide`);
  } catch (error) {
    console.error(`❌ ${file}: Erreur de syntaxe`);
    console.error(error.stderr.toString());
    hasErrors = true;
  }
});

// Vérifier le nombre d'accolades ouvrantes et fermantes dans server-luna.js
try {
  const serverLunaPath = path.join(__dirname, 'server-luna.js');
  const content = fs.readFileSync(serverLunaPath, 'utf8');

  const openBraces = (content.match(/{/g) || []).length;
  const closeBraces = (content.match(/}/g) || []).length;

  console.log(`📊 server-luna.js: ${openBraces} accolades ouvrantes, ${closeBraces} accolades fermantes`);

  if (openBraces !== closeBraces) {
    console.error(`❌ Erreur: Le nombre d'accolades ouvrantes (${openBraces}) ne correspond pas au nombre d'accolades fermantes (${closeBraces}) dans server-luna.js`);
    hasErrors = true;
  } else {
    console.log("✅ Le nombre d'accolades est équilibré dans server-luna.js");
  }
} catch (error) {
  console.error('❌ Erreur lors de la vérification des accolades:', error.message);
  hasErrors = true;
}

// Sortir avec un code d'erreur si des problèmes ont été détectés
if (hasErrors) {
  console.error('❌ Des erreurs de syntaxe ont été détectées. Veuillez les corriger avant de démarrer le serveur.');
  process.exit(1);
} else {
  console.log('✅ Tous les fichiers ont passé la validation de syntaxe.');
}
