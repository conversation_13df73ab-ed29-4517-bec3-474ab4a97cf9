const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');

// Configuration
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  },
  transports: ['websocket', 'polling']
});
const PORT = process.env.PORT || 3000;

// URL de l'API Ollama
const OLLAMA_API_URL = 'http://localhost:11434/api';

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Configuration du moteur de template
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Route principale
app.get('/', (req, res) => {
  res.render('index-simple', {
    title: 'DeepSeek r1 - Version Corrigée'
  });
});

// Gestion des connexions Socket.io
io.on('connection', (socket) => {
  console.log('Nouveau client connecté avec ID:', socket.id);

  // Vérification du statut d'Ollama
  socket.on('check ollama', async () => {
    try {
      const response = await axios.get(`${OLLAMA_API_URL}/version`);
      socket.emit('ollama status', { available: true, version: response.data.version });
      console.log('Ollama est disponible:', response.data);
      
      // Récupérer les modèles disponibles
      try {
        const modelsResponse = await axios.get(`${OLLAMA_API_URL}/tags`);
        console.log('Modèles disponibles:', modelsResponse.data);
        socket.emit('models', modelsResponse.data.models || []);
      } catch (err) {
        console.error('Erreur lors de la récupération des modèles:', err.message);
      }
    } catch (error) {
      console.error('Erreur lors de la vérification d\'Ollama:', error.message);
      socket.emit('ollama status', { available: false, error: error.message });
    }
  });

  // Traitement des messages
  socket.on('chat message', async (data) => {
    console.log('Message reçu du client:', data);
    
    try {
      socket.emit('processing', true);
      
      // Utiliser exactement le même format que la commande curl qui a fonctionné
      const requestData = {
        model: "deepseek-r1:7b", // Utiliser toujours ce modèle car nous savons qu'il fonctionne
        prompt: data.message
      };
      
      if (data.temperature) {
        requestData.options = {
          temperature: parseFloat(data.temperature)
        };
      }
      
      console.log('Envoi de la requête à Ollama:', JSON.stringify(requestData));
      
      // Appeler l'API Ollama avec exactement le même format que la commande curl
      const response = await axios.post(`${OLLAMA_API_URL}/generate`, requestData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      // Extraire la réponse (la dernière ligne contient done:true et le texte complet)
      const responseLines = response.data.toString().split('\n').filter(line => line.trim() !== '');
      
      // Trouver la dernière ligne avec "done":true
      let fullResponse = '';
      let lastResponse = null;
      
      for (const line of responseLines) {
        try {
          const parsedLine = JSON.parse(line);
          if (parsedLine.response) {
            fullResponse += parsedLine.response;
          }
          lastResponse = parsedLine;
        } catch (e) {
          console.warn('Impossible de parser la ligne:', line);
        }
      }
      
      console.log('Réponse complète extraite:', fullResponse);
      
      // Envoyer la réponse au client
      socket.emit('chat response', {
        message: {
          role: 'assistant',
          content: fullResponse
        }
      });
      
      socket.emit('processing', false);
    } catch (error) {
      console.error('Erreur lors du traitement du message:', error);
      
      // Plus de détails sur l'erreur
      if (error.response) {
        console.error('Statut de la réponse:', error.response.status);
        console.error('Données de la réponse:', error.response.data);
      }
      
      socket.emit('chat response', {
        message: {
          role: 'assistant',
          content: `Erreur lors du traitement de votre message: ${error.message}`
        }
      });
      
      socket.emit('processing', false);
    }
  });

  // Gestion de la déconnexion
  socket.on('disconnect', () => {
    console.log('Client déconnecté:', socket.id);
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`Serveur démarré sur http://localhost:${PORT}`);
  console.log('Version corrigée du serveur DeepSeek r1');
});
