/**
 * Module principal du système cognitif pour DeepSeek
 * Intègre la parole, la vision et le traitement de données dans un agent cognitif complet
 */

const path = require('path');
const fs = require('fs');
const EventEmitter = require('events');
const SpeechProcessor = require('./speech-processor');
const SensorySystem = require('./sensory-system');

class CognitiveAgent extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      name: options.name || 'Assistant',
      language: options.language || 'fr-FR',
      voiceName: options.voiceName || 'French',
      responseDelay: options.responseDelay || 300, // D<PERSON><PERSON> avant réponse (ms)
      memoryPath: options.memoryPath || path.join(__dirname, 'memory'),
      thermalMemory: options.thermalMemory || null,
      debugMode: options.debugMode || false
    };
    
    // Créer le dossier de mémoire
    if (!fs.existsSync(this.options.memoryPath)) {
      fs.mkdirSync(this.options.memoryPath, { recursive: true });
    }
    
    // Initialiser les sous-systèmes
    this.speech = new SpeechProcessor({
      language: this.options.language,
      voiceName: this.options.voiceName,
      debug: this.options.debugMode
    });
    
    this.sensory = new SensorySystem({
      debug: this.options.debugMode
    });
    
    // État cognitif
    this.cognitiveState = {
      isActive: false,
      isListening: false,
      isSpeaking: false,
      isObserving: false,
      lastUserInput: null,
      lastResponse: null,
      lastObservation: null,
      conversationContext: [],
      startTime: new Date(),
      shortTermMemory: [],
      workingMemory: {},
      attentionFocus: null
    };
    
    // État émotionnel (pour personnalité)
    this.emotionalState = {
      mood: 'neutral', // neutral, happy, concerned, focused, thoughtful
      energy: 0.7,     // 0-1
      engagement: 0.8, // 0-1
      confidence: 0.9  // 0-1
    };
    
    // Initialiser les gestionnaires d'événements
    this.initEventHandlers();
    
    console.log(`Agent cognitif ${this.options.name} initialisé (langue: ${this.options.language})`);
  }
  
  // Initialiser les gestionnaires d'événements
  initEventHandlers() {
    // Événements de parole
    this.speech.on('recognitionResult', (text) => {
      this.log(`Parole reconnue: "${text}"`);
      this.cognitiveState.lastUserInput = text;
      this.cognitiveState.conversationContext.push({ role: 'user', content: text });
      
      // Limiter la taille du contexte de conversation
      if (this.cognitiveState.conversationContext.length > 10) {
        this.cognitiveState.conversationContext.shift();
      }
      
      // Ajouter à la mémoire à court terme
      this.addToShortTermMemory({
        type: 'user_input',
        content: text,
        timestamp: new Date().toISOString()
      });
      
      // Traiter l'entrée utilisateur
      this.processUserInput(text);
      
      this.emit('userInputProcessed', text);
    });
    
    this.speech.on('speakingStarted', (text) => {
      this.cognitiveState.isSpeaking = true;
      this.emit('speakingStarted', text);
    });
    
    this.speech.on('speakingDone', (text) => {
      this.cognitiveState.isSpeaking = false;
      this.emit('speakingDone', text);
    });
    
    // Événements sensoriels
    this.sensory.on('imageCaptured', (result) => {
      this.log('Image capturée:', result.file);
      this.emit('imageCaptured', result);
    });
    
    this.sensory.on('analysisResult', (result) => {
      this.cognitiveState.lastObservation = result;
      this.cognitiveState.isObserving = false;
      
      // Ajouter à la mémoire à court terme
      this.addToShortTermMemory({
        type: 'observation',
        content: result,
        timestamp: new Date().toISOString()
      });
      
      this.log('Analyse d\'image complétée');
      this.emit('observationComplete', result);
    });
  }
  
  // Activer l'agent cognitif
  activate() {
    if (this.cognitiveState.isActive) {
      this.log('Agent déjà actif');
      return false;
    }
    
    this.cognitiveState.isActive = true;
    this.cognitiveState.startTime = new Date();
    
    // Annoncer l'activation
    const greeting = `${this.options.name} activé. Système sensoriel et vocal initialisés.`;
    setTimeout(() => {
      this.speak(greeting);
    }, 500);
    
    this.log('Agent cognitif activé');
    this.emit('activated');
    return true;
  }
  
  // Désactiver l'agent cognitif
  deactivate() {
    if (!this.cognitiveState.isActive) {
      this.log('Agent déjà inactif');
      return false;
    }
    
    // Arrêter toutes les activités en cours
    if (this.cognitiveState.isListening) {
      this.stopListening();
    }
    
    if (this.cognitiveState.isObserving) {
      this.sensory.stopContinuousDetection();
      this.cognitiveState.isObserving = false;
    }
    
    this.cognitiveState.isActive = false;
    
    // Annoncer la désactivation
    const farewell = `${this.options.name} désactivé. Au revoir.`;
    this.speak(farewell);
    
    this.log('Agent cognitif désactivé');
    this.emit('deactivated');
    return true;
  }
  
  // Commencer à écouter
  startListening() {
    if (!this.cognitiveState.isActive) {
      this.log('L\'agent doit être activé avant d\'écouter');
      return false;
    }
    
    this.cognitiveState.isListening = true;
    const result = this.speech.startListening();
    
    if (result) {
      this.emit('listeningStarted');
    } else {
      this.cognitiveState.isListening = false;
    }
    
    return result;
  }
  
  // Arrêter d'écouter
  stopListening() {
    if (!this.cognitiveState.isListening) {
      this.log('L\'agent n\'est pas en train d\'écouter');
      return false;
    }
    
    const result = this.speech.stopListening();
    this.cognitiveState.isListening = false;
    
    if (result) {
      this.emit('listeningStopped');
    }
    
    return result;
  }
  
  // Parler (synthèse vocale)
  speak(text) {
    if (!this.cognitiveState.isActive) {
      this.log('L\'agent doit être activé avant de parler');
      return false;
    }
    
    // Ajouter au contexte de conversation
    this.cognitiveState.conversationContext.push({ role: 'assistant', content: text });
    
    // Limiter la taille du contexte de conversation
    if (this.cognitiveState.conversationContext.length > 10) {
      this.cognitiveState.conversationContext.shift();
    }
    
    // Ajouter à la mémoire à court terme
    this.addToShortTermMemory({
      type: 'agent_response',
      content: text,
      timestamp: new Date().toISOString()
    });
    
    this.cognitiveState.lastResponse = text;
    return this.speech.speak(text);
  }
  
  // Observer l'environnement
  observe() {
    if (!this.cognitiveState.isActive) {
      this.log('L\'agent doit être activé avant d\'observer');
      return false;
    }
    
    this.cognitiveState.isObserving = true;
    const result = this.sensory.captureImage();
    
    if (result) {
      this.emit('observationStarted');
    } else {
      this.cognitiveState.isObserving = false;
    }
    
    return result;
  }
  
  // Commencer la détection continue
  startContinuousObservation(interval = 5000) {
    if (!this.cognitiveState.isActive) {
      this.log('L\'agent doit être activé avant de démarrer l\'observation continue');
      return false;
    }
    
    this.cognitiveState.isObserving = true;
    const result = this.sensory.startContinuousDetection(interval);
    
    if (result) {
      this.emit('continuousObservationStarted', interval);
    } else {
      this.cognitiveState.isObserving = false;
    }
    
    return result;
  }
  
  // Arrêter la détection continue
  stopContinuousObservation() {
    if (!this.cognitiveState.isObserving) {
      this.log('L\'agent n\'est pas en observation continue');
      return false;
    }
    
    const result = this.sensory.stopContinuousDetection();
    this.cognitiveState.isObserving = false;
    
    if (result) {
      this.emit('continuousObservationStopped');
    }
    
    return result;
  }
  
  // Traiter l'entrée utilisateur
  processUserInput(text) {
    if (!text || typeof text !== 'string') {
      return;
    }
    
    // Définir le focus d'attention sur cette entrée
    this.cognitiveState.attentionFocus = {
      type: 'user_input',
      content: text,
      timestamp: new Date().toISOString()
    };
    
    // Simuler un délai de réflexion
    setTimeout(() => {
      let response = "";
      
      // Commandes de base (simulation)
      if (text.toLowerCase().includes('activer') || text.toLowerCase().includes('démarrer')) {
        response = "Activation complète des systèmes sensoriels.";
        setTimeout(() => this.startContinuousObservation(10000), 1000);
      } 
      else if (text.toLowerCase().includes('désactiver') || text.toLowerCase().includes('arrêter')) {
        response = "Désactivation des systèmes sensoriels.";
        setTimeout(() => this.stopContinuousObservation(), 1000);
      }
      else if (text.toLowerCase().includes('observer') || text.toLowerCase().includes('regarde')) {
        response = "J'observe l'environnement.";
        setTimeout(() => this.observe(), 500);
      }
      else if (text.toLowerCase().includes('décrire') || text.toLowerCase().includes('que vois-tu')) {
        if (this.cognitiveState.lastObservation) {
          response = this.sensory.describeEnvironment();
        } else {
          response = "Je n'ai pas encore d'observation récente. Je vais regarder maintenant.";
          setTimeout(() => this.observe(), 500);
        }
      }
      else {
        // Si intégré avec la mémoire thermique
        if (this.options.thermalMemory) {
          response = "J'ai enregistré votre message dans ma mémoire thermique. Voulez-vous que j'observe l'environnement également ?";
          
          // Enregistrer dans la mémoire thermique
          try {
            const memoryContent = {
              type: 'conversation',
              user: text,
              assistant: response,
              timestamp: new Date().toISOString()
            };
            
            // Ajouter sous forme de document (simulation)
            console.log('[CognitiveAgent] Document ajouté à la mémoire thermique:', memoryContent);
          } catch (error) {
            this.log('Erreur lors de l\'enregistrement dans la mémoire thermique:', error);
          }
        } else {
          response = "Je comprends votre message. Je peux observer l'environnement si vous le souhaitez.";
        }
      }
      
      // Répondre vocalement
      this.speak(response);
      
    }, this.options.responseDelay);
  }
  
  // Ajouter à la mémoire à court terme
  addToShortTermMemory(item) {
    this.cognitiveState.shortTermMemory.push(item);
    
    // Limiter la taille de la mémoire à court terme
    if (this.cognitiveState.shortTermMemory.length > 20) {
      this.cognitiveState.shortTermMemory.shift();
    }
    
    // Si la mémoire thermique est disponible, y stocker également
    if (this.options.thermalMemory && this.options.thermalMemory.documentProcessor) {
      // Simulation d'ajout à la mémoire thermique
      this.log('Item ajouté à la mémoire thermique:', item.type);
    }
  }
  
  // Obtenir l'état cognitif actuel
  getCognitiveState() {
    return {
      ...this.cognitiveState,
      uptime: new Date() - this.cognitiveState.startTime,
      memorySize: this.cognitiveState.shortTermMemory.length,
      emotionalState: this.emotionalState
    };
  }
  
  // Utilitaire de log
  log(message, details = '') {
    if (this.options.debugMode) {
      console.log(`[CognitiveAgent] ${message}`, details);
    }
  }
}

module.exports = CognitiveAgent;