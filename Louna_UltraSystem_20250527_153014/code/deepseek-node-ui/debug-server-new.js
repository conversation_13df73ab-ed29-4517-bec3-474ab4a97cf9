const express = require('express');
const app = express();
const PORT = 3001;

// Ajouter un middleware de journalisation
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  next();
});

// Route principale
app.get('/', (req, res) => {
  console.log('Route principale appelée');
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Serveur de débogage</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .btn { display: inline-block; background: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>Serveur de débogage Luna</h1>
        <p>Ce serveur fonctionne correctement sur le port ${PORT}.</p>
        <p><a href="/luna" class="btn">Accéder à l'interface Luna</a></p>
      </div>
    </body>
    </html>
  `);
});

// Route pour Luna
app.get('/luna', (req, res) => {
  console.log('Route Luna appelée');
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Interface Luna (Debug)</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; background: #1a1a2e; color: #edf2fb; }
        h1 { color: #9c89b8; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: rgba(26, 26, 46, 0.7); border: 1px solid rgba(184, 190, 221, 0.2); border-radius: 15px; padding: 20px; margin-bottom: 20px; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>Interface Luna (Version de débogage)</h1>
        <div class="card">
          <h2>Statut du serveur</h2>
          <p>Le serveur de débogage fonctionne correctement.</p>
          <p>Cette page est une version simplifiée de l'interface Luna pour le débogage.</p>
        </div>
      </div>
    </body>
    </html>
  `);
});

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`\n\n=== SERVEUR DE DÉBOGAGE DÉMARRÉ ===`);
  console.log(`Serveur de débogage démarré sur http://localhost:${PORT}`);
  console.log(`=== SERVEUR DE DÉBOGAGE PRÊT ===\n\n`);
});
