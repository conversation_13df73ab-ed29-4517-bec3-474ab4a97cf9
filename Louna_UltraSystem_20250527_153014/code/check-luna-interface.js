/**
 * Script pour vérifier si l'interface Luna est correctement chargée
 * Utilise puppeteer pour ouvrir l'interface Luna dans un navigateur headless
 */

const puppeteer = require('puppeteer');

async function checkLunaInterface() {
  console.log('Vérification de l\'interface Luna...');
  
  // Lancer un navigateur headless
  const browser = await puppeteer.launch({
    headless: false, // Mettre à true pour un mode headless
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    // Ouvrir une nouvelle page
    const page = await browser.newPage();
    
    // Configurer la taille de la fenêtre
    await page.setViewport({ width: 1280, height: 800 });
    
    // Naviguer vers l'interface Luna
    console.log('Accès à l\'interface Luna sur http://localhost:3010/luna...');
    await page.goto('http://localhost:3010/luna', { waitUntil: 'networkidle2', timeout: 30000 });
    
    // Attendre que la page soit chargée
    await page.waitForSelector('.luna-card', { timeout: 10000 });
    
    // Vérifier si l'interface est correctement chargée
    const title = await page.title();
    console.log(`Titre de la page: ${title}`);
    
    // Vérifier si les éléments principaux sont présents
    const hasConversationContainer = await page.evaluate(() => {
      return !!document.getElementById('conversation-container');
    });
    
    const hasUserInput = await page.evaluate(() => {
      return !!document.getElementById('user-input');
    });
    
    const hasSendButton = await page.evaluate(() => {
      return !!document.getElementById('send-button');
    });
    
    console.log(`Conteneur de conversation présent: ${hasConversationContainer}`);
    console.log(`Champ de saisie présent: ${hasUserInput}`);
    console.log(`Bouton d'envoi présent: ${hasSendButton}`);
    
    // Prendre une capture d'écran
    await page.screenshot({ path: 'luna-interface.png' });
    console.log('Capture d\'écran enregistrée dans luna-interface.png');
    
    // Vérifier si les fichiers CSS sont correctement chargés
    const cssLoaded = await page.evaluate(() => {
      const styles = document.querySelectorAll('link[rel="stylesheet"]');
      return Array.from(styles).map(style => style.href);
    });
    
    console.log('Fichiers CSS chargés:');
    cssLoaded.forEach(css => console.log(`- ${css}`));
    
    // Vérifier si les fichiers JavaScript sont correctement chargés
    const jsLoaded = await page.evaluate(() => {
      const scripts = document.querySelectorAll('script[src]');
      return Array.from(scripts).map(script => script.src);
    });
    
    console.log('Fichiers JavaScript chargés:');
    jsLoaded.forEach(js => console.log(`- ${js}`));
    
    // Vérifier si Socket.IO est correctement chargé
    const socketIOLoaded = await page.evaluate(() => {
      return typeof io !== 'undefined';
    });
    
    console.log(`Socket.IO chargé: ${socketIOLoaded}`);
    
    // Résultat final
    if (hasConversationContainer && hasUserInput && hasSendButton && socketIOLoaded) {
      console.log('✅ L\'interface Luna est correctement chargée!');
    } else {
      console.log('❌ L\'interface Luna n\'est pas correctement chargée!');
    }
    
  } catch (error) {
    console.error('Erreur lors de la vérification de l\'interface Luna:', error);
  } finally {
    // Fermer le navigateur
    await browser.close();
  }
}

// Exécuter la fonction
checkLunaInterface().catch(console.error);
