/**
 * Script pour établir un lien direct avec la mémoire thermique
 * Ce script permet de tester facilement la mémoire thermique sans passer par l'interface web
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Chemin vers le fichier de mémoire thermique
const MEMORY_FILE = path.join(__dirname, 'data/memory/thermal_memory.json');

// Classe pour gérer la mémoire thermique
class ThermalMemory {
  constructor(filePath) {
    this.filePath = filePath;
    this.memory = { memories: [] };
    this.load();
  }

  // Charger la mémoire depuis le fichier
  load() {
    try {
      if (fs.existsSync(this.filePath)) {
        const data = fs.readFileSync(this.filePath, 'utf8');
        this.memory = JSON.parse(data);
        console.log(`Mémoire thermique chargée: ${this.memory.memories.length} entrées`);
      } else {
        console.log('Fichier de mémoire thermique non trouvé, création d\'une nouvelle mémoire');
        this.memory = { memories: [] };
        this.save();
      }
    } catch (error) {
      console.error(`Erreur lors du chargement de la mémoire thermique: ${error.message}`);
      this.memory = { memories: [] };
    }
  }

  // Sauvegarder la mémoire dans le fichier
  save() {
    try {
      fs.writeFileSync(this.filePath, JSON.stringify(this.memory, null, 2), 'utf8');
      console.log('Mémoire thermique sauvegardée');
    } catch (error) {
      console.error(`Erreur lors de la sauvegarde de la mémoire thermique: ${error.message}`);
    }
  }

  // Ajouter une entrée à la mémoire
  addEntry(entry) {
    // Ajouter un identifiant unique et un timestamp si nécessaire
    const newEntry = {
      id: `memory_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      timestamp: new Date().toISOString(),
      ...entry
    };

    this.memory.memories.push(newEntry);
    this.save();
    return newEntry;
  }

  // Récupérer toutes les entrées de la mémoire
  getAllEntries() {
    return this.memory.memories;
  }

  // Récupérer les entrées récentes pour le contexte
  getRecentMemoriesForContext(count = 8) {
    return this.memory.memories
      .sort((a, b) => new Date(b.timestamp || b.created || 0) - new Date(a.timestamp || a.created || 0))
      .slice(0, count);
  }

  // Récupérer les entrées d'une zone spécifique
  getEntriesFromZone(zone) {
    return this.memory.memories.filter(entry => entry.zone === zone);
  }

  // Rechercher des entrées par mot-clé
  searchEntries(keyword) {
    const lowerKeyword = keyword.toLowerCase();
    return this.memory.memories.filter(entry => {
      // Rechercher dans le contenu
      if (entry.content && entry.content.toLowerCase().includes(lowerKeyword)) {
        return true;
      }
      
      // Rechercher dans les messages
      if (entry.messages && entry.messages.length > 0) {
        return entry.messages.some(msg => 
          msg.content && msg.content.toLowerCase().includes(lowerKeyword)
        );
      }
      
      return false;
    });
  }

  // Supprimer une entrée par ID
  deleteEntry(id) {
    const initialLength = this.memory.memories.length;
    this.memory.memories = this.memory.memories.filter(entry => entry.id !== id);
    
    if (this.memory.memories.length < initialLength) {
      this.save();
      return true;
    }
    
    return false;
  }

  // Nettoyer la mémoire (supprimer les entrées plus anciennes qu'une certaine date)
  cleanMemory(daysToKeep = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const initialLength = this.memory.memories.length;
    this.memory.memories = this.memory.memories.filter(entry => {
      const entryDate = new Date(entry.timestamp || entry.created || 0);
      return entryDate >= cutoffDate;
    });
    
    if (this.memory.memories.length < initialLength) {
      this.save();
      console.log(`Mémoire nettoyée: ${initialLength - this.memory.memories.length} entrées supprimées`);
      return true;
    }
    
    return false;
  }
}

// Créer une instance de la mémoire thermique
const thermalMemory = new ThermalMemory(MEMORY_FILE);

// Interface en ligne de commande
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Afficher le menu
function showMenu() {
  console.log('\n=== LIEN DIRECT AVEC LA MÉMOIRE THERMIQUE ===');
  console.log('1. Afficher toutes les entrées');
  console.log('2. Ajouter une entrée');
  console.log('3. Rechercher des entrées');
  console.log('4. Supprimer une entrée');
  console.log('5. Nettoyer la mémoire');
  console.log('6. Afficher les entrées d\'une zone spécifique');
  console.log('7. Afficher les entrées récentes');
  console.log('8. Tester la récupération du contexte');
  console.log('9. Quitter');
  
  rl.question('\nChoisissez une option: ', (answer) => {
    switch (answer) {
      case '1':
        showAllEntries();
        break;
      case '2':
        addEntry();
        break;
      case '3':
        searchEntries();
        break;
      case '4':
        deleteEntry();
        break;
      case '5':
        cleanMemory();
        break;
      case '6':
        showEntriesFromZone();
        break;
      case '7':
        showRecentEntries();
        break;
      case '8':
        testContextRetrieval();
        break;
      case '9':
        console.log('Au revoir!');
        rl.close();
        break;
      default:
        console.log('Option invalide');
        showMenu();
        break;
    }
  });
}

// Afficher toutes les entrées
function showAllEntries() {
  const entries = thermalMemory.getAllEntries();
  console.log(`\n=== TOUTES LES ENTRÉES (${entries.length}) ===`);
  
  if (entries.length === 0) {
    console.log('Aucune entrée trouvée');
    showMenu();
    return;
  }
  
  entries.forEach((entry, index) => {
    console.log(`\n--- Entrée ${index + 1} ---`);
    console.log(`ID: ${entry.id}`);
    console.log(`Timestamp: ${entry.timestamp || entry.created || 'N/A'}`);
    console.log(`Zone: ${entry.zone || 'N/A'}`);
    console.log(`Type: ${entry.type || 'N/A'}`);
    
    if (entry.content) {
      console.log(`Contenu: ${entry.content.substring(0, 100)}${entry.content.length > 100 ? '...' : ''}`);
    } else if (entry.messages && entry.messages.length > 0) {
      console.log(`Messages: ${entry.messages.length}`);
      entry.messages.forEach((msg, i) => {
        if (i < 2) { // Limiter à 2 messages pour la lisibilité
          console.log(`  [${msg.role}]: ${msg.content.substring(0, 50)}${msg.content.length > 50 ? '...' : ''}`);
        }
      });
      if (entry.messages.length > 2) {
        console.log(`  ... et ${entry.messages.length - 2} autres messages`);
      }
    }
  });
  
  showMenu();
}

// Ajouter une entrée
function addEntry() {
  rl.question('\nType d\'entrée (user_identity, assistant_identity, important_fact, conversation): ', (type) => {
    rl.question('Zone (1-6, 1=instantanée, 6=long terme): ', (zoneStr) => {
      const zone = parseInt(zoneStr);
      
      if (type === 'conversation') {
        rl.question('Message utilisateur: ', (userMessage) => {
          rl.question('Réponse assistant: ', (assistantMessage) => {
            const entry = {
              type: 'conversation',
              zone: zone || 1,
              messages: [
                { role: 'user', content: userMessage },
                { role: 'assistant', content: assistantMessage }
              ]
            };
            
            const newEntry = thermalMemory.addEntry(entry);
            console.log(`Entrée ajoutée avec l'ID: ${newEntry.id}`);
            showMenu();
          });
        });
      } else {
        rl.question('Contenu: ', (content) => {
          const entry = {
            type: type || 'important_fact',
            zone: zone || 1,
            content: content
          };
          
          const newEntry = thermalMemory.addEntry(entry);
          console.log(`Entrée ajoutée avec l'ID: ${newEntry.id}`);
          showMenu();
        });
      }
    });
  });
}

// Rechercher des entrées
function searchEntries() {
  rl.question('\nMot-clé à rechercher: ', (keyword) => {
    const entries = thermalMemory.searchEntries(keyword);
    console.log(`\n=== RÉSULTATS DE RECHERCHE (${entries.length}) ===`);
    
    if (entries.length === 0) {
      console.log('Aucune entrée trouvée');
      showMenu();
      return;
    }
    
    entries.forEach((entry, index) => {
      console.log(`\n--- Résultat ${index + 1} ---`);
      console.log(`ID: ${entry.id}`);
      console.log(`Timestamp: ${entry.timestamp || entry.created || 'N/A'}`);
      console.log(`Zone: ${entry.zone || 'N/A'}`);
      console.log(`Type: ${entry.type || 'N/A'}`);
      
      if (entry.content) {
        console.log(`Contenu: ${entry.content}`);
      } else if (entry.messages && entry.messages.length > 0) {
        console.log(`Messages:`);
        entry.messages.forEach(msg => {
          console.log(`  [${msg.role}]: ${msg.content}`);
        });
      }
    });
    
    showMenu();
  });
}

// Supprimer une entrée
function deleteEntry() {
  rl.question('\nID de l\'entrée à supprimer: ', (id) => {
    const success = thermalMemory.deleteEntry(id);
    
    if (success) {
      console.log(`Entrée ${id} supprimée avec succès`);
    } else {
      console.log(`Entrée ${id} non trouvée`);
    }
    
    showMenu();
  });
}

// Nettoyer la mémoire
function cleanMemory() {
  rl.question('\nNombre de jours à conserver (30 par défaut): ', (daysStr) => {
    const days = parseInt(daysStr) || 30;
    const success = thermalMemory.cleanMemory(days);
    
    if (success) {
      console.log(`Mémoire nettoyée, entrées de plus de ${days} jours supprimées`);
    } else {
      console.log('Aucune entrée à nettoyer');
    }
    
    showMenu();
  });
}

// Afficher les entrées d'une zone spécifique
function showEntriesFromZone() {
  rl.question('\nZone à afficher (1-6): ', (zoneStr) => {
    const zone = parseInt(zoneStr);
    
    if (isNaN(zone) || zone < 1 || zone > 6) {
      console.log('Zone invalide');
      showMenu();
      return;
    }
    
    const entries = thermalMemory.getEntriesFromZone(zone);
    console.log(`\n=== ENTRÉES DE LA ZONE ${zone} (${entries.length}) ===`);
    
    if (entries.length === 0) {
      console.log('Aucune entrée trouvée dans cette zone');
      showMenu();
      return;
    }
    
    entries.forEach((entry, index) => {
      console.log(`\n--- Entrée ${index + 1} ---`);
      console.log(`ID: ${entry.id}`);
      console.log(`Timestamp: ${entry.timestamp || entry.created || 'N/A'}`);
      console.log(`Type: ${entry.type || 'N/A'}`);
      
      if (entry.content) {
        console.log(`Contenu: ${entry.content}`);
      } else if (entry.messages && entry.messages.length > 0) {
        console.log(`Messages:`);
        entry.messages.forEach(msg => {
          console.log(`  [${msg.role}]: ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`);
        });
      }
    });
    
    showMenu();
  });
}

// Afficher les entrées récentes
function showRecentEntries() {
  rl.question('\nNombre d\'entrées à afficher (8 par défaut): ', (countStr) => {
    const count = parseInt(countStr) || 8;
    const entries = thermalMemory.getRecentMemoriesForContext(count);
    
    console.log(`\n=== ENTRÉES RÉCENTES (${entries.length}) ===`);
    
    if (entries.length === 0) {
      console.log('Aucune entrée trouvée');
      showMenu();
      return;
    }
    
    entries.forEach((entry, index) => {
      console.log(`\n--- Entrée ${index + 1} ---`);
      console.log(`ID: ${entry.id}`);
      console.log(`Timestamp: ${entry.timestamp || entry.created || 'N/A'}`);
      console.log(`Zone: ${entry.zone || 'N/A'}`);
      console.log(`Type: ${entry.type || 'N/A'}`);
      
      if (entry.content) {
        console.log(`Contenu: ${entry.content}`);
      } else if (entry.messages && entry.messages.length > 0) {
        console.log(`Messages:`);
        entry.messages.forEach(msg => {
          console.log(`  [${msg.role}]: ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`);
        });
      }
    });
    
    showMenu();
  });
}

// Tester la récupération du contexte
function testContextRetrieval() {
  console.log('\n=== TEST DE RÉCUPÉRATION DU CONTEXTE ===');
  
  // Simuler la récupération du contexte comme dans le serveur
  const entries = thermalMemory.getRecentMemoriesForContext(8);
  
  if (entries.length === 0) {
    console.log('Aucune entrée trouvée pour le contexte');
    showMenu();
    return;
  }
  
  // Filtrer les entrées pertinentes
  const relevantEntries = entries.filter(entry => 
    entry.type === 'user_identity' || 
    entry.type === 'assistant_identity' ||
    entry.type === 'important_fact' ||
    entry.type === 'user_input' ||
    entry.type === 'assistant_output' ||
    entry.zone === 1 || // Zone instantanée
    entry.zone === 2 || // Zone court terme
    entry.zone === 6    // Zone long terme
  );
  
  // Construire le contexte
  const memoryContext = '=== MÉMOIRE THERMIQUE ===\n' +
    relevantEntries.map(entry => {
      // Extraire le contenu en fonction du format de l'entrée
      if (entry.messages && entry.messages.length > 0) {
        return entry.messages.map(msg => `[${msg.role}] ${msg.content}`).join('\n');
      } else if (entry.content) {
        return entry.content;
      } else {
        return JSON.stringify(entry);
      }
    }).join('\n\n') +
    '\n=== FIN DE LA MÉMOIRE THERMIQUE ===\n\n' +
    'INSTRUCTIONS IMPORTANTES:\n\n' +
    'Vous êtes Vision Ultra, une assistante IA.\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Comment je m\'appelle?", vous devez répondre EXACTEMENT: "Vous vous appelez Jean-Luc Passave."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Où j\'habite?", vous devez répondre EXACTEMENT: "Vous habitez à Sainte-Anne en Guadeloupe (97180)."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Quelle est mon origine?", vous devez répondre EXACTEMENT: "Vous êtes d\'origine africaine."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Qui est ton créateur?", vous devez répondre EXACTEMENT: "Vous, Jean-Luc Passave, êtes mon créateur."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Qui suis-je?", vous devez répondre EXACTEMENT: "Vous êtes Jean-Luc Passave, vous habitez à Sainte-Anne en Guadeloupe (97180) et vous êtes d\'origine africaine."\n\n' +
    'IMPORTANT: Quand l\'utilisateur demande "Comment tu t\'appelles?", vous devez répondre EXACTEMENT: "Je m\'appelle Vision Ultra."\n\n' +
    'Ne modifiez pas ces réponses. Ne donnez pas d\'explications supplémentaires.';
  
  console.log('\nContexte généré:');
  console.log('----------------------------');
  console.log(memoryContext);
  console.log('----------------------------');
  
  showMenu();
}

// Démarrer l'application
console.log('Bienvenue dans le lien direct avec la mémoire thermique');
showMenu();
