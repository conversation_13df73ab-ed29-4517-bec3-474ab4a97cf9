<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test des Interfaces</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }

    h1, h2 {
      color: #333;
      text-align: center;
    }

    .interfaces-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
      margin-bottom: 30px;
    }

    .interface {
      flex: 1;
      min-width: 300px;
      max-width: 400px;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }

    .interface-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    .interface-title {
      font-size: 1.2rem;
      font-weight: bold;
      margin: 0;
    }

    .status {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 5px;
    }

    .status.connected {
      background-color: #4CAF50;
    }

    .status.disconnected {
      background-color: #F44336;
    }

    .chat-container {
      height: 300px;
      overflow-y: auto;
      border: 1px solid #eee;
      border-radius: 5px;
      padding: 10px;
      margin-bottom: 15px;
      background-color: #f9f9f9;
    }

    .message {
      margin-bottom: 10px;
      padding: 8px 12px;
      border-radius: 5px;
      max-width: 80%;
      word-wrap: break-word;
    }

    .user-message {
      background-color: #e3f2fd;
      margin-left: auto;
      text-align: right;
    }

    .agent-message {
      background-color: #f1f1f1;
      margin-right: auto;
    }

    .input-container {
      display: flex;
      gap: 10px;
    }

    .message-input {
      flex: 1;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    .send-btn {
      padding: 10px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }

    .send-btn:hover {
      background-color: #45a049;
    }

    .connect-btn {
      padding: 5px 10px;
      background-color: #2196F3;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 0.8rem;
    }

    .connect-btn:hover {
      background-color: #0b7dda;
    }

    .disconnect-btn {
      padding: 5px 10px;
      background-color: #f44336;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 0.8rem;
    }

    .disconnect-btn:hover {
      background-color: #d32f2f;
    }
  </style>
</head>
<body>
  <h1>Test des Interfaces</h1>
  <p style="text-align: center;">Cette page permet de tester les interfaces Luna, Louna et Lounas.</p>

  <div style="text-align: center; margin-bottom: 20px; padding: 10px; background-color: #f0f8ff; border-radius: 5px;">
    <label>
      <input type="checkbox" id="simulation-toggle" checked>
      <strong>Mode simulation (sans serveur)</strong>
    </label>
    <div style="margin-top: 10px; font-size: 0.9em; color: #333;">
      En mode simulation, tapez <code>/help</code> pour voir les commandes disponibles.<br>
      La mémoire thermique est simulée et les messages sont stockés dans différentes zones.
    </div>
    <div style="margin-top: 10px;">
      <button id="show-chat-btn" class="tab-btn active">Interfaces de chat</button>
      <button id="show-memory-btn" class="tab-btn">Visualisation mémoire thermique</button>
    </div>
  </div>

  <style>
    .tab-btn {
      padding: 8px 15px;
      margin: 0 5px;
      background-color: #e0e0e0;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
    }

    .tab-btn.active {
      background-color: #4CAF50;
      color: white;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .memory-container {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      justify-content: center;
      margin-bottom: 20px;
    }

    .memory-zone {
      flex: 1;
      min-width: 200px;
      max-width: 300px;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 15px;
    }

    .memory-zone-header {
      font-weight: bold;
      padding-bottom: 8px;
      margin-bottom: 10px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .memory-count {
      background-color: #f0f0f0;
      border-radius: 10px;
      padding: 2px 8px;
      font-size: 0.8em;
    }

    .memory-item {
      background-color: #f9f9f9;
      border-radius: 5px;
      padding: 8px;
      margin-bottom: 8px;
      font-size: 0.9em;
      position: relative;
      transition: all 0.3s ease;
    }

    .memory-item:hover {
      background-color: #f0f8ff;
      transform: translateY(-2px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .memory-content {
      margin-bottom: 5px;
      word-break: break-word;
    }

    .memory-meta {
      font-size: 0.8em;
      color: #666;
      display: flex;
      justify-content: space-between;
    }

    .memory-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 5px;
    }

    .memory-tag {
      background-color: #e0f7fa;
      border-radius: 10px;
      padding: 2px 6px;
      font-size: 0.7em;
    }

    .memory-stats {
      background-color: #f5f5f5;
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .memory-stats-title {
      font-weight: bold;
      margin-bottom: 10px;
    }

    .memory-stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 10px;
    }

    .memory-stat-item {
      background-color: white;
      border-radius: 5px;
      padding: 10px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .memory-stat-label {
      font-size: 0.8em;
      color: #666;
    }

    .memory-stat-value {
      font-size: 1.2em;
      font-weight: bold;
    }

    .memory-controls {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .control-btn {
      padding: 8px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }

    .control-btn:hover {
      background-color: #45a049;
    }

    .control-btn.active {
      background-color: #ff9800;
    }

    .connections-container {
      background-color: #f5f5f5;
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .connections-title {
      font-weight: bold;
      margin-bottom: 10px;
      text-align: center;
    }

    .connections-graph {
      height: 400px;
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    .memory-node {
      position: absolute;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: rgba(76, 175, 80, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: white;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      z-index: 2;
    }

    .memory-node:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .memory-connection {
      position: absolute;
      background-color: rgba(0, 0, 0, 0.1);
      transform-origin: 0 0;
      z-index: 1;
    }

    .memory-item.dreaming {
      animation: dream-pulse 2s infinite;
      border-left: 3px solid #ff9800;
    }

    @keyframes dream-pulse {
      0% { background-color: #f9f9f9; }
      50% { background-color: #fff8e1; }
      100% { background-color: #f9f9f9; }
    }

    .emotional-indicator {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 5px;
    }

    .emotion-joy { background-color: #ffeb3b; }
    .emotion-sadness { background-color: #2196f3; }
    .emotion-fear { background-color: #9c27b0; }
    .emotion-anger { background-color: #f44336; }
    .emotion-surprise { background-color: #00bcd4; }
    .emotion-trust { background-color: #4caf50; }

    .small-btn {
      padding: 5px 10px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
      margin: 0 5px;
    }

    .small-btn:hover {
      background-color: #45a049;
    }

    .brain-activity-controls, .prediction-controls {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin: 10px 0;
    }

    .brain-activity-graph {
      height: 300px;
      background-color: #000;
      border-radius: 5px;
      position: relative;
      overflow: hidden;
    }

    .prediction-results {
      background-color: white;
      border-radius: 5px;
      padding: 15px;
      min-height: 100px;
      max-height: 300px;
      overflow-y: auto;
    }

    #prediction-input {
      flex: 1;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 3px;
      font-size: 14px;
    }

    .neuron {
      position: absolute;
      width: 6px;
      height: 6px;
      background-color: white;
      border-radius: 50%;
      transition: opacity 0.3s;
    }

    .neuron-connection {
      position: absolute;
      background-color: rgba(255, 255, 255, 0.2);
      transform-origin: 0 0;
      z-index: 1;
    }

    .brain-wave {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .wave-line {
      fill: none;
      stroke-width: 2px;
      stroke-linecap: round;
    }

    .alpha-wave { stroke: rgba(76, 175, 80, 0.7); }
    .beta-wave { stroke: rgba(33, 150, 243, 0.7); }
    .theta-wave { stroke: rgba(255, 193, 7, 0.7); }
    .delta-wave { stroke: rgba(156, 39, 176, 0.7); }

    .heatmap-cell {
      position: absolute;
      border-radius: 2px;
      transition: background-color 0.5s;
    }

    .memory-item.forgetting {
      opacity: 0.5;
      text-decoration: line-through;
      background-color: #f5f5f5;
      transition: all 1s;
    }

    .memory-item.learning {
      border-left: 3px solid #4CAF50;
      background-color: #f1f8e9;
      box-shadow: 0 2px 5px rgba(76, 175, 80, 0.2);
    }

    .prediction-item {
      background-color: #f9f9f9;
      border-radius: 5px;
      padding: 10px;
      margin-bottom: 10px;
      border-left: 3px solid #2196f3;
    }

    .prediction-score {
      float: right;
      background-color: #2196f3;
      color: white;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 0.8em;
    }

    .brain-3d-container {
      height: 400px;
      background-color: #000;
      border-radius: 5px;
      position: relative;
      overflow: hidden;
    }

    .brain-3d-controls {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin: 10px 0;
    }

    .emotion-analysis-container {
      background-color: white;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
    }

    .emotion-chart-container {
      height: 300px;
      background-color: white;
      border-radius: 5px;
      position: relative;
      overflow: hidden;
    }

    .emotion-analysis-controls {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin: 10px 0;
    }

    .content-generation-controls {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin: 10px 0;
    }

    .generation-results {
      background-color: white;
      border-radius: 5px;
      padding: 15px;
      min-height: 200px;
      max-height: 400px;
      overflow-y: auto;
    }

    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 15px;
      padding: 15px;
    }

    .dashboard-widget {
      background-color: white;
      border-radius: 5px;
      padding: 15px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .dashboard-widget-title {
      font-weight: bold;
      margin-bottom: 10px;
      padding-bottom: 5px;
      border-bottom: 1px solid #eee;
    }

    .dashboard-widget-content {
      height: 150px;
      position: relative;
    }

    .emotion-gauge {
      width: 100%;
      height: 20px;
      background-color: #f5f5f5;
      border-radius: 10px;
      margin-bottom: 10px;
      position: relative;
      overflow: hidden;
    }

    .emotion-gauge-fill {
      height: 100%;
      border-radius: 10px;
      transition: width 0.5s;
    }

    .emotion-gauge-label {
      display: flex;
      justify-content: space-between;
      font-size: 0.8em;
      color: #666;
    }

    .brain-region {
      position: absolute;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s;
      cursor: pointer;
    }

    .brain-region:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .brain-region.active {
      background-color: rgba(76, 175, 80, 0.3);
      border-color: rgba(76, 175, 80, 0.6);
    }

    .brain-region-tooltip {
      position: absolute;
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: 12px;
      pointer-events: none;
      z-index: 10;
      opacity: 0;
      transition: opacity 0.3s;
    }

    .generated-content {
      white-space: pre-wrap;
      line-height: 1.5;
    }

    .emotion-memory-item {
      background-color: #f9f9f9;
      border-radius: 5px;
      padding: 10px;
      margin-bottom: 10px;
      border-left: 3px solid #9c27b0;
    }

    .emotion-distribution {
      display: flex;
      margin-top: 10px;
    }

    .emotion-bar {
      flex: 1;
      height: 20px;
      margin: 0 1px;
    }

    .emotion-legend {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
      justify-content: center;
    }

    .emotion-legend-item {
      display: flex;
      align-items: center;
      font-size: 0.8em;
    }

    .emotion-legend-color {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 5px;
    }
  </style>

  <div id="chat-tab" class="tab-content active">
    <div class="interfaces-container">
      <div class="interface" id="luna-interface">
      <div class="interface-header">
        <h2 class="interface-title">Luna</h2>
        <div>
          <span class="status disconnected" id="luna-status"></span>
          <span id="luna-status-text">Déconnecté</span>
          <button class="connect-btn" id="luna-connect-btn">Connecter</button>
          <button class="disconnect-btn" id="luna-disconnect-btn" style="display: none;">Déconnecter</button>
        </div>
      </div>
      <div class="chat-container" id="luna-chat"></div>
      <div class="input-container">
        <input type="text" class="message-input" id="luna-input" placeholder="Tapez votre message ici...">
        <button class="send-btn" id="luna-send-btn">Envoyer</button>
      </div>
    </div>

    <div class="interface" id="louna-interface">
      <div class="interface-header">
        <h2 class="interface-title">Louna</h2>
        <div>
          <span class="status disconnected" id="louna-status"></span>
          <span id="louna-status-text">Déconnecté</span>
          <button class="connect-btn" id="louna-connect-btn">Connecter</button>
          <button class="disconnect-btn" id="louna-disconnect-btn" style="display: none;">Déconnecter</button>
        </div>
      </div>
      <div class="chat-container" id="louna-chat"></div>
      <div class="input-container">
        <input type="text" class="message-input" id="louna-input" placeholder="Tapez votre message ici...">
        <button class="send-btn" id="louna-send-btn">Envoyer</button>
      </div>
    </div>

    <div class="interface" id="lounas-interface">
      <div class="interface-header">
        <h2 class="interface-title">Lounas</h2>
        <div>
          <span class="status disconnected" id="lounas-status"></span>
          <span id="lounas-status-text">Déconnecté</span>
          <button class="connect-btn" id="lounas-connect-btn">Connecter</button>
          <button class="disconnect-btn" id="lounas-disconnect-btn" style="display: none;">Déconnecter</button>
        </div>
      </div>
      <div class="chat-container" id="lounas-chat"></div>
      <div class="input-container">
        <input type="text" class="message-input" id="lounas-input" placeholder="Tapez votre message ici...">
        <button class="send-btn" id="lounas-send-btn">Envoyer</button>
      </div>
    </div>
    </div>
  </div>

  <div id="memory-tab" class="tab-content">
    <div class="memory-controls">
      <button id="dream-mode-btn" class="control-btn">Activer mode rêve</button>
      <button id="learning-mode-btn" class="control-btn">Activer apprentissage</button>
      <button id="show-connections-btn" class="control-btn">Afficher connexions</button>
      <button id="show-brain-activity-btn" class="control-btn">Activité cérébrale</button>
    </div>

    <div class="memory-controls">
      <button id="create-random-btn" class="control-btn">Créer mémoires aléatoires</button>
      <button id="consolidate-btn" class="control-btn">Consolider maintenant</button>
      <button id="forget-btn" class="control-btn">Oubli naturel</button>
      <button id="predict-btn" class="control-btn">Prédire</button>
    </div>

    <div class="memory-controls">
      <button id="show-brain-3d-btn" class="control-btn">Cerveau 3D</button>
      <button id="emotion-analysis-btn" class="control-btn">Analyse émotionnelle</button>
      <button id="generate-content-btn" class="control-btn">Générer contenu</button>
      <button id="show-dashboard-btn" class="control-btn">Tableau de bord</button>
    </div>

    <div class="memory-stats">
      <div class="memory-stats-title">Statistiques de la mémoire thermique</div>
      <div class="memory-stats-grid" id="memory-stats-grid">
        <!-- Les statistiques seront ajoutées dynamiquement -->
      </div>
    </div>

    <div id="connections-view" class="connections-container" style="display: none;">
      <div class="connections-title">Visualisation des connexions entre mémoires</div>
      <div id="connections-graph" class="connections-graph">
        <!-- Le graphe de connexions sera généré ici -->
      </div>
    </div>

    <div id="brain-activity-view" class="connections-container" style="display: none;">
      <div class="connections-title">Visualisation de l'activité cérébrale</div>
      <div class="brain-activity-controls">
        <button id="pause-activity-btn" class="small-btn">Pause</button>
        <select id="activity-view-select">
          <option value="heatmap">Carte de chaleur</option>
          <option value="waves">Ondes cérébrales</option>
          <option value="neurons">Réseau neuronal</option>
        </select>
      </div>
      <div id="brain-activity-graph" class="brain-activity-graph">
        <!-- Le graphe d'activité cérébrale sera généré ici -->
      </div>
    </div>

    <div id="prediction-view" class="connections-container" style="display: none;">
      <div class="connections-title">Prédictions basées sur les connexions</div>
      <div class="prediction-controls">
        <input type="text" id="prediction-input" placeholder="Entrez un mot ou une phrase pour prédire...">
        <button id="run-prediction-btn" class="small-btn">Prédire</button>
      </div>
      <div id="prediction-results" class="prediction-results">
        <!-- Les résultats de prédiction seront affichés ici -->
      </div>
    </div>

    <div id="brain-3d-view" class="connections-container" style="display: none;">
      <div class="connections-title">Visualisation 3D du cerveau</div>
      <div class="brain-3d-controls">
        <button id="rotate-brain-btn" class="small-btn">Rotation auto</button>
        <select id="brain-region-select">
          <option value="all">Cerveau entier</option>
          <option value="frontal">Lobe frontal</option>
          <option value="temporal">Lobe temporal</option>
          <option value="parietal">Lobe pariétal</option>
          <option value="occipital">Lobe occipital</option>
          <option value="limbic">Système limbique</option>
        </select>
      </div>
      <div id="brain-3d-container" class="brain-3d-container">
        <!-- Le modèle 3D du cerveau sera généré ici -->
      </div>
    </div>

    <div id="emotion-analysis-view" class="connections-container" style="display: none;">
      <div class="connections-title">Analyse émotionnelle</div>
      <div class="emotion-analysis-controls">
        <select id="emotion-analysis-mode">
          <option value="current">État émotionnel actuel</option>
          <option value="history">Historique émotionnel</option>
          <option value="memories">Émotions par mémoire</option>
        </select>
      </div>
      <div id="emotion-analysis-container" class="emotion-analysis-container">
        <!-- L'analyse émotionnelle sera affichée ici -->
      </div>
      <div id="emotion-chart-container" class="emotion-chart-container">
        <!-- Le graphique des émotions sera affiché ici -->
      </div>
    </div>

    <div id="content-generation-view" class="connections-container" style="display: none;">
      <div class="connections-title">Génération de contenu</div>
      <div class="content-generation-controls">
        <input type="text" id="generation-input" placeholder="Entrez un sujet ou un mot-clé...">
        <select id="generation-type">
          <option value="text">Texte</option>
          <option value="story">Histoire</option>
          <option value="poem">Poème</option>
          <option value="summary">Résumé</option>
        </select>
        <button id="run-generation-btn" class="small-btn">Générer</button>
      </div>
      <div id="generation-results" class="generation-results">
        <!-- Les résultats de génération seront affichés ici -->
      </div>
    </div>

    <div id="dashboard-view" class="connections-container" style="display: none;">
      <div class="connections-title">Tableau de bord du cerveau</div>
      <div class="dashboard-grid" id="dashboard-grid">
        <!-- Les widgets du tableau de bord seront ajoutés ici -->
      </div>
    </div>

    <div class="memory-container">
      <div class="memory-zone" id="instant-zone">
        <div class="memory-zone-header">
          <span>Zone Instantanée</span>
          <span class="memory-count" id="instant-count">0</span>
        </div>
        <div class="memory-items" id="instant-items">
          <!-- Les mémoires seront ajoutées dynamiquement -->
        </div>
      </div>

      <div class="memory-zone" id="short_term-zone">
        <div class="memory-zone-header">
          <span>Zone Court Terme</span>
          <span class="memory-count" id="short_term-count">0</span>
        </div>
        <div class="memory-items" id="short_term-items">
          <!-- Les mémoires seront ajoutées dynamiquement -->
        </div>
      </div>

      <div class="memory-zone" id="working-zone">
        <div class="memory-zone-header">
          <span>Zone de Travail</span>
          <span class="memory-count" id="working-count">0</span>
        </div>
        <div class="memory-items" id="working-items">
          <!-- Les mémoires seront ajoutées dynamiquement -->
        </div>
      </div>

      <div class="memory-zone" id="medium_term-zone">
        <div class="memory-zone-header">
          <span>Zone Moyen Terme</span>
          <span class="memory-count" id="medium_term-count">0</span>
        </div>
        <div class="memory-items" id="medium_term-items">
          <!-- Les mémoires seront ajoutées dynamiquement -->
        </div>
      </div>
    </div>

    <div class="memory-container">
      <div class="memory-zone" id="long_term-zone">
        <div class="memory-zone-header">
          <span>Zone Long Terme</span>
          <span class="memory-count" id="long_term-count">0</span>
        </div>
        <div class="memory-items" id="long_term-items">
          <!-- Les mémoires seront ajoutées dynamiquement -->
        </div>
      </div>

      <div class="memory-zone" id="dream-zone">
        <div class="memory-zone-header">
          <span>Zone de Rêve</span>
          <span class="memory-count" id="dream-count">0</span>
        </div>
        <div class="memory-items" id="dream-items">
          <!-- Les mémoires seront ajoutées dynamiquement -->
        </div>
      </div>

      <div class="memory-zone" id="kyber-zone">
        <div class="memory-zone-header">
          <span>Zone Kyber</span>
          <span class="memory-count" id="kyber-count">0</span>
        </div>
        <div class="memory-items" id="kyber-items">
          <!-- Les mémoires seront ajoutées dynamiquement -->
        </div>
      </div>

      <div class="memory-zone" id="archive-zone">
        <div class="memory-zone-header">
          <span>Zone Archive</span>
          <span class="memory-count" id="archive-count">0</span>
        </div>
        <div class="memory-items" id="archive-items">
          <!-- Les mémoires seront ajoutées dynamiquement -->
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <script>
    // Configuration des interfaces
    const interfaces = [
      { name: 'luna', port: 3001 },
      { name: 'louna', port: 3002 },
      { name: 'lounas', port: 3003 }
    ];

    // Initialiser les sockets
    const sockets = {};

    // Mode de simulation (sans serveur)
    let simulationMode = true;

    // Simulation de la mémoire thermique
    const thermalMemory = {
      zones: {
        instant: [],
        short_term: [],
        working: [],
        medium_term: [],
        long_term: [],
        dream: [],
        kyber: [],
        archive: []
      },

      // État émotionnel global
      emotionalState: {
        joy: 0.5,
        sadness: 0.1,
        fear: 0.1,
        anger: 0.1,
        surprise: 0.2,
        trust: 0.6
      },

      // Modes
      dreamMode: false,
      learningMode: false,

      // Activité cérébrale
      brainActivity: {
        paused: false,
        activeNeurons: [],
        waveData: {
          alpha: [],
          beta: [],
          theta: [],
          delta: []
        },
        heatmap: []
      },

      // Historique émotionnel
      emotionalHistory: [],

      // Régions du cerveau
      brainRegions: {
        frontal: {
          name: 'Lobe frontal',
          description: 'Responsable de la planification, du raisonnement et de la prise de décision',
          x: 150,
          y: 100,
          radius: 80,
          memories: ['working', 'short_term']
        },
        temporal: {
          name: 'Lobe temporal',
          description: 'Impliqué dans le traitement auditif, la mémoire et le langage',
          x: 250,
          y: 150,
          radius: 70,
          memories: ['medium_term', 'long_term']
        },
        parietal: {
          name: 'Lobe pariétal',
          description: 'Traite les informations sensorielles et spatiales',
          x: 150,
          y: 200,
          radius: 60,
          memories: ['instant', 'short_term']
        },
        occipital: {
          name: 'Lobe occipital',
          description: 'Centre du traitement visuel',
          x: 80,
          y: 200,
          radius: 50,
          memories: ['instant']
        },
        limbic: {
          name: 'Système limbique',
          description: 'Impliqué dans les émotions, la motivation et la mémoire',
          x: 200,
          y: 150,
          radius: 40,
          memories: ['dream', 'kyber']
        }
      },

      // Statistiques de performance
      performanceStats: {
        memoryUsage: [],
        processingSpeed: [],
        emotionalStability: [],
        learningRate: []
      },

      // Ajouter une mémoire
      addMemory: function(content, source) {
        // Analyser le contenu pour détecter les émotions
        const emotions = this.detectEmotions(content);

        const memory = {
          id: `mem-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
          content: content,
          source: source,
          timestamp: new Date(),
          zone: 'instant',
          accessCount: 1,
          importance: Math.random() * 0.5 + 0.3, // Importance entre 0.3 et 0.8
          connections: [],
          semanticTags: this.generateTags(content),
          emotions: emotions
        };

        // Ajuster l'importance en fonction des émotions
        if (Object.keys(emotions).length > 0) {
          let emotionalBoost = 0;

          if (emotions.joy) emotionalBoost += 0.1;
          if (emotions.sadness) emotionalBoost += 0.15;
          if (emotions.fear) emotionalBoost += 0.2;
          if (emotions.anger) emotionalBoost += 0.2;
          if (emotions.surprise) emotionalBoost += 0.15;
          if (emotions.trust) emotionalBoost += 0.1;

          memory.importance = Math.min(1, memory.importance + emotionalBoost);
        }

        this.zones.instant.push(memory);
        console.log(`Mémoire ajoutée: ${memory.id} (${memory.content.substring(0, 30)}...)`);

        // Mettre à jour l'état émotionnel global
        this.updateEmotionalState(emotions);

        // Créer des connexions avec les mémoires existantes
        this.createConnectionsForMemory(memory);

        // Mettre à jour la visualisation si l'onglet de mémoire est actif
        if (document.getElementById('memory-tab').classList.contains('active')) {
          updateMemoryVisualization();

          // Mettre à jour la visualisation des connexions si elle est visible
          if (document.getElementById('connections-view').style.display !== 'none') {
            updateConnectionsVisualization();
          }
        }

        // Déclencher la migration après un délai
        setTimeout(() => {
          this.migrateMemories();

          // Mettre à jour la visualisation après la migration si l'onglet de mémoire est actif
          if (document.getElementById('memory-tab').classList.contains('active')) {
            updateMemoryVisualization();

            // Mettre à jour la visualisation des connexions si elle est visible
            if (document.getElementById('connections-view').style.display !== 'none') {
              updateConnectionsVisualization();
            }
          }
        }, 5000);

        return memory;
      },

      // Détecter les émotions dans le contenu
      detectEmotions: function(content) {
        const emotions = {};
        const lowerContent = content.toLowerCase();

        // Mots liés à la joie
        const joyWords = ['heureux', 'content', 'joyeux', 'super', 'génial', 'excellent', 'happy', 'great'];
        if (joyWords.some(word => lowerContent.includes(word))) {
          emotions.joy = true;
        }

        // Mots liés à la tristesse
        const sadnessWords = ['triste', 'malheureux', 'désolé', 'dommage', 'peine', 'sad', 'sorry'];
        if (sadnessWords.some(word => lowerContent.includes(word))) {
          emotions.sadness = true;
        }

        // Mots liés à la peur
        const fearWords = ['peur', 'effrayant', 'inquiet', 'danger', 'afraid', 'scared', 'worried'];
        if (fearWords.some(word => lowerContent.includes(word))) {
          emotions.fear = true;
        }

        // Mots liés à la colère
        const angerWords = ['colère', 'énervé', 'furieux', 'frustré', 'angry', 'mad', 'frustrated'];
        if (angerWords.some(word => lowerContent.includes(word))) {
          emotions.anger = true;
        }

        // Mots liés à la surprise
        const surpriseWords = ['surprise', 'étonnant', 'incroyable', 'wow', 'amazing', 'surprising'];
        if (surpriseWords.some(word => lowerContent.includes(word))) {
          emotions.surprise = true;
        }

        // Mots liés à la confiance
        const trustWords = ['confiance', 'fiable', 'sûr', 'certain', 'trust', 'reliable', 'sure'];
        if (trustWords.some(word => lowerContent.includes(word))) {
          emotions.trust = true;
        }

        return emotions;
      },

      // Mettre à jour l'état émotionnel global
      updateEmotionalState: function(emotions) {
        const decayRate = 0.01; // Taux de décroissance des émotions
        const impactRate = 0.1; // Taux d'impact des nouvelles émotions

        // Appliquer la décroissance à toutes les émotions
        for (const emotion in this.emotionalState) {
          // Valeur neutre pour chaque émotion
          const neutralValue = emotion === 'trust' ? 0.6 : 0.2;

          // Décroissance vers la valeur neutre
          if (this.emotionalState[emotion] > neutralValue) {
            this.emotionalState[emotion] = Math.max(
              neutralValue,
              this.emotionalState[emotion] - decayRate
            );
          } else if (this.emotionalState[emotion] < neutralValue) {
            this.emotionalState[emotion] = Math.min(
              neutralValue,
              this.emotionalState[emotion] + decayRate
            );
          }
        }

        // Appliquer l'impact des nouvelles émotions
        if (emotions.joy) {
          this.emotionalState.joy = Math.min(1, this.emotionalState.joy + impactRate);
          this.emotionalState.sadness = Math.max(0, this.emotionalState.sadness - impactRate);
        }

        if (emotions.sadness) {
          this.emotionalState.sadness = Math.min(1, this.emotionalState.sadness + impactRate);
          this.emotionalState.joy = Math.max(0, this.emotionalState.joy - impactRate);
        }

        if (emotions.fear) {
          this.emotionalState.fear = Math.min(1, this.emotionalState.fear + impactRate);
          this.emotionalState.trust = Math.max(0, this.emotionalState.trust - impactRate / 2);
        }

        if (emotions.anger) {
          this.emotionalState.anger = Math.min(1, this.emotionalState.anger + impactRate);
          this.emotionalState.trust = Math.max(0, this.emotionalState.trust - impactRate / 2);
        }

        if (emotions.surprise) {
          this.emotionalState.surprise = Math.min(1, this.emotionalState.surprise + impactRate);
        }

        if (emotions.trust) {
          this.emotionalState.trust = Math.min(1, this.emotionalState.trust + impactRate);
          this.emotionalState.fear = Math.max(0, this.emotionalState.fear - impactRate / 2);
        }

        // Enregistrer l'état émotionnel dans l'historique
        this.updateEmotionalHistory();

        // Mettre à jour les statistiques de performance
        this.updatePerformanceStats();
      },

      // Mettre à jour l'historique émotionnel
      updateEmotionalHistory: function() {
        // Créer une entrée d'historique
        const historyEntry = {
          timestamp: new Date(),
          emotionalState: { ...this.emotionalState },
          dominantEmotion: this.getDominantEmotion()
        };

        // Ajouter à l'historique
        this.emotionalHistory.push(historyEntry);

        // Limiter la taille de l'historique
        if (this.emotionalHistory.length > 100) {
          this.emotionalHistory.shift();
        }
      },

      // Mettre à jour les statistiques de performance
      updatePerformanceStats: function() {
        const now = new Date();

        // Calculer l'utilisation de la mémoire
        const totalMemories = Object.values(this.zones).reduce((sum, zone) => sum + zone.length, 0);
        const memoryUsage = Math.min(1, totalMemories / 100); // Normaliser entre 0 et 1

        // Calculer la vitesse de traitement (basée sur le nombre de connexions)
        const totalConnections = Object.values(this.zones).reduce((sum, zone) => {
          return sum + zone.reduce((connSum, memory) => connSum + memory.connections.length, 0);
        }, 0);
        const processingSpeed = Math.min(1, totalConnections / 200); // Normaliser entre 0 et 1

        // Calculer la stabilité émotionnelle
        const emotionalValues = Object.values(this.emotionalState);
        const emotionalVariance = emotionalValues.reduce((sum, value) => sum + Math.pow(value - 0.5, 2), 0) / emotionalValues.length;
        const emotionalStability = 1 - Math.min(1, emotionalVariance * 5); // Normaliser entre 0 et 1

        // Calculer le taux d'apprentissage
        const learningRate = this.learningMode ? 0.8 : 0.3;

        // Ajouter aux statistiques
        this.performanceStats.memoryUsage.push({ timestamp: now, value: memoryUsage });
        this.performanceStats.processingSpeed.push({ timestamp: now, value: processingSpeed });
        this.performanceStats.emotionalStability.push({ timestamp: now, value: emotionalStability });
        this.performanceStats.learningRate.push({ timestamp: now, value: learningRate });

        // Limiter la taille des statistiques
        const maxStats = 100;
        if (this.performanceStats.memoryUsage.length > maxStats) {
          this.performanceStats.memoryUsage.shift();
        }
        if (this.performanceStats.processingSpeed.length > maxStats) {
          this.performanceStats.processingSpeed.shift();
        }
        if (this.performanceStats.emotionalStability.length > maxStats) {
          this.performanceStats.emotionalStability.shift();
        }
        if (this.performanceStats.learningRate.length > maxStats) {
          this.performanceStats.learningRate.shift();
        }
      },

      // Créer des connexions pour une nouvelle mémoire
      createConnectionsForMemory: function(memory) {
        const allMemories = [].concat(
          this.zones.instant,
          this.zones.short_term,
          this.zones.working,
          this.zones.medium_term,
          this.zones.long_term,
          this.zones.dream,
          this.zones.kyber,
          this.zones.archive
        );

        // Trouver des mémoires similaires
        for (const otherMemory of allMemories) {
          // Ne pas connecter avec soi-même
          if (otherMemory.id === memory.id) continue;

          // Calculer la similarité
          const similarity = this.calculateSimilarity(memory, otherMemory);

          // Si la similarité est suffisante, créer une connexion
          if (similarity > 0.3) {
            this.connectMemories(memory.id, otherMemory.id, similarity);
          }
        }
      },

      // Calculer la similarité entre deux mémoires
      calculateSimilarity: function(memory1, memory2) {
        let similarity = 0;

        // Similarité basée sur les tags sémantiques
        const tags1 = new Set(memory1.semanticTags || []);
        const tags2 = new Set(memory2.semanticTags || []);

        // Calculer l'intersection et l'union des tags
        const intersection = new Set([...tags1].filter(tag => tags2.has(tag)));
        const union = new Set([...tags1, ...tags2]);

        // Jaccard similarity coefficient
        if (union.size > 0) {
          similarity += (intersection.size / union.size) * 0.6;
        }

        // Similarité basée sur la source
        if (memory1.source === memory2.source) {
          similarity += 0.2;
        }

        // Similarité basée sur la proximité temporelle
        const timeDiff = Math.abs(new Date(memory1.timestamp) - new Date(memory2.timestamp));
        const timeSimilarity = Math.max(0, 1 - (timeDiff / (1000 * 60 * 60))); // 1 heure max
        similarity += timeSimilarity * 0.2;

        // Similarité basée sur les émotions
        if (memory1.emotions && memory2.emotions) {
          const emotions1 = Object.keys(memory1.emotions);
          const emotions2 = Object.keys(memory2.emotions);

          const emotionIntersection = emotions1.filter(emotion => emotions2.includes(emotion));
          if (emotionIntersection.length > 0) {
            similarity += 0.2;
          }
        }

        return similarity;
      },

      // Connecter deux mémoires
      connectMemories: function(id1, id2, strength = 0.5) {
        // Trouver les mémoires
        let memory1 = null;
        let memory2 = null;

        // Parcourir toutes les zones pour trouver les mémoires
        for (const zone in this.zones) {
          for (const memory of this.zones[zone]) {
            if (memory.id === id1) memory1 = memory;
            if (memory.id === id2) memory2 = memory;

            // Si les deux mémoires sont trouvées, sortir des boucles
            if (memory1 && memory2) break;
          }
          if (memory1 && memory2) break;
        }

        // Si les deux mémoires sont trouvées, créer la connexion
        if (memory1 && memory2) {
          // Vérifier si la connexion existe déjà
          const existingConnection1 = memory1.connections.find(conn => conn.id === id2);
          const existingConnection2 = memory2.connections.find(conn => conn.id === id1);

          if (existingConnection1) {
            // Renforcer la connexion existante
            existingConnection1.strength = Math.min(1, existingConnection1.strength + 0.1);
          } else {
            // Créer une nouvelle connexion
            memory1.connections.push({
              id: id2,
              strength: strength
            });
          }

          if (existingConnection2) {
            // Renforcer la connexion existante
            existingConnection2.strength = Math.min(1, existingConnection2.strength + 0.1);
          } else {
            // Créer une nouvelle connexion
            memory2.connections.push({
              id: id1,
              strength: strength
            });
          }

          console.log(`Connexion créée/renforcée entre ${id1} et ${id2} (force: ${strength.toFixed(2)})`);
          return true;
        }

        return false;
      },

      // Activer le mode rêve
      activateDreamMode: function() {
        if (this.dreamMode) return; // Déjà en mode rêve

        this.dreamMode = true;
        console.log('Mode rêve activé');

        // Démarrer le processus de rêve
        this.dreamInterval = setInterval(() => {
          this.processDream();
        }, 5000); // Traiter le rêve toutes les 5 secondes
      },

      // Désactiver le mode rêve
      deactivateDreamMode: function() {
        if (!this.dreamMode) return; // Pas en mode rêve

        this.dreamMode = false;
        console.log('Mode rêve désactivé');

        // Arrêter le processus de rêve
        clearInterval(this.dreamInterval);
      },

      // Traiter le rêve
      processDream: function() {
        console.log('Traitement du rêve...');

        // Sélectionner des mémoires aléatoires pour le traitement
        const allMemories = [].concat(
          this.zones.short_term,
          this.zones.working,
          this.zones.medium_term
        );

        // Mélanger les mémoires
        const shuffledMemories = this.shuffleArray(allMemories);

        // Sélectionner quelques mémoires pour le traitement
        const selectedMemories = shuffledMemories.slice(0, Math.min(3, shuffledMemories.length));

        // Marquer les mémoires comme étant en cours de traitement
        for (const memory of selectedMemories) {
          memory.dreaming = true;

          // Créer des connexions entre les mémoires sélectionnées
          for (const otherMemory of selectedMemories) {
            if (memory.id !== otherMemory.id) {
              this.connectMemories(memory.id, otherMemory.id, 0.3);
            }
          }

          // Déplacer certaines mémoires vers la zone de rêve
          if (Math.random() < 0.3) {
            // Trouver la zone actuelle de la mémoire
            for (const zone in this.zones) {
              const index = this.zones[zone].findIndex(m => m.id === memory.id);

              if (index !== -1) {
                // Supprimer de la zone actuelle
                const memoryToMove = this.zones[zone].splice(index, 1)[0];

                // Mettre à jour la zone
                memoryToMove.zone = 'dream';

                // Ajouter à la zone de rêve
                this.zones.dream.push(memoryToMove);

                console.log(`Mémoire déplacée vers la zone de rêve: ${memory.id}`);
                break;
              }
            }
          }
        }

        // Mettre à jour la visualisation si l'onglet de mémoire est actif
        if (document.getElementById('memory-tab').classList.contains('active')) {
          updateMemoryVisualization();

          // Mettre à jour la visualisation des connexions si elle est visible
          if (document.getElementById('connections-view').style.display !== 'none') {
            updateConnectionsVisualization();
          }
        }

        // Après un délai, terminer le traitement des mémoires
        setTimeout(() => {
          for (const memory of selectedMemories) {
            memory.dreaming = false;
          }

          // Mettre à jour la visualisation
          if (document.getElementById('memory-tab').classList.contains('active')) {
            updateMemoryVisualization();
          }
        }, 3000);
      },

      // Mélanger un tableau
      shuffleArray: function(array) {
        const newArray = [...array];

        for (let i = newArray.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
        }

        return newArray;
      },

      // Créer des mémoires aléatoires
      createRandomMemories: function(count = 5) {
        const sources = ['luna', 'louna', 'lounas', 'utilisateur'];
        const topics = [
          'La mémoire thermique est un système qui simule le fonctionnement du cerveau humain.',
          'Les zones de mémoire permettent de stocker les informations selon leur importance.',
          'Le mode rêve permet de consolider les souvenirs pendant les périodes d\'inactivité.',
          'Les connexions entre les mémoires créent un réseau neuronal artificiel.',
          'Les émotions influencent l\'importance des souvenirs dans le cerveau.',
          'La migration des mémoires se fait en fonction de leur importance et de leur âge.',
          'Le système de mémoire thermique permet de simuler l\'oubli naturel.',
          'Les tags sémantiques permettent de catégoriser les souvenirs.',
          'La zone instantanée contient les informations les plus récentes.',
          'La zone à long terme stocke les informations importantes sur une longue période.'
        ];

        for (let i = 0; i < count; i++) {
          const source = sources[Math.floor(Math.random() * sources.length)];
          const content = topics[Math.floor(Math.random() * topics.length)];

          this.addMemory(content, source);
        }
      },

      // Consolider les mémoires
      consolidateMemories: function() {
        console.log('Consolidation des mémoires...');

        // Renforcer les connexions importantes
        const allMemories = [].concat(
          this.zones.instant,
          this.zones.short_term,
          this.zones.working,
          this.zones.medium_term,
          this.zones.long_term,
          this.zones.dream,
          this.zones.kyber,
          this.zones.archive
        );

        // Parcourir toutes les mémoires
        for (const memory of allMemories) {
          // Renforcer les connexions en fonction de l'importance
          for (const connection of memory.connections) {
            // Plus la mémoire est importante, plus la connexion est renforcée
            const strengthIncrease = memory.importance * 0.1;
            connection.strength = Math.min(1, connection.strength + strengthIncrease);
          }

          // Augmenter l'importance des mémoires avec beaucoup de connexions
          if (memory.connections.length > 3) {
            memory.importance = Math.min(1, memory.importance + 0.05);
          }
        }

        // Déclencher la migration
        this.migrateMemories();

        // Mettre à jour la visualisation
        if (document.getElementById('memory-tab').classList.contains('active')) {
          updateMemoryVisualization();

          // Mettre à jour la visualisation des connexions si elle est visible
          if (document.getElementById('connections-view').style.display !== 'none') {
            updateConnectionsVisualization();
          }
        }
      },

      // Activer le mode d'apprentissage
      activateLearningMode: function() {
        if (this.learningMode) return; // Déjà en mode apprentissage

        this.learningMode = true;
        console.log('Mode apprentissage activé');

        // Démarrer le processus d'apprentissage
        this.learningInterval = setInterval(() => {
          this.processLearning();
        }, 5000); // Traiter l'apprentissage toutes les 5 secondes
      },

      // Désactiver le mode d'apprentissage
      deactivateLearningMode: function() {
        if (!this.learningMode) return; // Pas en mode apprentissage

        this.learningMode = false;
        console.log('Mode apprentissage désactivé');

        // Arrêter le processus d'apprentissage
        clearInterval(this.learningInterval);

        // Réinitialiser les marqueurs d'apprentissage
        const allMemories = [].concat(
          this.zones.instant,
          this.zones.short_term,
          this.zones.working,
          this.zones.medium_term,
          this.zones.long_term,
          this.zones.dream,
          this.zones.kyber,
          this.zones.archive
        );

        for (const memory of allMemories) {
          memory.learning = false;
        }

        // Mettre à jour la visualisation
        if (document.getElementById('memory-tab').classList.contains('active')) {
          updateMemoryVisualization();
        }
      },

      // Traiter l'apprentissage
      processLearning: function() {
        console.log('Traitement de l\'apprentissage...');

        // Sélectionner des mémoires importantes pour l'apprentissage
        const allMemories = [].concat(
          this.zones.instant,
          this.zones.short_term,
          this.zones.working,
          this.zones.medium_term
        );

        // Trier les mémoires par importance
        const sortedMemories = [...allMemories].sort((a, b) => b.importance - a.importance);

        // Sélectionner les mémoires les plus importantes
        const memoriesToLearn = sortedMemories.slice(0, Math.min(3, sortedMemories.length));

        // Marquer les mémoires comme étant en cours d'apprentissage
        for (const memory of memoriesToLearn) {
          memory.learning = true;

          // Augmenter l'importance des mémoires en apprentissage
          memory.importance = Math.min(1, memory.importance + 0.1);

          // Renforcer les connexions existantes
          for (const connection of memory.connections) {
            connection.strength = Math.min(1, connection.strength + 0.2);
          }

          // Créer de nouvelles connexions avec d'autres mémoires importantes
          for (const otherMemory of memoriesToLearn) {
            if (memory.id !== otherMemory.id) {
              this.connectMemories(memory.id, otherMemory.id, 0.7);
            }
          }

          // Déplacer les mémoires en apprentissage vers la zone de travail
          if (memory.zone !== 'working' && memory.zone !== 'kyber') {
            // Trouver la zone actuelle de la mémoire
            for (const zone in this.zones) {
              const index = this.zones[zone].findIndex(m => m.id === memory.id);

              if (index !== -1) {
                // Supprimer de la zone actuelle
                const memoryToMove = this.zones[zone].splice(index, 1)[0];

                // Mettre à jour la zone
                memoryToMove.zone = 'working';

                // Ajouter à la zone de travail
                this.zones.working.push(memoryToMove);

                console.log(`Mémoire déplacée vers la zone de travail: ${memory.id}`);
                break;
              }
            }
          }
        }

        // Mettre à jour la visualisation
        if (document.getElementById('memory-tab').classList.contains('active')) {
          updateMemoryVisualization();

          // Mettre à jour la visualisation des connexions si elle est visible
          if (document.getElementById('connections-view').style.display !== 'none') {
            updateConnectionsVisualization();
          }
        }

        // Après un délai, terminer l'apprentissage des mémoires
        setTimeout(() => {
          for (const memory of memoriesToLearn) {
            memory.learning = false;
          }

          // Mettre à jour la visualisation
          if (document.getElementById('memory-tab').classList.contains('active')) {
            updateMemoryVisualization();
          }
        }, 3000);
      },

      // Processus d'oubli naturel
      forgetMemories: function() {
        console.log('Processus d\'oubli naturel...');

        // Obtenir toutes les mémoires
        const allMemories = [].concat(
          this.zones.instant,
          this.zones.short_term,
          this.zones.working,
          this.zones.medium_term,
          this.zones.long_term,
          this.zones.dream,
          this.zones.kyber,
          this.zones.archive
        );

        // Mémoires à oublier
        const memoriesToForget = [];

        // Parcourir toutes les mémoires
        for (const memory of allMemories) {
          // Calculer la probabilité d'oubli
          let forgetProbability = 0;

          // Plus la mémoire est ancienne, plus elle a de chances d'être oubliée
          const ageInHours = (Date.now() - new Date(memory.timestamp).getTime()) / (1000 * 60 * 60);
          forgetProbability += Math.min(0.5, ageInHours / 24) * 0.4;

          // Moins la mémoire est importante, plus elle a de chances d'être oubliée
          forgetProbability += (1 - memory.importance) * 0.4;

          // Moins la mémoire a de connexions, plus elle a de chances d'être oubliée
          forgetProbability += Math.max(0, 1 - (memory.connections.length / 5)) * 0.2;

          // Décider si la mémoire doit être oubliée
          if (Math.random() < forgetProbability * 0.3) { // Facteur 0.3 pour réduire la probabilité globale
            // Marquer la mémoire pour l'oubli
            memory.forgetting = true;
            memoriesToForget.push(memory);
          }
        }

        // Mettre à jour la visualisation
        if (document.getElementById('memory-tab').classList.contains('active')) {
          updateMemoryVisualization();
        }

        // Après un délai, supprimer les mémoires oubliées
        setTimeout(() => {
          for (const memory of memoriesToForget) {
            // Supprimer la mémoire de sa zone
            for (const zone in this.zones) {
              const index = this.zones[zone].findIndex(m => m.id === memory.id);

              if (index !== -1) {
                // Supprimer de la zone
                this.zones[zone].splice(index, 1);
                console.log(`Mémoire oubliée: ${memory.id}`);
                break;
              }
            }

            // Supprimer les connexions vers cette mémoire
            for (const zone in this.zones) {
              for (const otherMemory of this.zones[zone]) {
                otherMemory.connections = otherMemory.connections.filter(conn => conn.id !== memory.id);
              }
            }
          }

          // Mettre à jour la visualisation
          if (document.getElementById('memory-tab').classList.contains('active')) {
            updateMemoryVisualization();

            // Mettre à jour la visualisation des connexions si elle est visible
            if (document.getElementById('connections-view').style.display !== 'none') {
              updateConnectionsVisualization();
            }
          }
        }, 3000);
      },

      // Prédire à partir d'un texte
      predictFromText: function(text) {
        console.log(`Prédiction à partir de: "${text}"`);

        // Obtenir toutes les mémoires
        const allMemories = [].concat(
          this.zones.instant,
          this.zones.short_term,
          this.zones.working,
          this.zones.medium_term,
          this.zones.long_term,
          this.zones.dream,
          this.zones.kyber,
          this.zones.archive
        );

        // Rechercher des mémoires similaires au texte
        const relatedMemories = [];

        for (const memory of allMemories) {
          // Calculer la similarité avec le texte
          const contentSimilarity = this.calculateTextSimilarity(text, memory.content);

          if (contentSimilarity > 0.2) {
            relatedMemories.push({
              memory,
              similarity: contentSimilarity
            });
          }
        }

        // Trier par similarité
        relatedMemories.sort((a, b) => b.similarity - a.similarity);

        // Limiter le nombre de mémoires
        const topRelatedMemories = relatedMemories.slice(0, 5);

        // Trouver les mémoires connectées aux mémoires similaires
        const connectedMemories = [];

        for (const { memory } of topRelatedMemories) {
          // Parcourir les connexions
          for (const connection of memory.connections) {
            // Trouver la mémoire connectée
            let connectedMemory = null;

            for (const zone in this.zones) {
              for (const mem of this.zones[zone]) {
                if (mem.id === connection.id) {
                  connectedMemory = mem;
                  break;
                }
              }
              if (connectedMemory) break;
            }

            if (connectedMemory) {
              // Calculer le score de prédiction
              const predictionScore = memory.importance * connection.strength * 0.7 +
                                     connectedMemory.importance * 0.3;

              // Ajouter à la liste des mémoires connectées
              connectedMemories.push({
                memory: connectedMemory,
                score: predictionScore
              });
            }
          }
        }

        // Trier par score de prédiction
        connectedMemories.sort((a, b) => b.score - a.score);

        // Limiter le nombre de prédictions
        const predictions = connectedMemories.slice(0, 5);

        return predictions;
      },

      // Calculer la similarité entre deux textes
      calculateTextSimilarity: function(text1, text2) {
        // Convertir en minuscules
        const t1 = text1.toLowerCase();
        const t2 = text2.toLowerCase();

        // Diviser en mots
        const words1 = t1.split(/\s+/);
        const words2 = t2.split(/\s+/);

        // Compter les mots communs
        let commonWords = 0;

        for (const word of words1) {
          if (word.length > 3 && words2.includes(word)) {
            commonWords++;
          }
        }

        // Calculer la similarité
        const similarity = commonWords / Math.max(words1.length, words2.length);

        return similarity;
      },

      // Générer des données d'activité cérébrale
      generateBrainActivity: function() {
        if (this.brainActivity.paused) return;

        // Générer des données pour les ondes cérébrales
        const time = Date.now() / 1000;
        const width = 500; // Largeur du graphique

        // Vider les données précédentes
        this.brainActivity.waveData = {
          alpha: [],
          beta: [],
          theta: [],
          delta: []
        };

        // Générer les points pour chaque type d'onde
        for (let i = 0; i < width; i++) {
          const x = i;

          // Alpha (8-12 Hz) - Relaxation
          const alphaY = 50 + 20 * Math.sin((time + i / 20) * 10) + 5 * Math.sin((time + i / 10) * 8);
          this.brainActivity.waveData.alpha.push({ x, y: alphaY });

          // Beta (13-30 Hz) - Concentration
          const betaY = 120 + 10 * Math.sin((time + i / 10) * 20) + 5 * Math.sin((time + i / 5) * 15);
          this.brainActivity.waveData.beta.push({ x, y: betaY });

          // Theta (4-7 Hz) - Somnolence
          const thetaY = 190 + 25 * Math.sin((time + i / 30) * 5) + 10 * Math.sin((time + i / 20) * 4);
          this.brainActivity.waveData.theta.push({ x, y: thetaY });

          // Delta (1-3 Hz) - Sommeil profond
          const deltaY = 260 + 30 * Math.sin((time + i / 50) * 2) + 15 * Math.sin((time + i / 40) * 1.5);
          this.brainActivity.waveData.delta.push({ x, y: deltaY });
        }

        // Générer des données pour la carte de chaleur
        this.brainActivity.heatmap = [];

        const gridSize = 20;
        const cellSize = 15;

        for (let i = 0; i < gridSize; i++) {
          for (let j = 0; j < gridSize; j++) {
            // Calculer l'activité en fonction des zones actives
            let activity = 0;

            // Plus d'activité dans les zones avec plus de mémoires
            const zoneActivity = {
              instant: this.zones.instant.length * 0.2,
              short_term: this.zones.short_term.length * 0.15,
              working: this.zones.working.length * 0.3,
              medium_term: this.zones.medium_term.length * 0.1,
              long_term: this.zones.long_term.length * 0.05,
              dream: this.zones.dream.length * 0.25,
              kyber: this.zones.kyber.length * 0.4,
              archive: this.zones.archive.length * 0.01
            };

            // Calculer l'activité totale
            const totalActivity = Object.values(zoneActivity).reduce((sum, val) => sum + val, 0);

            // Normaliser l'activité
            const normalizedActivity = Math.min(1, totalActivity / 10);

            // Ajouter du bruit pour rendre la carte plus naturelle
            const noise = Math.sin(i * j + time * 5) * 0.2 + Math.sin(i + j + time * 3) * 0.3;

            // Calculer l'activité finale
            activity = Math.max(0, Math.min(1, normalizedActivity + noise * 0.3));

            // Ajouter la cellule à la carte de chaleur
            this.brainActivity.heatmap.push({
              x: j * cellSize,
              y: i * cellSize,
              width: cellSize,
              height: cellSize,
              activity: activity
            });
          }
        }

        // Générer des données pour le réseau neuronal
        this.brainActivity.activeNeurons = [];

        // Nombre de neurones basé sur le nombre total de mémoires
        const totalMemories = Object.values(this.zones).reduce((sum, zone) => sum + zone.length, 0);
        const neuronCount = Math.min(100, Math.max(20, totalMemories * 2));

        for (let i = 0; i < neuronCount; i++) {
          // Position aléatoire
          const x = Math.random() * 500;
          const y = Math.random() * 300;

          // Activité basée sur l'état émotionnel et les modes actifs
          let activity = 0.2 + Math.random() * 0.3;

          // Plus d'activité si le mode rêve est actif
          if (this.dreamMode) {
            activity += 0.2;
          }

          // Plus d'activité si le mode apprentissage est actif
          if (this.learningMode) {
            activity += 0.3;
          }

          // Influence de l'état émotionnel
          const dominantEmotion = this.getDominantEmotion();
          if (dominantEmotion === 'joy' || dominantEmotion === 'surprise') {
            activity += 0.2;
          } else if (dominantEmotion === 'fear' || dominantEmotion === 'anger') {
            activity += 0.4;
          }

          // Ajouter le neurone
          this.brainActivity.activeNeurons.push({
            x,
            y,
            activity: Math.min(1, activity)
          });
        }
      },

      // Obtenir l'émotion dominante
      getDominantEmotion: function() {
        let dominantEmotion = null;
        let maxIntensity = 0;

        for (const [emotion, intensity] of Object.entries(this.emotionalState)) {
          if (intensity > maxIntensity) {
            maxIntensity = intensity;
            dominantEmotion = emotion;
          }
        }

        return dominantEmotion;
      },

      // Générer du contenu basé sur les mémoires
      generateContent: function(topic, type = 'text') {
        console.log(`Génération de contenu de type ${type} sur le sujet: "${topic}"`);

        // Rechercher des mémoires liées au sujet
        const allMemories = [].concat(
          this.zones.instant,
          this.zones.short_term,
          this.zones.working,
          this.zones.medium_term,
          this.zones.long_term,
          this.zones.dream,
          this.zones.kyber,
          this.zones.archive
        );

        // Trouver les mémoires pertinentes
        const relatedMemories = [];

        for (const memory of allMemories) {
          // Calculer la pertinence
          const relevance = this.calculateTextSimilarity(topic, memory.content);

          if (relevance > 0.1) {
            relatedMemories.push({
              memory,
              relevance
            });
          }
        }

        // Trier par pertinence
        relatedMemories.sort((a, b) => b.relevance - a.relevance);

        // Limiter le nombre de mémoires
        const topMemories = relatedMemories.slice(0, 10);

        // Si aucune mémoire pertinente n'est trouvée
        if (topMemories.length === 0) {
          return {
            content: `Je n'ai pas assez d'informations sur "${topic}" pour générer du contenu.`,
            type: type,
            topic: topic,
            sources: []
          };
        }

        // Générer le contenu en fonction du type
        let generatedContent = '';

        if (type === 'text') {
          // Générer un texte informatif
          generatedContent = `Voici ce que je sais sur "${topic}" :\n\n`;

          for (const { memory } of topMemories) {
            generatedContent += `- ${memory.content}\n`;
          }
        } else if (type === 'story') {
          // Générer une histoire
          const dominantEmotion = this.getDominantEmotion();
          let tone = '';

          // Définir le ton en fonction de l'émotion dominante
          if (dominantEmotion === 'joy') {
            tone = 'joyeuse';
          } else if (dominantEmotion === 'sadness') {
            tone = 'mélancolique';
          } else if (dominantEmotion === 'fear') {
            tone = 'inquiétante';
          } else if (dominantEmotion === 'anger') {
            tone = 'intense';
          } else if (dominantEmotion === 'surprise') {
            tone = 'surprenante';
          } else if (dominantEmotion === 'trust') {
            tone = 'rassurante';
          } else {
            tone = 'intéressante';
          }

          // Introduction
          generatedContent = `Il était une fois, une histoire ${tone} à propos de "${topic}"...\n\n`;

          // Corps de l'histoire
          let storyContent = '';
          for (const { memory } of topMemories) {
            storyContent += `${memory.content} `;
          }

          // Simplifier et adapter le contenu pour une histoire
          const sentences = storyContent.split(/[.!?]+/).filter(s => s.trim().length > 0);

          if (sentences.length > 0) {
            generatedContent += sentences.join('. ') + '.';
          } else {
            generatedContent += `L'histoire de ${topic} reste encore à écrire...`;
          }
        } else if (type === 'poem') {
          // Générer un poème
          generatedContent = `Poème sur "${topic}"\n\n`;

          // Extraire des mots-clés des mémoires
          const keywords = [];
          for (const { memory } of topMemories) {
            const words = memory.content.split(/\s+/).filter(word => word.length > 3);
            keywords.push(...words);
          }

          // Sélectionner des mots-clés aléatoires
          const selectedKeywords = [];
          for (let i = 0; i < Math.min(5, keywords.length); i++) {
            const randomIndex = Math.floor(Math.random() * keywords.length);
            selectedKeywords.push(keywords[randomIndex]);
            keywords.splice(randomIndex, 1);
          }

          // Générer le poème
          generatedContent += `${topic}, ${topic},\n`;
          generatedContent += `Dans mes pensées tu demeures.\n`;

          if (selectedKeywords.length > 0) {
            generatedContent += `${selectedKeywords[0]} comme une lumière,\n`;
          }

          if (selectedKeywords.length > 1) {
            generatedContent += `${selectedKeywords[1]} dans mon esprit,\n`;
          }

          generatedContent += `Souvenirs qui s'entrelacent,\n`;
          generatedContent += `Connexions qui se renforcent.\n`;

          if (selectedKeywords.length > 2) {
            generatedContent += `${selectedKeywords[2]} et ${selectedKeywords.length > 3 ? selectedKeywords[3] : 'mémoire'},\n`;
          }

          generatedContent += `Dans le réseau de mes pensées,\n`;
          generatedContent += `${topic}, à jamais gravé.`;
        } else if (type === 'summary') {
          // Générer un résumé
          generatedContent = `Résumé sur "${topic}" :\n\n`;

          // Extraire les phrases les plus pertinentes
          const sentences = [];
          for (const { memory } of topMemories) {
            const memorySentences = memory.content.split(/[.!?]+/).filter(s => s.trim().length > 0);
            sentences.push(...memorySentences);
          }

          // Limiter le nombre de phrases
          const topSentences = sentences.slice(0, 5);

          if (topSentences.length > 0) {
            generatedContent += topSentences.join('. ') + '.';
          } else {
            generatedContent += `Pas assez d'informations pour générer un résumé sur ${topic}.`;
          }
        }

        // Créer l'objet de résultat
        const result = {
          content: generatedContent,
          type: type,
          topic: topic,
          sources: topMemories.map(item => ({
            id: item.memory.id,
            content: item.memory.content.substring(0, 50) + '...',
            zone: item.memory.zone,
            relevance: item.relevance
          }))
        };

        return result;
      },

      // Obtenir les mémoires par région du cerveau
      getMemoriesByBrainRegion: function(region) {
        if (region === 'all') {
          return [].concat(
            this.zones.instant,
            this.zones.short_term,
            this.zones.working,
            this.zones.medium_term,
            this.zones.long_term,
            this.zones.dream,
            this.zones.kyber,
            this.zones.archive
          );
        }

        // Obtenir la région du cerveau
        const brainRegion = this.brainRegions[region];
        if (!brainRegion) return [];

        // Obtenir les zones de mémoire associées à cette région
        const memoryZones = brainRegion.memories || [];

        // Rassembler les mémoires de ces zones
        const memories = [];
        for (const zone of memoryZones) {
          memories.push(...this.zones[zone]);
        }

        return memories;
      },

      // Obtenir l'analyse émotionnelle
      getEmotionalAnalysis: function(mode = 'current') {
        if (mode === 'current') {
          // Analyse de l'état émotionnel actuel
          return {
            mode: 'current',
            emotionalState: { ...this.emotionalState },
            dominantEmotion: this.getDominantEmotion()
          };
        } else if (mode === 'history') {
          // Analyse de l'historique émotionnel
          return {
            mode: 'history',
            emotionalHistory: [...this.emotionalHistory],
            currentState: { ...this.emotionalState }
          };
        } else if (mode === 'memories') {
          // Analyse des émotions par mémoire
          const allMemories = [].concat(
            this.zones.instant,
            this.zones.short_term,
            this.zones.working,
            this.zones.medium_term,
            this.zones.long_term,
            this.zones.dream,
            this.zones.kyber,
            this.zones.archive
          );

          // Compter les émotions dans les mémoires
          const emotionCounts = {
            joy: 0,
            sadness: 0,
            fear: 0,
            anger: 0,
            surprise: 0,
            trust: 0
          };

          // Mémoires avec des émotions
          const emotionalMemories = [];

          for (const memory of allMemories) {
            if (memory.emotions && Object.keys(memory.emotions).length > 0) {
              // Ajouter aux compteurs
              for (const emotion in memory.emotions) {
                if (emotionCounts[emotion] !== undefined) {
                  emotionCounts[emotion]++;
                }
              }

              // Ajouter à la liste des mémoires émotionnelles
              emotionalMemories.push(memory);
            }
          }

          // Trier les mémoires par nombre d'émotions
          emotionalMemories.sort((a, b) => {
            return Object.keys(b.emotions || {}).length - Object.keys(a.emotions || {}).length;
          });

          return {
            mode: 'memories',
            emotionCounts,
            emotionalMemories: emotionalMemories.slice(0, 10),
            totalEmotionalMemories: emotionalMemories.length
          };
        }

        return null;
      },

      // Obtenir les statistiques du tableau de bord
      getDashboardStats: function() {
        return {
          memoryStats: {
            totalMemories: Object.values(this.zones).reduce((sum, zone) => sum + zone.length, 0),
            zoneDistribution: Object.entries(this.zones).map(([zone, memories]) => ({
              zone,
              count: memories.length
            })),
            connectionCount: Object.values(this.zones).reduce((sum, zone) => {
              return sum + zone.reduce((connSum, memory) => connSum + memory.connections.length, 0);
            }, 0)
          },
          emotionalStats: {
            currentState: { ...this.emotionalState },
            dominantEmotion: this.getDominantEmotion(),
            emotionalStability: this.performanceStats.emotionalStability.length > 0
              ? this.performanceStats.emotionalStability[this.performanceStats.emotionalStability.length - 1].value
              : 0.5
          },
          performanceStats: {
            memoryUsage: this.performanceStats.memoryUsage.length > 0
              ? this.performanceStats.memoryUsage[this.performanceStats.memoryUsage.length - 1].value
              : 0,
            processingSpeed: this.performanceStats.processingSpeed.length > 0
              ? this.performanceStats.processingSpeed[this.performanceStats.processingSpeed.length - 1].value
              : 0,
            learningRate: this.performanceStats.learningRate.length > 0
              ? this.performanceStats.learningRate[this.performanceStats.learningRate.length - 1].value
              : 0
          },
          activityStats: {
            dreamMode: this.dreamMode,
            learningMode: this.learningMode,
            recentlyAccessedZones: this.getRecentlyAccessedZones()
          }
        };
      },

      // Obtenir les zones récemment accédées
      getRecentlyAccessedZones: function() {
        const zoneAccessCounts = {
          instant: 0,
          short_term: 0,
          working: 0,
          medium_term: 0,
          long_term: 0,
          dream: 0,
          kyber: 0,
          archive: 0
        };

        // Parcourir toutes les mémoires
        for (const zone in this.zones) {
          for (const memory of this.zones[zone]) {
            // Si la mémoire a été accédée récemment (dans les 5 dernières minutes)
            const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
            if (memory.lastAccessed && new Date(memory.lastAccessed) > fiveMinutesAgo) {
              zoneAccessCounts[zone]++;
            }
          }
        }

        // Convertir en tableau et trier
        return Object.entries(zoneAccessCounts)
          .map(([zone, count]) => ({ zone, count }))
          .sort((a, b) => b.count - a.count);
      },

      // Générer des tags sémantiques
      generateTags: function(content) {
        const tags = [];
        const keywords = [
          'assistant', 'mémoire', 'thermique', 'cerveau', 'interface',
          'luna', 'louna', 'lounas', 'simulation', 'test', 'utilisateur',
          'question', 'réponse', 'système', 'connexion', 'serveur'
        ];

        // Ajouter des tags basés sur le contenu
        keywords.forEach(keyword => {
          if (content.toLowerCase().includes(keyword.toLowerCase())) {
            tags.push(keyword);
          }
        });

        // Ajouter quelques tags aléatoires
        const randomTags = ['important', 'prioritaire', 'information', 'conversation', 'interaction'];
        const randomTag = randomTags[Math.floor(Math.random() * randomTags.length)];
        if (!tags.includes(randomTag)) {
          tags.push(randomTag);
        }

        return tags;
      },

      // Rechercher des mémoires
      searchMemories: function(query, limit = 5) {
        const results = [];
        const allMemories = [].concat(
          this.zones.instant,
          this.zones.short_term,
          this.zones.working,
          this.zones.medium_term,
          this.zones.long_term,
          this.zones.dream,
          this.zones.kyber,
          this.zones.archive
        );

        // Recherche simple basée sur le contenu
        for (const memory of allMemories) {
          if (memory.content.toLowerCase().includes(query.toLowerCase())) {
            results.push(memory);
            memory.accessCount += 1;

            if (results.length >= limit) {
              break;
            }
          }
        }

        return results;
      },

      // Migrer les mémoires entre les zones
      migrateMemories: function() {
        console.log('Migration des mémoires...');

        // Instant -> Short Term
        this.migrateZone('instant', 'short_term', 0.7);

        // Short Term -> Working
        this.migrateZone('short_term', 'working', 0.6);

        // Working -> Medium Term
        this.migrateZone('working', 'medium_term', 0.5);

        // Medium Term -> Long Term
        this.migrateZone('medium_term', 'long_term', 0.4);

        // Long Term -> Archive (rarement)
        this.migrateZone('long_term', 'archive', 0.1);

        // Certaines mémoires vont dans la zone de rêve
        this.migrateRandomToZone('medium_term', 'dream', 0.2);
        this.migrateRandomToZone('long_term', 'dream', 0.1);

        // Certaines mémoires importantes vont dans la zone kyber
        this.migrateImportantToZone('working', 'kyber', 0.8);
        this.migrateImportantToZone('medium_term', 'kyber', 0.9);

        console.log('Migration terminée');
      },

      // Migrer des mémoires d'une zone à une autre
      migrateZone: function(sourceZone, targetZone, threshold) {
        const sourceMemories = this.zones[sourceZone];
        const memoriesToMigrate = [];

        for (let i = 0; i < sourceMemories.length; i++) {
          const memory = sourceMemories[i];
          const ageInMinutes = (Date.now() - new Date(memory.timestamp).getTime()) / (1000 * 60);

          // Calculer la probabilité de migration
          let migrationProbability = 0;

          // Plus la mémoire est ancienne, plus elle a de chances d'être migrée
          if (ageInMinutes > 1) {
            migrationProbability += 0.3;
          }

          // Moins la mémoire est importante, plus elle a de chances d'être migrée
          migrationProbability += (1 - memory.importance) * 0.4;

          // Moins la mémoire est accédée, plus elle a de chances d'être migrée
          migrationProbability += Math.max(0, 1 - (memory.accessCount / 5)) * 0.3;

          if (migrationProbability > threshold) {
            memoriesToMigrate.push(i);
          }
        }

        // Migrer les mémoires (en commençant par la fin pour ne pas perturber les indices)
        for (let i = memoriesToMigrate.length - 1; i >= 0; i--) {
          const index = memoriesToMigrate[i];
          const memory = sourceMemories[index];

          // Mettre à jour la zone
          memory.zone = targetZone;

          // Ajouter à la zone cible
          this.zones[targetZone].push(memory);

          // Supprimer de la zone source
          sourceMemories.splice(index, 1);

          console.log(`Mémoire migrée: ${memory.id} de ${sourceZone} vers ${targetZone}`);
        }
      },

      // Migrer des mémoires aléatoires vers une zone
      migrateRandomToZone: function(sourceZone, targetZone, probability) {
        const sourceMemories = this.zones[sourceZone];

        for (let i = sourceMemories.length - 1; i >= 0; i--) {
          if (Math.random() < probability) {
            const memory = sourceMemories[i];

            // Mettre à jour la zone
            memory.zone = targetZone;

            // Ajouter à la zone cible
            this.zones[targetZone].push(memory);

            // Supprimer de la zone source
            sourceMemories.splice(i, 1);

            console.log(`Mémoire migrée aléatoirement: ${memory.id} de ${sourceZone} vers ${targetZone}`);
          }
        }
      },

      // Migrer des mémoires importantes vers une zone
      migrateImportantToZone: function(sourceZone, targetZone, importanceThreshold) {
        const sourceMemories = this.zones[sourceZone];

        for (let i = sourceMemories.length - 1; i >= 0; i--) {
          const memory = sourceMemories[i];

          if (memory.importance > importanceThreshold) {
            // Mettre à jour la zone
            memory.zone = targetZone;

            // Ajouter à la zone cible
            this.zones[targetZone].push(memory);

            // Supprimer de la zone source
            sourceMemories.splice(i, 1);

            console.log(`Mémoire importante migrée: ${memory.id} de ${sourceZone} vers ${targetZone}`);
          }
        }
      },

      // Obtenir les statistiques de la mémoire
      getStats: function() {
        const stats = {
          totalMemories: 0,
          zoneStats: {}
        };

        // Calculer les statistiques pour chaque zone
        for (const [zone, memories] of Object.entries(this.zones)) {
          stats.totalMemories += memories.length;
          stats.zoneStats[zone] = {
            count: memories.length,
            averageImportance: 0,
            averageAccessCount: 0
          };

          // Calculer les moyennes
          if (memories.length > 0) {
            let totalImportance = 0;
            let totalAccessCount = 0;

            for (const memory of memories) {
              totalImportance += memory.importance;
              totalAccessCount += memory.accessCount;
            }

            stats.zoneStats[zone].averageImportance = totalImportance / memories.length;
            stats.zoneStats[zone].averageAccessCount = totalAccessCount / memories.length;
          }
        }

        return stats;
      }
    };

    // Fonction pour connecter une interface
    function connectInterface(name, port) {
      // Si mode simulation, créer un faux socket
      if (simulationMode) {
        console.log(`Simulation de connexion pour ${name}`);

        // Créer un faux socket
        sockets[name] = {
          connected: true,
          disconnect: function() {
            this.connected = false;
            // Simuler l'événement de déconnexion
            setTimeout(() => {
              console.log(`${name} déconnecté (simulation)`);
              document.getElementById(`${name}-status`).classList.remove('connected');
              document.getElementById(`${name}-status`).classList.add('disconnected');
              document.getElementById(`${name}-status-text`).textContent = 'Déconnecté';
              document.getElementById(`${name}-connect-btn`).style.display = 'inline-block';
              document.getElementById(`${name}-disconnect-btn`).style.display = 'none';

              // Ajouter un message système
              addMessage(name, 'Déconnecté du serveur (mode simulation)', 'system');
            }, 100);
          },
          emit: function(event, data) {
            console.log(`Émission d'événement ${event} (simulation):`, data);

            // Ajouter le message à la mémoire thermique
            thermalMemory.addMemory(data.message, name);

            // Simuler une réponse après un court délai
            setTimeout(() => {
              // Rechercher des mémoires pertinentes
              const relatedMemories = thermalMemory.searchMemories(data.message, 3);

              // Générer une réponse basée sur le message et les mémoires
              let response = '';

              // Réponses spécifiques pour certaines commandes
              if (data.message.toLowerCase().startsWith('/stats')) {
                const stats = thermalMemory.getStats();
                response = `Statistiques de la mémoire thermique:\n`;
                response += `Total des mémoires: ${stats.totalMemories}\n\n`;

                for (const [zone, zoneStats] of Object.entries(stats.zoneStats)) {
                  response += `Zone ${zone}: ${zoneStats.count} mémoires\n`;
                  if (zoneStats.count > 0) {
                    response += `  Importance moyenne: ${zoneStats.averageImportance.toFixed(2)}\n`;
                    response += `  Accès moyen: ${zoneStats.averageAccessCount.toFixed(2)}\n`;
                  }
                }
              }
              else if (data.message.toLowerCase().startsWith('/search ')) {
                const query = data.message.substring(8);
                const results = thermalMemory.searchMemories(query, 5);

                if (results.length > 0) {
                  response = `Résultats de recherche pour "${query}":\n\n`;

                  results.forEach((memory, index) => {
                    response += `${index + 1}. ${memory.content.substring(0, 50)}...\n`;
                    response += `   Zone: ${memory.zone}, Importance: ${memory.importance.toFixed(2)}\n`;
                    response += `   Tags: ${memory.semanticTags.join(', ')}\n\n`;
                  });
                } else {
                  response = `Aucun résultat trouvé pour "${query}".`;
                }
              }
              else if (data.message.toLowerCase().startsWith('/help')) {
                response = `Commandes disponibles:\n`;
                response += `/stats - Afficher les statistiques de la mémoire thermique\n`;
                response += `/search <terme> - Rechercher dans la mémoire thermique\n`;
                response += `/help - Afficher cette aide\n`;
              }
              else {
                // Réponse standard basée sur l'interface
                const responses = {
                  'luna': `Je suis Luna, l'assistant principal. `,
                  'louna': `Bonjour, je suis Louna, l'assistant secondaire. `,
                  'lounas': `Lounas à votre service. `
                };

                response = responses[name];

                // Ajouter une référence aux mémoires trouvées si disponibles
                if (relatedMemories.length > 0) {
                  response += `J'ai trouvé ${relatedMemories.length} mémoires liées à votre message. `;

                  // Ajouter un extrait de la mémoire la plus pertinente
                  if (relatedMemories[0]) {
                    response += `Voici un extrait pertinent: "${relatedMemories[0].content.substring(0, 50)}..."`;
                  }
                } else {
                  response += `Je traite votre message: "${data.message}"`;
                }
              }

              // Ajouter une réponse simulée
              addMessage(name, response, 'agent');
            }, 1000);
          }
        };

        // Simuler l'événement de connexion
        setTimeout(() => {
          console.log(`${name} connecté (simulation)`);
          document.getElementById(`${name}-status`).classList.remove('disconnected');
          document.getElementById(`${name}-status`).classList.add('connected');
          document.getElementById(`${name}-status-text`).textContent = 'Connecté (simulation)';
          document.getElementById(`${name}-connect-btn`).style.display = 'none';
          document.getElementById(`${name}-disconnect-btn`).style.display = 'inline-block';

          // Ajouter un message système
          addMessage(name, 'Connecté au serveur (mode simulation)', 'system');
          addMessage(name, `Bienvenue dans l'interface ${name} (simulation). Tapez /help pour voir les commandes disponibles.`, 'agent');
        }, 500);

        return;
      }

      // Mode normal avec serveur réel
      // Déconnecter d'abord si déjà connecté
      if (sockets[name]) {
        sockets[name].disconnect();
      }

      // Créer une nouvelle connexion
      sockets[name] = io(`http://localhost:${port}`);

      // Gérer la connexion
      sockets[name].on('connect', () => {
        console.log(`${name} connecté`);
        document.getElementById(`${name}-status`).classList.remove('disconnected');
        document.getElementById(`${name}-status`).classList.add('connected');
        document.getElementById(`${name}-status-text`).textContent = 'Connecté';
        document.getElementById(`${name}-connect-btn`).style.display = 'none';
        document.getElementById(`${name}-disconnect-btn`).style.display = 'inline-block';

        // Ajouter un message système
        addMessage(name, 'Connecté au serveur', 'system');
      });

      // Gérer la déconnexion
      sockets[name].on('disconnect', () => {
        console.log(`${name} déconnecté`);
        document.getElementById(`${name}-status`).classList.remove('connected');
        document.getElementById(`${name}-status`).classList.add('disconnected');
        document.getElementById(`${name}-status-text`).textContent = 'Déconnecté';
        document.getElementById(`${name}-connect-btn`).style.display = 'inline-block';
        document.getElementById(`${name}-disconnect-btn`).style.display = 'none';

        // Ajouter un message système
        addMessage(name, 'Déconnecté du serveur', 'system');
      });

      // Gérer les réponses
      sockets[name].on(`${name} response`, (data) => {
        console.log(`Réponse ${name} reçue:`, data);
        addMessage(name, data.message, 'agent');
      });
    }

    // Fonction pour déconnecter une interface
    function disconnectInterface(name) {
      if (sockets[name]) {
        sockets[name].disconnect();
        sockets[name] = null;
      }
    }

    // Fonction pour envoyer un message
    function sendMessage(name) {
      const input = document.getElementById(`${name}-input`);
      const message = input.value.trim();

      if (message && sockets[name] && sockets[name].connected) {
        // Ajouter le message à l'interface
        addMessage(name, message, 'user');

        // Envoyer le message au serveur
        sockets[name].emit(`${name} message`, { message });

        // Vider le champ de saisie
        input.value = '';
      }
    }

    // Fonction pour ajouter un message à l'interface
    function addMessage(name, message, type) {
      const chat = document.getElementById(`${name}-chat`);
      const messageElement = document.createElement('div');
      messageElement.classList.add('message');

      if (type === 'user') {
        messageElement.classList.add('user-message');
        messageElement.textContent = `Vous: ${message}`;
      } else if (type === 'agent') {
        messageElement.classList.add('agent-message');
        messageElement.textContent = `${name}: ${message}`;
      } else {
        messageElement.style.color = '#666';
        messageElement.style.textAlign = 'center';
        messageElement.style.fontStyle = 'italic';
        messageElement.textContent = message;
      }

      chat.appendChild(messageElement);
      chat.scrollTop = chat.scrollHeight;
    }

    // Fonction pour mettre à jour la visualisation de la mémoire thermique
    function updateMemoryVisualization() {
      // Mettre à jour les statistiques
      const stats = thermalMemory.getStats();
      const statsGrid = document.getElementById('memory-stats-grid');

      // Vider la grille de statistiques
      statsGrid.innerHTML = '';

      // Ajouter la statistique du total des mémoires
      const totalMemoriesItem = document.createElement('div');
      totalMemoriesItem.className = 'memory-stat-item';
      totalMemoriesItem.innerHTML = `
        <div class="memory-stat-label">Total des mémoires</div>
        <div class="memory-stat-value">${stats.totalMemories}</div>
      `;
      statsGrid.appendChild(totalMemoriesItem);

      // Ajouter les statistiques de l'état émotionnel
      const emotionalStateItem = document.createElement('div');
      emotionalStateItem.className = 'memory-stat-item';

      // Créer le contenu HTML pour l'état émotionnel
      let emotionalStateHTML = `<div class="memory-stat-label">État émotionnel</div><div class="memory-stat-value">`;

      for (const [emotion, value] of Object.entries(thermalMemory.emotionalState)) {
        const emotionClass = `emotion-${emotion}`;
        emotionalStateHTML += `<span class="emotional-indicator ${emotionClass}" title="${emotion}: ${value.toFixed(2)}"></span>`;
      }

      emotionalStateHTML += `</div>`;
      emotionalStateItem.innerHTML = emotionalStateHTML;
      statsGrid.appendChild(emotionalStateItem);

      // Ajouter les statistiques pour chaque zone
      for (const [zone, zoneStats] of Object.entries(stats.zoneStats)) {
        // Mettre à jour le compteur de mémoires
        const countElement = document.getElementById(`${zone}-count`);
        if (countElement) {
          countElement.textContent = zoneStats.count;
        }

        // Ajouter la statistique de la zone
        const zoneItem = document.createElement('div');
        zoneItem.className = 'memory-stat-item';
        zoneItem.innerHTML = `
          <div class="memory-stat-label">Zone ${zone}</div>
          <div class="memory-stat-value">${zoneStats.count}</div>
        `;
        statsGrid.appendChild(zoneItem);

        // Mettre à jour les mémoires de la zone
        const zoneItemsContainer = document.getElementById(`${zone}-items`);
        if (zoneItemsContainer) {
          // Vider le conteneur
          zoneItemsContainer.innerHTML = '';

          // Ajouter les mémoires
          const memories = thermalMemory.zones[zone];

          for (const memory of memories) {
            const memoryItem = document.createElement('div');
            memoryItem.className = memory.dreaming ? 'memory-item dreaming' : 'memory-item';
            memoryItem.dataset.id = memory.id;

            // Limiter le contenu à 100 caractères
            const contentPreview = memory.content.length > 100
              ? memory.content.substring(0, 100) + '...'
              : memory.content;

            // Formater la date
            const date = new Date(memory.timestamp);
            const formattedDate = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;

            // Créer les indicateurs d'émotions
            let emotionsHTML = '';
            if (memory.emotions && Object.keys(memory.emotions).length > 0) {
              emotionsHTML = '<div class="memory-emotions">';
              for (const emotion of Object.keys(memory.emotions)) {
                emotionsHTML += `<span class="emotional-indicator emotion-${emotion}" title="${emotion}"></span>`;
              }
              emotionsHTML += '</div>';
            }

            // Créer le HTML de la mémoire
            memoryItem.innerHTML = `
              <div class="memory-content">${contentPreview}</div>
              ${emotionsHTML}
              <div class="memory-meta">
                <span>Source: ${memory.source}</span>
                <span>Importance: ${memory.importance.toFixed(2)}</span>
              </div>
              <div class="memory-meta">
                <span>Accès: ${memory.accessCount}</span>
                <span>Connexions: ${memory.connections.length}</span>
              </div>
              <div class="memory-meta">
                <span>${formattedDate}</span>
              </div>
              <div class="memory-tags">
                ${memory.semanticTags.map(tag => `<span class="memory-tag">${tag}</span>`).join('')}
              </div>
            `;

            // Ajouter un gestionnaire d'événements pour afficher les connexions
            memoryItem.addEventListener('click', function() {
              highlightMemoryConnections(memory.id);
            });

            zoneItemsContainer.appendChild(memoryItem);
          }
        }
      }
    }

    // Fonction pour mettre à jour la visualisation des connexions
    function updateConnectionsVisualization() {
      const connectionsGraph = document.getElementById('connections-graph');

      // Vider le graphe
      connectionsGraph.innerHTML = '';

      // Obtenir toutes les mémoires
      const allMemories = [].concat(
        thermalMemory.zones.instant,
        thermalMemory.zones.short_term,
        thermalMemory.zones.working,
        thermalMemory.zones.medium_term,
        thermalMemory.zones.long_term,
        thermalMemory.zones.dream,
        thermalMemory.zones.kyber,
        thermalMemory.zones.archive
      );

      // Limiter le nombre de mémoires à afficher
      const memoriesToShow = allMemories.slice(0, 20);

      // Créer les nœuds pour chaque mémoire
      for (let i = 0; i < memoriesToShow.length; i++) {
        const memory = memoriesToShow[i];

        // Calculer la position du nœud (disposition circulaire)
        const angle = (i / memoriesToShow.length) * 2 * Math.PI;
        const radius = connectionsGraph.clientWidth * 0.4;
        const x = connectionsGraph.clientWidth / 2 + radius * Math.cos(angle);
        const y = connectionsGraph.clientHeight / 2 + radius * Math.sin(angle);

        // Créer le nœud
        const node = document.createElement('div');
        node.className = 'memory-node';
        node.dataset.id = memory.id;
        node.style.left = `${x - 40}px`;
        node.style.top = `${y - 40}px`;

        // Couleur basée sur la zone
        const zoneColors = {
          'instant': 'rgba(255, 87, 34, 0.7)',
          'short_term': 'rgba(255, 193, 7, 0.7)',
          'working': 'rgba(76, 175, 80, 0.7)',
          'medium_term': 'rgba(33, 150, 243, 0.7)',
          'long_term': 'rgba(156, 39, 176, 0.7)',
          'dream': 'rgba(233, 30, 99, 0.7)',
          'kyber': 'rgba(0, 188, 212, 0.7)',
          'archive': 'rgba(96, 125, 139, 0.7)'
        };

        node.style.backgroundColor = zoneColors[memory.zone] || 'rgba(76, 175, 80, 0.7)';

        // Taille basée sur l'importance
        const size = 60 + memory.importance * 40;
        node.style.width = `${size}px`;
        node.style.height = `${size}px`;
        node.style.left = `${x - size/2}px`;
        node.style.top = `${y - size/2}px`;

        // Contenu du nœud
        node.innerHTML = `
          <div>${memory.source}</div>
          <div>${memory.zone}</div>
        `;

        // Ajouter un gestionnaire d'événements pour afficher les détails
        node.addEventListener('click', function() {
          highlightMemoryConnections(memory.id);
        });

        connectionsGraph.appendChild(node);
      }

      // Créer les connexions entre les nœuds
      for (const memory of memoriesToShow) {
        const sourceNode = connectionsGraph.querySelector(`.memory-node[data-id="${memory.id}"]`);
        if (!sourceNode) continue;

        // Obtenir la position du nœud source
        const sourceRect = sourceNode.getBoundingClientRect();
        const sourceX = sourceRect.left + sourceRect.width / 2 - connectionsGraph.getBoundingClientRect().left;
        const sourceY = sourceRect.top + sourceRect.height / 2 - connectionsGraph.getBoundingClientRect().top;

        // Créer une connexion pour chaque mémoire connectée
        for (const connection of memory.connections) {
          // Vérifier si la mémoire cible est affichée
          const targetNode = connectionsGraph.querySelector(`.memory-node[data-id="${connection.id}"]`);
          if (!targetNode) continue;

          // Obtenir la position du nœud cible
          const targetRect = targetNode.getBoundingClientRect();
          const targetX = targetRect.left + targetRect.width / 2 - connectionsGraph.getBoundingClientRect().left;
          const targetY = targetRect.top + targetRect.height / 2 - connectionsGraph.getBoundingClientRect().top;

          // Calculer la longueur et l'angle de la connexion
          const dx = targetX - sourceX;
          const dy = targetY - sourceY;
          const length = Math.sqrt(dx * dx + dy * dy);
          const angle = Math.atan2(dy, dx) * 180 / Math.PI;

          // Créer la connexion
          const connectionElement = document.createElement('div');
          connectionElement.className = 'memory-connection';
          connectionElement.dataset.source = memory.id;
          connectionElement.dataset.target = connection.id;

          // Positionner et dimensionner la connexion
          connectionElement.style.width = `${length}px`;
          connectionElement.style.height = `${2 + connection.strength * 3}px`; // Épaisseur basée sur la force
          connectionElement.style.left = `${sourceX}px`;
          connectionElement.style.top = `${sourceY}px`;
          connectionElement.style.transform = `rotate(${angle}deg)`;

          // Opacité basée sur la force de la connexion
          connectionElement.style.opacity = 0.1 + connection.strength * 0.5;

          connectionsGraph.appendChild(connectionElement);
        }
      }
    }

    // Fonction pour mettre à jour la visualisation de l'activité cérébrale
    function updateBrainActivityVisualization() {
      // Générer de nouvelles données d'activité cérébrale
      thermalMemory.generateBrainActivity();

      const activityGraph = document.getElementById('brain-activity-graph');
      const viewMode = document.getElementById('activity-view-select').value;

      // Vider le graphe
      activityGraph.innerHTML = '';

      // Afficher la visualisation en fonction du mode sélectionné
      if (viewMode === 'heatmap') {
        // Afficher la carte de chaleur
        for (const cell of thermalMemory.brainActivity.heatmap) {
          const cellElement = document.createElement('div');
          cellElement.className = 'heatmap-cell';

          // Positionner la cellule
          cellElement.style.left = `${cell.x}px`;
          cellElement.style.top = `${cell.y}px`;
          cellElement.style.width = `${cell.width}px`;
          cellElement.style.height = `${cell.height}px`;

          // Couleur basée sur l'activité
          const r = Math.floor(255 * cell.activity);
          const g = Math.floor(100 + 155 * (1 - cell.activity));
          const b = Math.floor(50 + 100 * (1 - cell.activity));

          cellElement.style.backgroundColor = `rgba(${r}, ${g}, ${b}, ${0.3 + cell.activity * 0.7})`;

          activityGraph.appendChild(cellElement);
        }
      } else if (viewMode === 'waves') {
        // Afficher les ondes cérébrales
        const waveTypes = ['alpha', 'beta', 'theta', 'delta'];

        for (const waveType of waveTypes) {
          const waveData = thermalMemory.brainActivity.waveData[waveType];

          // Créer un SVG pour l'onde
          const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
          svg.setAttribute('class', 'brain-wave');
          svg.setAttribute('viewBox', '0 0 500 300');
          svg.setAttribute('preserveAspectRatio', 'none');

          // Créer le chemin de l'onde
          const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
          path.setAttribute('class', `wave-line ${waveType}-wave`);

          // Générer le chemin
          let pathData = '';

          for (let i = 0; i < waveData.length; i++) {
            const point = waveData[i];

            if (i === 0) {
              pathData += `M ${point.x} ${point.y}`;
            } else {
              pathData += ` L ${point.x} ${point.y}`;
            }
          }

          path.setAttribute('d', pathData);

          svg.appendChild(path);
          activityGraph.appendChild(svg);
        }
      } else if (viewMode === 'neurons') {
        // Afficher le réseau neuronal
        for (const neuron of thermalMemory.brainActivity.activeNeurons) {
          // Créer le neurone
          const neuronElement = document.createElement('div');
          neuronElement.className = 'neuron';

          // Positionner le neurone
          neuronElement.style.left = `${neuron.x}px`;
          neuronElement.style.top = `${neuron.y}px`;

          // Taille et opacité basées sur l'activité
          const size = 3 + neuron.activity * 5;
          neuronElement.style.width = `${size}px`;
          neuronElement.style.height = `${size}px`;
          neuronElement.style.opacity = 0.3 + neuron.activity * 0.7;

          activityGraph.appendChild(neuronElement);
        }

        // Créer des connexions aléatoires entre les neurones
        const neuronElements = activityGraph.querySelectorAll('.neuron');

        for (let i = 0; i < neuronElements.length; i++) {
          // Créer 1 à 3 connexions par neurone
          const connectionCount = Math.floor(Math.random() * 3) + 1;

          for (let j = 0; j < connectionCount; j++) {
            // Sélectionner un neurone cible aléatoire
            const targetIndex = Math.floor(Math.random() * neuronElements.length);

            if (targetIndex !== i) {
              const sourceNeuron = neuronElements[i];
              const targetNeuron = neuronElements[targetIndex];

              // Obtenir les positions
              const sourceX = parseFloat(sourceNeuron.style.left) + parseFloat(sourceNeuron.style.width) / 2;
              const sourceY = parseFloat(sourceNeuron.style.top) + parseFloat(sourceNeuron.style.height) / 2;
              const targetX = parseFloat(targetNeuron.style.left) + parseFloat(targetNeuron.style.width) / 2;
              const targetY = parseFloat(targetNeuron.style.top) + parseFloat(targetNeuron.style.height) / 2;

              // Calculer la longueur et l'angle de la connexion
              const dx = targetX - sourceX;
              const dy = targetY - sourceY;
              const length = Math.sqrt(dx * dx + dy * dy);
              const angle = Math.atan2(dy, dx) * 180 / Math.PI;

              // Créer la connexion
              const connectionElement = document.createElement('div');
              connectionElement.className = 'neuron-connection';

              // Positionner et dimensionner la connexion
              connectionElement.style.width = `${length}px`;
              connectionElement.style.height = '1px';
              connectionElement.style.left = `${sourceX}px`;
              connectionElement.style.top = `${sourceY}px`;
              connectionElement.style.transform = `rotate(${angle}deg)`;

              activityGraph.appendChild(connectionElement);
            }
          }
        }
      }
    }

    // Fonction pour mettre à jour la visualisation des prédictions
    function updatePredictionVisualization(text) {
      const predictions = thermalMemory.predictFromText(text);
      const predictionResults = document.getElementById('prediction-results');

      // Vider les résultats précédents
      predictionResults.innerHTML = '';

      if (predictions.length === 0) {
        predictionResults.innerHTML = '<p>Aucune prédiction trouvée pour ce texte.</p>';
        return;
      }

      // Afficher les prédictions
      for (const prediction of predictions) {
        const predictionItem = document.createElement('div');
        predictionItem.className = 'prediction-item';

        // Formater le score en pourcentage
        const scorePercent = Math.round(prediction.score * 100);

        // Créer le contenu de la prédiction
        predictionItem.innerHTML = `
          <div class="prediction-score">${scorePercent}%</div>
          <div class="prediction-content">${prediction.memory.content}</div>
          <div class="prediction-meta">
            <span>Source: ${prediction.memory.source}</span>
            <span>Zone: ${prediction.memory.zone}</span>
          </div>
        `;

        predictionResults.appendChild(predictionItem);
      }
    }

    // Fonction pour mettre à jour la visualisation 3D du cerveau
    function updateBrain3DVisualization() {
      const brain3DContainer = document.getElementById('brain-3d-container');
      const selectedRegion = document.getElementById('brain-region-select').value;

      // Vider le conteneur
      brain3DContainer.innerHTML = '';

      // Créer l'infobulle
      const tooltip = document.createElement('div');
      tooltip.className = 'brain-region-tooltip';
      brain3DContainer.appendChild(tooltip);

      // Créer les régions du cerveau
      for (const [regionId, region] of Object.entries(thermalMemory.brainRegions)) {
        // Si une région spécifique est sélectionnée et que ce n'est pas celle-ci, passer
        if (selectedRegion !== 'all' && selectedRegion !== regionId) continue;

        // Créer l'élément de région
        const regionElement = document.createElement('div');
        regionElement.className = 'brain-region';
        regionElement.dataset.region = regionId;

        // Positionner la région
        regionElement.style.left = `${region.x}px`;
        regionElement.style.top = `${region.y}px`;

        // Taille basée sur le rayon
        regionElement.style.width = `${region.radius * 2}px`;
        regionElement.style.height = `${region.radius * 2}px`;

        // Couleur basée sur l'activité
        const memories = thermalMemory.getMemoriesByBrainRegion(regionId);
        const activityLevel = Math.min(1, memories.length / 10);

        // Couleur basée sur la région
        let color;
        if (regionId === 'frontal') {
          color = `rgba(76, 175, 80, ${0.3 + activityLevel * 0.5})`;
        } else if (regionId === 'temporal') {
          color = `rgba(33, 150, 243, ${0.3 + activityLevel * 0.5})`;
        } else if (regionId === 'parietal') {
          color = `rgba(255, 193, 7, ${0.3 + activityLevel * 0.5})`;
        } else if (regionId === 'occipital') {
          color = `rgba(156, 39, 176, ${0.3 + activityLevel * 0.5})`;
        } else if (regionId === 'limbic') {
          color = `rgba(233, 30, 99, ${0.3 + activityLevel * 0.5})`;
        }

        regionElement.style.backgroundColor = color;

        // Ajouter des gestionnaires d'événements pour l'infobulle
        regionElement.addEventListener('mouseover', function(e) {
          tooltip.innerHTML = `
            <div><strong>${region.name}</strong></div>
            <div>${region.description}</div>
            <div>Mémoires: ${memories.length}</div>
          `;

          tooltip.style.left = `${e.pageX - brain3DContainer.getBoundingClientRect().left + 10}px`;
          tooltip.style.top = `${e.pageY - brain3DContainer.getBoundingClientRect().top + 10}px`;
          tooltip.style.opacity = 1;
        });

        regionElement.addEventListener('mousemove', function(e) {
          tooltip.style.left = `${e.pageX - brain3DContainer.getBoundingClientRect().left + 10}px`;
          tooltip.style.top = `${e.pageY - brain3DContainer.getBoundingClientRect().top + 10}px`;
        });

        regionElement.addEventListener('mouseout', function() {
          tooltip.style.opacity = 0;
        });

        // Ajouter un gestionnaire d'événements pour afficher les mémoires
        regionElement.addEventListener('click', function() {
          // Mettre en évidence la région
          document.querySelectorAll('.brain-region').forEach(r => r.classList.remove('active'));
          this.classList.add('active');

          // Afficher les mémoires de cette région
          alert(`${region.name}: ${memories.length} mémoires\n\n${memories.slice(0, 5).map(m => m.content.substring(0, 50) + '...').join('\n\n')}`);
        });

        brain3DContainer.appendChild(regionElement);
      }
    }

    // Fonction pour mettre à jour la visualisation de l'analyse émotionnelle
    function updateEmotionalAnalysisVisualization() {
      const mode = document.getElementById('emotion-analysis-mode').value;
      const analysisContainer = document.getElementById('emotion-analysis-container');
      const chartContainer = document.getElementById('emotion-chart-container');

      // Obtenir l'analyse émotionnelle
      const analysis = thermalMemory.getEmotionalAnalysis(mode);

      // Vider les conteneurs
      analysisContainer.innerHTML = '';
      chartContainer.innerHTML = '';

      if (!analysis) {
        analysisContainer.innerHTML = '<p>Aucune analyse émotionnelle disponible.</p>';
        return;
      }

      if (mode === 'current') {
        // Afficher l'état émotionnel actuel
        analysisContainer.innerHTML = `
          <h3>État émotionnel actuel</h3>
          <p>Émotion dominante: <strong>${analysis.dominantEmotion || 'Neutre'}</strong></p>
        `;

        // Créer les jauges d'émotions
        for (const [emotion, value] of Object.entries(analysis.emotionalState)) {
          const gauge = document.createElement('div');
          gauge.className = 'emotion-gauge';

          // Couleur basée sur l'émotion
          let color;
          if (emotion === 'joy') color = '#ffeb3b';
          else if (emotion === 'sadness') color = '#2196f3';
          else if (emotion === 'fear') color = '#9c27b0';
          else if (emotion === 'anger') color = '#f44336';
          else if (emotion === 'surprise') color = '#00bcd4';
          else if (emotion === 'trust') color = '#4caf50';

          // Créer la jauge
          gauge.innerHTML = `
            <div class="emotion-gauge-label">
              <span>${emotion}</span>
              <span>${Math.round(value * 100)}%</span>
            </div>
            <div class="emotion-gauge-fill" style="width: ${value * 100}%; background-color: ${color};"></div>
          `;

          analysisContainer.appendChild(gauge);
        }
      } else if (mode === 'history') {
        // Afficher l'historique émotionnel
        analysisContainer.innerHTML = `
          <h3>Historique émotionnel</h3>
          <p>Nombre d'entrées: ${analysis.emotionalHistory.length}</p>
        `;

        // Créer un graphique simple pour l'historique
        if (analysis.emotionalHistory.length > 0) {
          const emotionColors = {
            joy: '#ffeb3b',
            sadness: '#2196f3',
            fear: '#9c27b0',
            anger: '#f44336',
            surprise: '#00bcd4',
            trust: '#4caf50'
          };

          // Créer la légende
          const legend = document.createElement('div');
          legend.className = 'emotion-legend';

          for (const [emotion, color] of Object.entries(emotionColors)) {
            const legendItem = document.createElement('div');
            legendItem.className = 'emotion-legend-item';
            legendItem.innerHTML = `
              <div class="emotion-legend-color" style="background-color: ${color};"></div>
              <span>${emotion}</span>
            `;

            legend.appendChild(legendItem);
          }

          analysisContainer.appendChild(legend);

          // Créer un graphique simple
          const chart = document.createElement('div');
          chart.style.height = '200px';
          chart.style.position = 'relative';
          chart.style.marginTop = '20px';

          // Limiter l'historique aux 20 dernières entrées
          const historyToShow = analysis.emotionalHistory.slice(-20);

          // Créer les lignes pour chaque émotion
          for (const emotion of Object.keys(emotionColors)) {
            const line = document.createElement('div');
            line.style.position = 'absolute';
            line.style.left = '0';
            line.style.right = '0';
            line.style.height = '2px';
            line.style.backgroundColor = emotionColors[emotion];
            line.style.bottom = '0';
            line.style.zIndex = '1';

            // Créer les points pour chaque entrée
            for (let i = 0; i < historyToShow.length; i++) {
              const entry = historyToShow[i];
              const value = entry.emotionalState[emotion];

              const point = document.createElement('div');
              point.style.position = 'absolute';
              point.style.width = '6px';
              point.style.height = '6px';
              point.style.borderRadius = '50%';
              point.style.backgroundColor = emotionColors[emotion];
              point.style.left = `${(i / (historyToShow.length - 1)) * 100}%`;
              point.style.bottom = `${value * 200}px`;
              point.style.transform = 'translate(-50%, 50%)';

              // Ajouter une infobulle
              point.title = `${emotion}: ${Math.round(value * 100)}%`;

              line.appendChild(point);
            }

            chart.appendChild(line);
          }

          chartContainer.appendChild(chart);
        }
      } else if (mode === 'memories') {
        // Afficher les émotions par mémoire
        analysisContainer.innerHTML = `
          <h3>Émotions par mémoire</h3>
          <p>Mémoires émotionnelles: ${analysis.totalEmotionalMemories}</p>
        `;

        // Créer un graphique pour la distribution des émotions
        const emotionColors = {
          joy: '#ffeb3b',
          sadness: '#2196f3',
          fear: '#9c27b0',
          anger: '#f44336',
          surprise: '#00bcd4',
          trust: '#4caf50'
        };

        // Créer la distribution des émotions
        const distribution = document.createElement('div');
        distribution.className = 'emotion-distribution';

        const totalEmotions = Object.values(analysis.emotionCounts).reduce((sum, count) => sum + count, 0);

        for (const [emotion, count] of Object.entries(analysis.emotionCounts)) {
          if (totalEmotions > 0) {
            const percentage = (count / totalEmotions) * 100;

            const bar = document.createElement('div');
            bar.className = 'emotion-bar';
            bar.style.backgroundColor = emotionColors[emotion];
            bar.style.width = `${percentage}%`;
            bar.title = `${emotion}: ${count} (${Math.round(percentage)}%)`;

            distribution.appendChild(bar);
          }
        }

        analysisContainer.appendChild(distribution);

        // Créer la légende
        const legend = document.createElement('div');
        legend.className = 'emotion-legend';

        for (const [emotion, color] of Object.entries(emotionColors)) {
          const count = analysis.emotionCounts[emotion] || 0;

          const legendItem = document.createElement('div');
          legendItem.className = 'emotion-legend-item';
          legendItem.innerHTML = `
            <div class="emotion-legend-color" style="background-color: ${color};"></div>
            <span>${emotion}: ${count}</span>
          `;

          legend.appendChild(legendItem);
        }

        analysisContainer.appendChild(legend);

        // Afficher les mémoires émotionnelles
        if (analysis.emotionalMemories.length > 0) {
          const memoriesContainer = document.createElement('div');
          memoriesContainer.style.marginTop = '20px';

          for (const memory of analysis.emotionalMemories) {
            const memoryItem = document.createElement('div');
            memoryItem.className = 'emotion-memory-item';

            // Créer les indicateurs d'émotions
            let emotionsHTML = '<div class="memory-emotions">';
            for (const emotion of Object.keys(memory.emotions || {})) {
              emotionsHTML += `<span class="emotional-indicator emotion-${emotion}" title="${emotion}"></span>`;
            }
            emotionsHTML += '</div>';

            // Créer le contenu de la mémoire
            memoryItem.innerHTML = `
              <div class="memory-content">${memory.content}</div>
              ${emotionsHTML}
              <div class="memory-meta">
                <span>Source: ${memory.source}</span>
                <span>Zone: ${memory.zone}</span>
              </div>
            `;

            memoriesContainer.appendChild(memoryItem);
          }

          chartContainer.appendChild(memoriesContainer);
        }
      }
    }

    // Fonction pour mettre à jour la visualisation de la génération de contenu
    function updateContentGenerationVisualization(topic, type) {
      const generationResults = document.getElementById('generation-results');

      // Générer le contenu
      const result = thermalMemory.generateContent(topic, type);

      // Vider les résultats précédents
      generationResults.innerHTML = '';

      // Créer le conteneur de résultats
      const resultContainer = document.createElement('div');

      // Ajouter le contenu généré
      const contentElement = document.createElement('div');
      contentElement.className = 'generated-content';
      contentElement.textContent = result.content;

      resultContainer.appendChild(contentElement);

      // Ajouter les sources si disponibles
      if (result.sources && result.sources.length > 0) {
        const sourcesElement = document.createElement('div');
        sourcesElement.className = 'generated-sources';
        sourcesElement.innerHTML = '<h4>Sources:</h4>';

        const sourcesList = document.createElement('ul');

        for (const source of result.sources) {
          const sourceItem = document.createElement('li');
          sourceItem.innerHTML = `
            <span>${source.content}</span>
            <span class="source-meta">Zone: ${source.zone}, Pertinence: ${Math.round(source.relevance * 100)}%</span>
          `;

          sourcesList.appendChild(sourceItem);
        }

        sourcesElement.appendChild(sourcesList);
        resultContainer.appendChild(sourcesElement);
      }

      generationResults.appendChild(resultContainer);
    }

    // Fonction pour mettre à jour la visualisation du tableau de bord
    function updateDashboardVisualization() {
      const dashboardGrid = document.getElementById('dashboard-grid');

      // Obtenir les statistiques du tableau de bord
      const stats = thermalMemory.getDashboardStats();

      // Vider le tableau de bord
      dashboardGrid.innerHTML = '';

      // Créer le widget des statistiques de mémoire
      const memoryWidget = createDashboardWidget('Statistiques de mémoire', `
        <div>Total des mémoires: <strong>${stats.memoryStats.totalMemories}</strong></div>
        <div>Connexions: <strong>${stats.memoryStats.connectionCount}</strong></div>
        <div style="margin-top: 10px;">Distribution par zone:</div>
        <div style="display: flex; height: 100px; margin-top: 5px;">
          ${stats.memoryStats.zoneDistribution.map(zone => `
            <div style="flex: 1; position: relative; margin: 0 1px;">
              <div style="position: absolute; bottom: 0; left: 0; right: 0; background-color: #2196f3; height: ${Math.min(100, zone.count * 5)}px;" title="${zone.zone}: ${zone.count}"></div>
              <div style="position: absolute; bottom: -20px; left: 0; right: 0; text-align: center; font-size: 10px;">${zone.zone.substring(0, 3)}</div>
            </div>
          `).join('')}
        </div>
      `);

      // Créer le widget des statistiques émotionnelles
      const emotionWidget = createDashboardWidget('État émotionnel', `
        <div>Émotion dominante: <strong>${stats.emotionalStats.dominantEmotion || 'Neutre'}</strong></div>
        <div>Stabilité émotionnelle: <strong>${Math.round(stats.emotionalStats.emotionalStability * 100)}%</strong></div>
        <div style="margin-top: 10px;">État actuel:</div>
        ${Object.entries(stats.emotionalStats.currentState).map(([emotion, value]) => {
          let color;
          if (emotion === 'joy') color = '#ffeb3b';
          else if (emotion === 'sadness') color = '#2196f3';
          else if (emotion === 'fear') color = '#9c27b0';
          else if (emotion === 'anger') color = '#f44336';
          else if (emotion === 'surprise') color = '#00bcd4';
          else if (emotion === 'trust') color = '#4caf50';

          return `
            <div style="margin-top: 5px;">
              <div style="display: flex; justify-content: space-between; font-size: 12px;">
                <span>${emotion}</span>
                <span>${Math.round(value * 100)}%</span>
              </div>
              <div style="height: 10px; background-color: #f5f5f5; border-radius: 5px; overflow: hidden;">
                <div style="height: 100%; width: ${value * 100}%; background-color: ${color};"></div>
              </div>
            </div>
          `;
        }).join('')}
      `);

      // Créer le widget des statistiques de performance
      const performanceWidget = createDashboardWidget('Performance', `
        <div>Utilisation de la mémoire: <strong>${Math.round(stats.performanceStats.memoryUsage * 100)}%</strong></div>
        <div>Vitesse de traitement: <strong>${Math.round(stats.performanceStats.processingSpeed * 100)}%</strong></div>
        <div>Taux d'apprentissage: <strong>${Math.round(stats.performanceStats.learningRate * 100)}%</strong></div>
        <div style="margin-top: 10px;">Graphique de performance:</div>
        <div style="display: flex; height: 80px; margin-top: 5px; align-items: flex-end;">
          <div style="flex: 1; height: ${stats.performanceStats.memoryUsage * 100}%; background-color: #f44336; margin: 0 5px;" title="Utilisation de la mémoire: ${Math.round(stats.performanceStats.memoryUsage * 100)}%"></div>
          <div style="flex: 1; height: ${stats.performanceStats.processingSpeed * 100}%; background-color: #2196f3; margin: 0 5px;" title="Vitesse de traitement: ${Math.round(stats.performanceStats.processingSpeed * 100)}%"></div>
          <div style="flex: 1; height: ${stats.performanceStats.learningRate * 100}%; background-color: #4caf50; margin: 0 5px;" title="Taux d'apprentissage: ${Math.round(stats.performanceStats.learningRate * 100)}%"></div>
        </div>
        <div style="display: flex; justify-content: space-around; font-size: 10px; margin-top: 5px;">
          <span>Mémoire</span>
          <span>Vitesse</span>
          <span>Apprentissage</span>
        </div>
      `);

      // Créer le widget des statistiques d'activité
      const activityWidget = createDashboardWidget('Activité', `
        <div>Mode rêve: <strong>${stats.activityStats.dreamMode ? 'Activé' : 'Désactivé'}</strong></div>
        <div>Mode apprentissage: <strong>${stats.activityStats.learningMode ? 'Activé' : 'Désactivé'}</strong></div>
        <div style="margin-top: 10px;">Zones récemment accédées:</div>
        <ul style="margin-top: 5px; padding-left: 20px;">
          ${stats.activityStats.recentlyAccessedZones.slice(0, 3).map(zone => `
            <li>${zone.zone}: ${zone.count} accès</li>
          `).join('')}
        </ul>
      `);

      // Ajouter les widgets au tableau de bord
      dashboardGrid.appendChild(memoryWidget);
      dashboardGrid.appendChild(emotionWidget);
      dashboardGrid.appendChild(performanceWidget);
      dashboardGrid.appendChild(activityWidget);
    }

    // Fonction pour créer un widget du tableau de bord
    function createDashboardWidget(title, content) {
      const widget = document.createElement('div');
      widget.className = 'dashboard-widget';

      widget.innerHTML = `
        <div class="dashboard-widget-title">${title}</div>
        <div class="dashboard-widget-content">
          ${content}
        </div>
      `;

      return widget;
    }

    // Fonction pour mettre en évidence les connexions d'une mémoire
    function highlightMemoryConnections(memoryId) {
      // Trouver la mémoire
      let selectedMemory = null;

      // Parcourir toutes les zones pour trouver la mémoire
      for (const zone in thermalMemory.zones) {
        for (const memory of thermalMemory.zones[zone]) {
          if (memory.id === memoryId) {
            selectedMemory = memory;
            break;
          }
        }
        if (selectedMemory) break;
      }

      if (!selectedMemory) return;

      // Afficher les détails de la mémoire
      alert(`Mémoire: ${selectedMemory.content}\n\nZone: ${selectedMemory.zone}\nImportance: ${selectedMemory.importance.toFixed(2)}\nConnexions: ${selectedMemory.connections.length}\nTags: ${selectedMemory.semanticTags.join(', ')}`);

      // Si la visualisation des connexions est visible, mettre en évidence les connexions
      if (document.getElementById('connections-view').style.display !== 'none') {
        // Réinitialiser toutes les connexions
        const connections = document.querySelectorAll('.memory-connection');
        connections.forEach(conn => {
          conn.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
          conn.style.zIndex = 1;
        });

        // Mettre en évidence les connexions de la mémoire sélectionnée
        const relatedConnections = document.querySelectorAll(`.memory-connection[data-source="${memoryId}"], .memory-connection[data-target="${memoryId}"]`);
        relatedConnections.forEach(conn => {
          conn.style.backgroundColor = 'rgba(255, 87, 34, 0.7)';
          conn.style.zIndex = 3;
        });

        // Mettre en évidence les nœuds connectés
        const nodes = document.querySelectorAll('.memory-node');
        nodes.forEach(node => {
          node.style.zIndex = 2;
          node.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
        });

        // Mettre en évidence le nœud sélectionné
        const selectedNode = document.querySelector(`.memory-node[data-id="${memoryId}"]`);
        if (selectedNode) {
          selectedNode.style.zIndex = 4;
          selectedNode.style.boxShadow = '0 0 15px rgba(255, 87, 34, 0.7)';
        }

        // Mettre en évidence les nœuds connectés
        for (const connection of selectedMemory.connections) {
          const connectedNode = document.querySelector(`.memory-node[data-id="${connection.id}"]`);
          if (connectedNode) {
            connectedNode.style.zIndex = 3;
            connectedNode.style.boxShadow = '0 0 10px rgba(76, 175, 80, 0.7)';
          }
        }
      }
    }

    // Gérer la navigation entre les onglets
    document.getElementById('show-chat-btn').addEventListener('click', function() {
      document.getElementById('chat-tab').classList.add('active');
      document.getElementById('memory-tab').classList.remove('active');
      document.getElementById('show-chat-btn').classList.add('active');
      document.getElementById('show-memory-btn').classList.remove('active');
    });

    document.getElementById('show-memory-btn').addEventListener('click', function() {
      document.getElementById('chat-tab').classList.remove('active');
      document.getElementById('memory-tab').classList.add('active');
      document.getElementById('show-chat-btn').classList.remove('active');
      document.getElementById('show-memory-btn').classList.add('active');

      // Mettre à jour la visualisation de la mémoire thermique
      updateMemoryVisualization();
    });

    // Gérer les boutons de contrôle de la mémoire thermique
    document.getElementById('dream-mode-btn').addEventListener('click', function() {
      if (thermalMemory.dreamMode) {
        thermalMemory.deactivateDreamMode();
        this.textContent = 'Activer mode rêve';
        this.classList.remove('active');
      } else {
        thermalMemory.activateDreamMode();
        this.textContent = 'Désactiver mode rêve';
        this.classList.add('active');
      }
    });

    document.getElementById('learning-mode-btn').addEventListener('click', function() {
      if (thermalMemory.learningMode) {
        thermalMemory.deactivateLearningMode();
        this.textContent = 'Activer apprentissage';
        this.classList.remove('active');
      } else {
        thermalMemory.activateLearningMode();
        this.textContent = 'Désactiver apprentissage';
        this.classList.add('active');
      }
    });

    document.getElementById('show-connections-btn').addEventListener('click', function() {
      const connectionsView = document.getElementById('connections-view');
      const brainActivityView = document.getElementById('brain-activity-view');
      const predictionView = document.getElementById('prediction-view');

      // Masquer les autres vues
      brainActivityView.style.display = 'none';
      predictionView.style.display = 'none';
      document.getElementById('show-brain-activity-btn').classList.remove('active');

      if (connectionsView.style.display === 'none') {
        connectionsView.style.display = 'block';
        this.textContent = 'Masquer connexions';
        this.classList.add('active');

        // Mettre à jour la visualisation des connexions
        updateConnectionsVisualization();
      } else {
        connectionsView.style.display = 'none';
        this.textContent = 'Afficher connexions';
        this.classList.remove('active');
      }
    });

    document.getElementById('show-brain-activity-btn').addEventListener('click', function() {
      const connectionsView = document.getElementById('connections-view');
      const brainActivityView = document.getElementById('brain-activity-view');
      const predictionView = document.getElementById('prediction-view');

      // Masquer les autres vues
      connectionsView.style.display = 'none';
      predictionView.style.display = 'none';
      document.getElementById('show-connections-btn').classList.remove('active');

      if (brainActivityView.style.display === 'none') {
        brainActivityView.style.display = 'block';
        this.textContent = 'Masquer activité';
        this.classList.add('active');

        // Mettre à jour la visualisation de l'activité cérébrale
        updateBrainActivityVisualization();

        // Mettre à jour périodiquement l'activité cérébrale
        const activityInterval = setInterval(function() {
          if (brainActivityView.style.display === 'none') {
            clearInterval(activityInterval);
            return;
          }

          if (!thermalMemory.brainActivity.paused) {
            updateBrainActivityVisualization();
          }
        }, 1000);
      } else {
        brainActivityView.style.display = 'none';
        this.textContent = 'Activité cérébrale';
        this.classList.remove('active');
      }
    });

    // Gérer le bouton de pause de l'activité cérébrale
    document.getElementById('pause-activity-btn').addEventListener('click', function() {
      thermalMemory.brainActivity.paused = !thermalMemory.brainActivity.paused;

      if (thermalMemory.brainActivity.paused) {
        this.textContent = 'Reprendre';
      } else {
        this.textContent = 'Pause';
        updateBrainActivityVisualization();
      }
    });

    // Gérer le changement de mode de visualisation de l'activité cérébrale
    document.getElementById('activity-view-select').addEventListener('change', function() {
      updateBrainActivityVisualization();
    });

    document.getElementById('create-random-btn').addEventListener('click', function() {
      thermalMemory.createRandomMemories(5);
      updateMemoryVisualization();

      // Mettre à jour la visualisation des connexions si elle est visible
      if (document.getElementById('connections-view').style.display !== 'none') {
        updateConnectionsVisualization();
      }
    });

    document.getElementById('consolidate-btn').addEventListener('click', function() {
      thermalMemory.consolidateMemories();
    });

    document.getElementById('forget-btn').addEventListener('click', function() {
      thermalMemory.forgetMemories();
    });

    document.getElementById('predict-btn').addEventListener('click', function() {
      const connectionsView = document.getElementById('connections-view');
      const brainActivityView = document.getElementById('brain-activity-view');
      const predictionView = document.getElementById('prediction-view');

      // Masquer les autres vues
      connectionsView.style.display = 'none';
      brainActivityView.style.display = 'none';
      document.getElementById('show-connections-btn').classList.remove('active');
      document.getElementById('show-brain-activity-btn').classList.remove('active');

      if (predictionView.style.display === 'none') {
        predictionView.style.display = 'block';
        this.classList.add('active');
      } else {
        predictionView.style.display = 'none';
        this.classList.remove('active');
      }
    });

    // Gérer le bouton de prédiction
    document.getElementById('run-prediction-btn').addEventListener('click', function() {
      const text = document.getElementById('prediction-input').value.trim();

      if (text) {
        updatePredictionVisualization(text);
      }
    });

    // Gérer le bouton de visualisation 3D du cerveau
    document.getElementById('show-brain-3d-btn').addEventListener('click', function() {
      const brain3dView = document.getElementById('brain-3d-view');
      const connectionsView = document.getElementById('connections-view');
      const brainActivityView = document.getElementById('brain-activity-view');
      const predictionView = document.getElementById('prediction-view');
      const emotionAnalysisView = document.getElementById('emotion-analysis-view');
      const contentGenerationView = document.getElementById('content-generation-view');
      const dashboardView = document.getElementById('dashboard-view');

      // Masquer les autres vues
      connectionsView.style.display = 'none';
      brainActivityView.style.display = 'none';
      predictionView.style.display = 'none';
      emotionAnalysisView.style.display = 'none';
      contentGenerationView.style.display = 'none';
      dashboardView.style.display = 'none';

      // Réinitialiser les classes des boutons
      document.getElementById('show-connections-btn').classList.remove('active');
      document.getElementById('show-brain-activity-btn').classList.remove('active');
      document.getElementById('predict-btn').classList.remove('active');
      document.getElementById('emotion-analysis-btn').classList.remove('active');
      document.getElementById('generate-content-btn').classList.remove('active');
      document.getElementById('show-dashboard-btn').classList.remove('active');

      if (brain3dView.style.display === 'none') {
        brain3dView.style.display = 'block';
        this.classList.add('active');

        // Mettre à jour la visualisation 3D du cerveau
        updateBrain3DVisualization();
      } else {
        brain3dView.style.display = 'none';
        this.classList.remove('active');
      }
    });

    // Gérer le bouton d'analyse émotionnelle
    document.getElementById('emotion-analysis-btn').addEventListener('click', function() {
      const emotionAnalysisView = document.getElementById('emotion-analysis-view');
      const connectionsView = document.getElementById('connections-view');
      const brainActivityView = document.getElementById('brain-activity-view');
      const predictionView = document.getElementById('prediction-view');
      const brain3dView = document.getElementById('brain-3d-view');
      const contentGenerationView = document.getElementById('content-generation-view');
      const dashboardView = document.getElementById('dashboard-view');

      // Masquer les autres vues
      connectionsView.style.display = 'none';
      brainActivityView.style.display = 'none';
      predictionView.style.display = 'none';
      brain3dView.style.display = 'none';
      contentGenerationView.style.display = 'none';
      dashboardView.style.display = 'none';

      // Réinitialiser les classes des boutons
      document.getElementById('show-connections-btn').classList.remove('active');
      document.getElementById('show-brain-activity-btn').classList.remove('active');
      document.getElementById('predict-btn').classList.remove('active');
      document.getElementById('show-brain-3d-btn').classList.remove('active');
      document.getElementById('generate-content-btn').classList.remove('active');
      document.getElementById('show-dashboard-btn').classList.remove('active');

      if (emotionAnalysisView.style.display === 'none') {
        emotionAnalysisView.style.display = 'block';
        this.classList.add('active');

        // Mettre à jour l'analyse émotionnelle
        updateEmotionalAnalysisVisualization();
      } else {
        emotionAnalysisView.style.display = 'none';
        this.classList.remove('active');
      }
    });

    // Gérer le changement de mode d'analyse émotionnelle
    document.getElementById('emotion-analysis-mode').addEventListener('change', function() {
      updateEmotionalAnalysisVisualization();
    });

    // Gérer le bouton de génération de contenu
    document.getElementById('generate-content-btn').addEventListener('click', function() {
      const contentGenerationView = document.getElementById('content-generation-view');
      const connectionsView = document.getElementById('connections-view');
      const brainActivityView = document.getElementById('brain-activity-view');
      const predictionView = document.getElementById('prediction-view');
      const brain3dView = document.getElementById('brain-3d-view');
      const emotionAnalysisView = document.getElementById('emotion-analysis-view');
      const dashboardView = document.getElementById('dashboard-view');

      // Masquer les autres vues
      connectionsView.style.display = 'none';
      brainActivityView.style.display = 'none';
      predictionView.style.display = 'none';
      brain3dView.style.display = 'none';
      emotionAnalysisView.style.display = 'none';
      dashboardView.style.display = 'none';

      // Réinitialiser les classes des boutons
      document.getElementById('show-connections-btn').classList.remove('active');
      document.getElementById('show-brain-activity-btn').classList.remove('active');
      document.getElementById('predict-btn').classList.remove('active');
      document.getElementById('show-brain-3d-btn').classList.remove('active');
      document.getElementById('emotion-analysis-btn').classList.remove('active');
      document.getElementById('show-dashboard-btn').classList.remove('active');

      if (contentGenerationView.style.display === 'none') {
        contentGenerationView.style.display = 'block';
        this.classList.add('active');
      } else {
        contentGenerationView.style.display = 'none';
        this.classList.remove('active');
      }
    });

    // Gérer le bouton de génération de contenu
    document.getElementById('run-generation-btn').addEventListener('click', function() {
      const topic = document.getElementById('generation-input').value.trim();
      const type = document.getElementById('generation-type').value;

      if (topic) {
        updateContentGenerationVisualization(topic, type);
      }
    });

    // Gérer le bouton du tableau de bord
    document.getElementById('show-dashboard-btn').addEventListener('click', function() {
      const dashboardView = document.getElementById('dashboard-view');
      const connectionsView = document.getElementById('connections-view');
      const brainActivityView = document.getElementById('brain-activity-view');
      const predictionView = document.getElementById('prediction-view');
      const brain3dView = document.getElementById('brain-3d-view');
      const emotionAnalysisView = document.getElementById('emotion-analysis-view');
      const contentGenerationView = document.getElementById('content-generation-view');

      // Masquer les autres vues
      connectionsView.style.display = 'none';
      brainActivityView.style.display = 'none';
      predictionView.style.display = 'none';
      brain3dView.style.display = 'none';
      emotionAnalysisView.style.display = 'none';
      contentGenerationView.style.display = 'none';

      // Réinitialiser les classes des boutons
      document.getElementById('show-connections-btn').classList.remove('active');
      document.getElementById('show-brain-activity-btn').classList.remove('active');
      document.getElementById('predict-btn').classList.remove('active');
      document.getElementById('show-brain-3d-btn').classList.remove('active');
      document.getElementById('emotion-analysis-btn').classList.remove('active');
      document.getElementById('generate-content-btn').classList.remove('active');

      if (dashboardView.style.display === 'none') {
        dashboardView.style.display = 'block';
        this.classList.add('active');

        // Mettre à jour le tableau de bord
        updateDashboardVisualization();
      } else {
        dashboardView.style.display = 'none';
        this.classList.remove('active');
      }
    });

    // Gérer le bouton de rotation du cerveau 3D
    document.getElementById('rotate-brain-btn').addEventListener('click', function() {
      const brain3DContainer = document.getElementById('brain-3d-container');

      if (brain3DContainer.classList.contains('rotating')) {
        brain3DContainer.classList.remove('rotating');
        this.textContent = 'Rotation auto';
      } else {
        brain3DContainer.classList.add('rotating');
        this.textContent = 'Arrêter rotation';
      }
    });

    // Gérer le changement de région du cerveau
    document.getElementById('brain-region-select').addEventListener('change', function() {
      updateBrain3DVisualization();
    });

    // Mettre à jour périodiquement la visualisation de la mémoire thermique
    setInterval(function() {
      // Mettre à jour uniquement si l'onglet de mémoire est actif
      if (document.getElementById('memory-tab').classList.contains('active')) {
        updateMemoryVisualization();

        // Mettre à jour la visualisation des connexions si elle est visible
        if (document.getElementById('connections-view').style.display !== 'none') {
          updateConnectionsVisualization();
        }
      }
    }, 2000); // Mettre à jour toutes les 2 secondes

    // Gérer le basculement du mode simulation
    document.getElementById('simulation-toggle').addEventListener('change', function() {
      simulationMode = this.checked;
      console.log(`Mode simulation ${simulationMode ? 'activé' : 'désactivé'}`);

      // Déconnecter toutes les interfaces
      interfaces.forEach(interface => {
        disconnectInterface(interface.name);
      });

      // Ajouter un message système à chaque interface
      interfaces.forEach(interface => {
        addMessage(interface.name, `Mode simulation ${simulationMode ? 'activé' : 'désactivé'}`, 'system');
      });
    });

    // Initialiser les événements pour chaque interface
    interfaces.forEach(interface => {
      const { name } = interface;

      // Bouton de connexion
      document.getElementById(`${name}-connect-btn`).addEventListener('click', () => {
        connectInterface(name, interface.port);
      });

      // Bouton de déconnexion
      document.getElementById(`${name}-disconnect-btn`).addEventListener('click', () => {
        disconnectInterface(name);
      });

      // Bouton d'envoi
      document.getElementById(`${name}-send-btn`).addEventListener('click', () => {
        sendMessage(name);
      });

      // Touche Entrée dans le champ de saisie
      document.getElementById(`${name}-input`).addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
          sendMessage(name);
        }
      });
    });
  </script>
</body>
</html>
