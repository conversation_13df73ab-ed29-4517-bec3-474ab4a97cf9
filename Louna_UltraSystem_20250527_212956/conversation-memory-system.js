/**
 * Système de Mémoire Conversationnelle pour Louna
 * 
 * Assure la persistance des conversations entre les sessions
 */

const fs = require('fs').promises;
const path = require('path');

class ConversationMemorySystem {
    constructor(options = {}) {
        this.config = {
            memoryFile: options.memoryFile || path.join(__dirname, 'data', 'memory', 'conversations.json'),
            maxConversations: options.maxConversations || 1000,
            maxMessagesPerConversation: options.maxMessagesPerConversation || 100,
            autoSave: options.autoSave !== undefined ? options.autoSave : true,
            debug: options.debug !== undefined ? options.debug : true
        };

        this.conversations = [];
        this.currentSession = null;
        this.initialized = false;
    }

    /**
     * Initialise le système de mémoire
     */
    async initialize() {
        try {
            this.log('🧠 Initialisation du système de mémoire conversationnelle...');

            // Créer le dossier de données s'il n'existe pas
            const memoryDir = path.dirname(this.config.memoryFile);
            await fs.mkdir(memoryDir, { recursive: true });

            // Charger les conversations existantes
            await this.loadConversations();

            // Créer une nouvelle session
            this.startNewSession();

            this.initialized = true;
            this.log('✅ Système de mémoire conversationnelle initialisé');

        } catch (error) {
            this.log('❌ Erreur lors de l\'initialisation:', error);
            throw error;
        }
    }

    /**
     * Charge les conversations depuis le fichier
     */
    async loadConversations() {
        try {
            const data = await fs.readFile(this.config.memoryFile, 'utf8');
            const parsed = JSON.parse(data);
            
            this.conversations = parsed.conversations || [];
            
            this.log(`📚 ${this.conversations.length} conversation(s) chargée(s) depuis la mémoire`);
            
            // Afficher un résumé des conversations récentes
            if (this.conversations.length > 0) {
                const recent = this.conversations.slice(-3);
                this.log('📖 Conversations récentes:');
                recent.forEach((conv, index) => {
                    this.log(`   ${index + 1}. Session ${conv.sessionId} - ${conv.messages.length} messages (${new Date(conv.startTime).toLocaleString()})`);
                });
            }

        } catch (error) {
            if (error.code === 'ENOENT') {
                this.log('📝 Aucune conversation précédente trouvée, création d\'un nouveau fichier');
                this.conversations = [];
            } else {
                this.log('⚠️ Erreur lors du chargement des conversations:', error);
                this.conversations = [];
            }
        }
    }

    /**
     * Sauvegarde les conversations dans le fichier
     */
    async saveConversations() {
        try {
            const data = {
                conversations: this.conversations,
                lastSaved: new Date().toISOString(),
                totalConversations: this.conversations.length,
                totalMessages: this.conversations.reduce((sum, conv) => sum + conv.messages.length, 0)
            };

            await fs.writeFile(this.config.memoryFile, JSON.stringify(data, null, 2), 'utf8');
            this.log(`💾 Conversations sauvegardées: ${data.totalConversations} conversations, ${data.totalMessages} messages`);

        } catch (error) {
            this.log('❌ Erreur lors de la sauvegarde:', error);
        }
    }

    /**
     * Démarre une nouvelle session de conversation
     */
    startNewSession() {
        this.currentSession = {
            sessionId: `session_${Date.now()}`,
            startTime: new Date().toISOString(),
            messages: [],
            metadata: {
                userAgent: 'Louna Desktop App',
                platform: 'macOS',
                version: '2.0.0'
            }
        };

        this.log(`🆕 Nouvelle session démarrée: ${this.currentSession.sessionId}`);
    }

    /**
     * Ajoute un message à la conversation courante
     */
    async addMessage(message, sender = 'user', metadata = {}) {
        if (!this.currentSession) {
            this.startNewSession();
        }

        const messageEntry = {
            id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            sender: sender, // 'user' ou 'agent'
            content: message,
            metadata: {
                length: message.length,
                wordCount: message.split(' ').length,
                ...metadata
            }
        };

        this.currentSession.messages.push(messageEntry);

        this.log(`💬 Message ajouté (${sender}): ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`);

        // Sauvegarder automatiquement si activé
        if (this.config.autoSave) {
            await this.saveCurrentSession();
        }

        return messageEntry;
    }

    /**
     * Sauvegarde la session courante
     */
    async saveCurrentSession() {
        if (!this.currentSession || this.currentSession.messages.length === 0) {
            return;
        }

        // Vérifier si la session existe déjà
        const existingIndex = this.conversations.findIndex(conv => conv.sessionId === this.currentSession.sessionId);

        if (existingIndex >= 0) {
            // Mettre à jour la session existante
            this.conversations[existingIndex] = { ...this.currentSession };
        } else {
            // Ajouter une nouvelle session
            this.conversations.push({ ...this.currentSession });
        }

        // Limiter le nombre de conversations
        if (this.conversations.length > this.config.maxConversations) {
            const removed = this.conversations.splice(0, this.conversations.length - this.config.maxConversations);
            this.log(`🗑️ ${removed.length} anciennes conversations supprimées`);
        }

        await this.saveConversations();
    }

    /**
     * Récupère les messages récents pour le contexte
     */
    getRecentMessages(limit = 20) {
        const allMessages = [];

        // Récupérer les messages de toutes les conversations récentes
        const recentConversations = this.conversations.slice(-5); // 5 dernières conversations
        
        recentConversations.forEach(conversation => {
            conversation.messages.forEach(message => {
                allMessages.push({
                    ...message,
                    sessionId: conversation.sessionId,
                    sessionStart: conversation.startTime
                });
            });
        });

        // Ajouter les messages de la session courante
        if (this.currentSession) {
            this.currentSession.messages.forEach(message => {
                allMessages.push({
                    ...message,
                    sessionId: this.currentSession.sessionId,
                    sessionStart: this.currentSession.startTime
                });
            });
        }

        // Trier par timestamp et prendre les plus récents
        const sortedMessages = allMessages
            .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
            .slice(-limit);

        this.log(`📋 Récupération de ${sortedMessages.length} messages récents pour le contexte`);

        return sortedMessages;
    }

    /**
     * Recherche dans les conversations
     */
    searchConversations(query, limit = 10) {
        const results = [];
        const queryLower = query.toLowerCase();

        this.conversations.forEach(conversation => {
            conversation.messages.forEach(message => {
                if (message.content.toLowerCase().includes(queryLower)) {
                    results.push({
                        ...message,
                        sessionId: conversation.sessionId,
                        sessionStart: conversation.startTime,
                        relevance: this.calculateRelevance(message.content, query)
                    });
                }
            });
        });

        // Trier par pertinence et timestamp
        return results
            .sort((a, b) => b.relevance - a.relevance || new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, limit);
    }

    /**
     * Calcule la pertinence d'un message par rapport à une requête
     */
    calculateRelevance(content, query) {
        const contentLower = content.toLowerCase();
        const queryLower = query.toLowerCase();
        const queryWords = queryLower.split(' ');

        let score = 0;

        // Score pour correspondance exacte
        if (contentLower.includes(queryLower)) {
            score += 10;
        }

        // Score pour mots individuels
        queryWords.forEach(word => {
            if (contentLower.includes(word)) {
                score += 2;
            }
        });

        return score;
    }

    /**
     * Obtient les statistiques de la mémoire
     */
    getMemoryStats() {
        const totalMessages = this.conversations.reduce((sum, conv) => sum + conv.messages.length, 0);
        const currentSessionMessages = this.currentSession ? this.currentSession.messages.length : 0;

        return {
            totalConversations: this.conversations.length,
            totalMessages: totalMessages + currentSessionMessages,
            currentSessionMessages: currentSessionMessages,
            currentSessionId: this.currentSession ? this.currentSession.sessionId : null,
            memoryFile: this.config.memoryFile,
            lastSaved: new Date().toISOString()
        };
    }

    /**
     * Génère un contexte pour l'agent
     */
    generateContextForAgent() {
        const recentMessages = this.getRecentMessages(15);
        
        if (recentMessages.length === 0) {
            return "Aucune conversation précédente trouvée.";
        }

        let context = "=== CONTEXTE DES CONVERSATIONS PRÉCÉDENTES ===\n\n";
        
        const sessions = {};
        recentMessages.forEach(msg => {
            if (!sessions[msg.sessionId]) {
                sessions[msg.sessionId] = [];
            }
            sessions[msg.sessionId].push(msg);
        });

        Object.keys(sessions).forEach(sessionId => {
            const sessionMessages = sessions[sessionId];
            context += `Session ${sessionId} (${new Date(sessionMessages[0].sessionStart).toLocaleString()}):\n`;
            
            sessionMessages.forEach(msg => {
                const time = new Date(msg.timestamp).toLocaleTimeString();
                const sender = msg.sender === 'user' ? 'Utilisateur' : 'Agent';
                context += `[${time}] ${sender}: ${msg.content}\n`;
            });
            
            context += "\n";
        });

        context += "=== FIN DU CONTEXTE ===\n";
        
        return context;
    }

    /**
     * Fonction de logging
     */
    log(message, level = 'info') {
        if (!this.config.debug && level === 'debug') return;
        
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] [ConversationMemory] ${message}`);
    }
}

module.exports = ConversationMemorySystem;
