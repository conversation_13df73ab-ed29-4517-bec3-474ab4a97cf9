<!-- Tableau de bord système -->
<div class="card">
  <div class="card-header">
    <i class="bi bi-speedometer2 card-icon"></i>
    <h2 class="card-title">Tableau de Bord Système</h2>
  </div>
  
  <!-- Statistiques système -->
  <div class="grid-container" style="margin-bottom: 20px;">
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Utilisation RAM (physique/Conteneurs)</div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: 65%;"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Utilisation CPU (Conteneurs)</div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: 45%;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Statistiques détaillées -->
  <div class="grid-container">
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Temps de démarrage</div>
          <div class="stat-value">519ms</div>
          <div class="stat-badge">Initialisation rapide</div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Statut système</div>
          <div class="stat-value" style="font-size: 16px; color: #1dd1a1;">Opérationnel</div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Dernière mise à jour</div>
          <div class="stat-value">08:01:51</div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Types de mémoire surveillés -->
  <h3 style="margin: 20px 0 15px; font-size: 16px;">Types de mémoire surveillés</h3>
  
  <div class="grid-container">
    <div class="grid-item">
      <div class="memory-type">
        <i class="bi bi-cpu" style="color: #54a0ff; margin-right: 10px;"></i>
        <div>
          <div style="font-weight: bold; margin-bottom: 5px;">RAM physique</div>
          <div style="font-size: 14px; color: var(--text-secondary);">Mémoire de l'ordinateur hôte utilisée par l'application</div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="memory-type">
        <i class="bi bi-thermometer-half" style="color: #ff9f43; margin-right: 10px;"></i>
        <div>
          <div style="font-weight: bold; margin-bottom: 5px;">Mémoire thermique</div>
          <div style="font-size: 14px; color: var(--text-secondary);">Modèle de mémoire virtuelle conceptualisé pour l'IA</div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="memory-type">
        <i class="bi bi-hdd" style="color: #1dd1a1; margin-right: 10px;"></i>
        <div>
          <div style="font-weight: bold; margin-bottom: 5px;">Cache système</div>
          <div style="font-size: 14px; color: var(--text-secondary);">Mémoire temporaire utilisée pour les opérations</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Grille de 3 cartes -->
<div class="grid-container">
  <!-- Carte Mémoire Thermique -->
  <div class="grid-item">
    <div class="card">
      <div class="card-header">
        <i class="bi bi-thermometer-half card-icon"></i>
        <h3 class="card-title">Mémoire Thermique</h3>
        <span class="card-badge">OPTIMALISÉE</span>
      </div>
      
      <div class="progress-bar">
        <div class="progress-fill" style="width: 75%;"></div>
      </div>
      
      <div style="margin: 15px 0;">
        <div style="font-size: 14px; margin-bottom: 5px;">Capacité totale:</div>
        <div style="font-weight: bold;">1250 unités réparties sur 6 zones</div>
        <div style="font-size: 14px; color: var(--text-secondary); margin-top: 5px;">Utilisation actuelle:</div>
        <div style="font-size: 14px; color: var(--text-secondary);">42% (525 unités)</div>
        <div style="font-size: 14px; margin-top: 10px;">Zones actives:</div>
        <div style="font-weight: bold;">6 zones configurées</div>
      </div>
    </div>
  </div>
  
  <!-- Carte Zones Thermiques -->
  <div class="grid-item">
    <div class="card">
      <div class="card-header">
        <i class="bi bi-layers card-icon"></i>
        <h3 class="card-title">Zones Thermiques</h3>
        <span class="card-badge">CONNECTÉE</span>
      </div>
      
      <div class="thermal-zones">
        <div class="thermal-zone">
          <span class="zone-name">Zone 1 (Instant)</span>
          <span class="zone-temp hot">48°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill hot" style="width: 90%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 2 (Court terme)</span>
          <span class="zone-temp warm">35°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill warm" style="width: 70%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 3 (Moyen)</span>
          <span class="zone-temp medium">20°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill medium" style="width: 50%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 4 (Moyen terme)</span>
          <span class="zone-temp cool">10°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill cool" style="width: 30%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 5 (Long terme)</span>
          <span class="zone-temp cool">5°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill cool" style="width: 20%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 6 (Rêve)</span>
          <span class="zone-temp cool">3°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill cool" style="width: 10%;"></div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Carte Accélérateurs -->
  <div class="grid-item">
    <div class="card">
      <div class="card-header">
        <i class="bi bi-lightning card-icon"></i>
        <h3 class="card-title">Accélérateurs</h3>
        <span class="card-badge">OPTIMALISÉS</span>
      </div>
      
      <div class="accelerators">
        <div class="accelerator-item">
          <span class="accelerator-name">Accélérateur Réflexif</span>
          <span class="accelerator-value">x3.1</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 80%; background-color: #54a0ff;"></div>
        </div>
        
        <div class="accelerator-item">
          <span class="accelerator-name">Accélérateur Thermique</span>
          <span class="accelerator-value">x2.7</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 70%; background-color: #54a0ff;"></div>
        </div>
        
        <div class="accelerator-item">
          <span class="accelerator-name">Connecteur Thermique</span>
          <span class="accelerator-value">x2.1</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 60%; background-color: #54a0ff;"></div>
        </div>
        
        <div style="margin-top: 20px;">
          <div style="font-size: 14px; margin-bottom: 5px;">Efficacité moyenne:</div>
          <div style="font-weight: bold; font-size: 24px;">92%</div>
          <div style="font-size: 12px; color: var(--text-secondary); margin-top: 5px;">Dernière mise à jour: 08:00 (MESZ)</div>
        </div>
      </div>
    </div>
  </div>
</div>
