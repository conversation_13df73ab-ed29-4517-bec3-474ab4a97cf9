/**
 * Système d'Intelligence Automatique pour Louna
 * Gestion automatique de tous les processus intelligents
 */

const fs = require('fs');
const path = require('path');
const zlib = require('zlib');
const { promisify } = require('util');

class AutoIntelligenceSystem {
    constructor(thermalMemory, kyberAccelerators) {
        this.thermalMemory = thermalMemory;
        this.kyberAccelerators = kyberAccelerators;
        this.isRunning = false;
        this.intervals = {};
        this.performanceHistory = [];
        this.autoAccelerators = new Map();

        // Configuration automatique
        this.config = {
            memoryCheckInterval: 30000, // 30 secondes
            acceleratorCheckInterval: 15000, // 15 secondes
            compressionThreshold: 1024 * 1024, // 1MB
            performanceThreshold: 0.7, // 70%
            autoOptimizationEnabled: true,
            intelligenceTransferEnabled: true
        };

        this.initializeAutoSystems();
    }

    /**
     * Initialise tous les systèmes automatiques
     */
    initializeAutoSystems() {
        console.log('🤖 Initialisation du système d\'intelligence automatique...');

        // Transfert initial d'intelligence
        this.transferCoreIntelligence();

        // Démarrage des processus automatiques
        this.startAutomaticProcesses();

        // Configuration des accélérateurs automatiques
        this.setupAutoAccelerators();

        console.log('✅ Système d\'intelligence automatique initialisé');
    }

    /**
     * Transfert massif d'intelligence de base
     */
    async transferCoreIntelligence() {
        console.log('🧠 Transfert d\'intelligence de base...');

        const coreKnowledge = [
            {
                key: "auto_learning_principles",
                data: "APPRENTISSAGE AUTOMATIQUE: Observation continue des patterns, adaptation des paramètres selon performance, mémorisation des solutions efficaces, évitement des erreurs passées, optimisation prédictive",
                importance: 1.0,
                category: "auto_learning"
            },
            {
                key: "compression_algorithms",
                data: "COMPRESSION INTELLIGENTE: Détection automatique du type de fichier, choix de l'algorithme optimal (gzip, deflate, brotli), compression adaptative selon la taille, décompression sécurisée avec vérification d'intégrité",
                importance: 0.9,
                category: "file_management"
            },
            {
                key: "performance_optimization",
                data: "OPTIMISATION PERFORMANCE: Monitoring continu CPU/RAM/GPU, détection des goulots d'étranglement, allocation dynamique des ressources, cache intelligent, parallélisation automatique",
                importance: 0.95,
                category: "performance"
            },
            {
                key: "error_handling_strategies",
                data: "GESTION ERREURS AVANCÉE: Prédiction des erreurs potentielles, récupération automatique, logging intelligent, escalade progressive, apprentissage des patterns d'erreur",
                importance: 0.9,
                category: "reliability"
            },
            {
                key: "security_protocols",
                data: "SÉCURITÉ AUTOMATIQUE: Détection d'intrusions en temps réel, chiffrement adaptatif, validation automatique des entrées, isolation des processus suspects, mise à jour sécuritaire",
                importance: 1.0,
                category: "security"
            }
        ];

        for (const knowledge of coreKnowledge) {
            try {
                await this.thermalMemory.add(
                    knowledge.key,
                    knowledge.data,
                    knowledge.importance,
                    knowledge.category
                );
                console.log(`✅ Transféré: ${knowledge.key}`);
            } catch (error) {
                console.error(`❌ Erreur transfert ${knowledge.key}:`, error);
            }
        }
    }

    /**
     * Démarre tous les processus automatiques
     */
    startAutomaticProcesses() {
        if (this.isRunning) return;

        console.log('🚀 Démarrage des processus automatiques...');
        this.isRunning = true;

        // Cycle automatique de mémoire
        this.intervals.memoryCheck = setInterval(() => {
            this.automaticMemoryCycle();
        }, this.config.memoryCheckInterval);

        // Vérification automatique des accélérateurs
        this.intervals.acceleratorCheck = setInterval(() => {
            this.checkAndAddAccelerators();
        }, this.config.acceleratorCheckInterval);

        // Optimisation automatique des performances
        this.intervals.performanceCheck = setInterval(() => {
            this.automaticPerformanceOptimization();
        }, 60000); // 1 minute

        // Compression automatique des fichiers
        this.intervals.compressionCheck = setInterval(() => {
            this.automaticFileCompression();
        }, 120000); // 2 minutes

        // Sauvegarde automatique
        this.intervals.autoSave = setInterval(() => {
            this.automaticSave();
        }, 300000); // 5 minutes

        console.log('✅ Tous les processus automatiques démarrés');
    }

    /**
     * Cycle automatique de mémoire basé sur la température
     */
    async automaticMemoryCycle() {
        try {
            const stats = this.thermalMemory.getMemoryStats();
            const temperatures = this.thermalMemory.getSystemTemperatures();

            // Décision automatique basée sur les conditions
            const shouldCycle = this.shouldPerformMemoryCycle(stats, temperatures);

            if (shouldCycle) {
                console.log('🔄 Cycle automatique de mémoire déclenché');
                this.thermalMemory.performMemoryCycle();

                // Apprentissage: mémoriser les conditions du cycle
                await this.thermalMemory.add(
                    `auto_cycle_${Date.now()}`,
                    `Cycle automatique: temp_avg=${stats.averageTemperature}, cpu=${temperatures.cpu}°C, entries=${stats.totalEntries}`,
                    0.6,
                    'auto_learning'
                );
            }
        } catch (error) {
            console.error('❌ Erreur cycle automatique:', error);
        }
    }

    /**
     * Détermine si un cycle de mémoire est nécessaire
     */
    shouldPerformMemoryCycle(stats, temperatures) {
        // Conditions multiples pour décision intelligente
        const highTemperature = temperatures.normalized > 0.7;
        const highMemoryUsage = stats.averageTemperature > 0.8;
        const timeSinceLastCycle = Date.now() - (stats.lastCycle || 0) > 1800000; // 30 min

        return highTemperature || highMemoryUsage || timeSinceLastCycle;
    }

    /**
     * Vérification et ajout automatique d'accélérateurs
     */
    async checkAndAddAccelerators() {
        try {
            const performance = await this.getCurrentPerformance();
            const needed = this.analyzeAcceleratorNeeds(performance);

            for (const acceleratorType of needed) {
                await this.addAutoAccelerator(acceleratorType, performance);
            }
        } catch (error) {
            console.error('❌ Erreur vérification accélérateurs:', error);
        }
    }

    /**
     * Analyse les besoins en accélérateurs
     */
    analyzeAcceleratorNeeds(performance) {
        const needed = [];

        if (performance.memoryEfficiency < 0.5) {
            needed.push('memory_optimizer');
        }

        if (performance.cpuUsage > 0.8) {
            needed.push('cpu_accelerator');
        }

        if (performance.responseTime > 1000) {
            needed.push('response_accelerator');
        }

        if (performance.thermalStability < 0.6) {
            needed.push('thermal_stabilizer');
        }

        return needed;
    }

    /**
     * Ajoute automatiquement un accélérateur
     */
    async addAutoAccelerator(type, performance) {
        if (this.autoAccelerators.has(type)) {
            return; // Déjà ajouté
        }

        console.log(`🚀 Ajout automatique d'accélérateur: ${type}`);

        const accelerator = this.createAccelerator(type, performance);
        this.autoAccelerators.set(type, accelerator);

        // Sauvegarder l'accélérateur
        await this.saveAccelerator(type, accelerator);

        // Mémoriser l'ajout
        await this.thermalMemory.add(
            `auto_accelerator_${type}_${Date.now()}`,
            `Accélérateur automatique ajouté: ${type} - Performance: ${JSON.stringify(performance)}`,
            0.8,
            'auto_optimization'
        );
    }

    /**
     * Crée un accélérateur selon le type
     */
    createAccelerator(type, performance) {
        const accelerators = {
            memory_optimizer: {
                name: 'Optimiseur Mémoire Auto',
                boost: 1.5,
                target: 'memory',
                algorithm: 'adaptive_compression',
                efficiency: 0.85
            },
            cpu_accelerator: {
                name: 'Accélérateur CPU Auto',
                boost: 1.3,
                target: 'cpu',
                algorithm: 'parallel_processing',
                efficiency: 0.9
            },
            response_accelerator: {
                name: 'Accélérateur Réponse Auto',
                boost: 2.0,
                target: 'response',
                algorithm: 'predictive_caching',
                efficiency: 0.8
            },
            thermal_stabilizer: {
                name: 'Stabilisateur Thermique Auto',
                boost: 1.2,
                target: 'thermal',
                algorithm: 'temperature_balancing',
                efficiency: 0.75
            }
        };

        return accelerators[type] || null;
    }

    /**
     * Sauvegarde automatique d'un accélérateur
     */
    async saveAccelerator(type, accelerator) {
        try {
            const acceleratorPath = path.join(__dirname, 'data', 'auto_accelerators');

            if (!fs.existsSync(acceleratorPath)) {
                fs.mkdirSync(acceleratorPath, { recursive: true });
            }

            const filePath = path.join(acceleratorPath, `${type}.json`);
            const data = {
                ...accelerator,
                createdAt: new Date().toISOString(),
                autoGenerated: true
            };

            fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
            console.log(`💾 Accélérateur sauvegardé: ${filePath}`);
        } catch (error) {
            console.error(`❌ Erreur sauvegarde accélérateur ${type}:`, error);
        }
    }

    /**
     * Compression automatique des fichiers
     */
    async automaticFileCompression() {
        try {
            const dataDir = path.join(__dirname, 'data');
            await this.compressDirectoryFiles(dataDir);
        } catch (error) {
            console.error('❌ Erreur compression automatique:', error);
        }
    }

    /**
     * Compresse les fichiers d'un répertoire
     */
    async compressDirectoryFiles(dirPath) {
        if (!fs.existsSync(dirPath)) return;

        const files = fs.readdirSync(dirPath);

        for (const file of files) {
            const filePath = path.join(dirPath, file);
            const stats = fs.statSync(filePath);

            if (stats.isFile() && stats.size > this.config.compressionThreshold) {
                await this.compressFile(filePath);
            } else if (stats.isDirectory()) {
                await this.compressDirectoryFiles(filePath);
            }
        }
    }

    /**
     * Compresse un fichier spécifique
     */
    async compressFile(filePath) {
        try {
            if (filePath.endsWith('.gz')) return; // Déjà compressé

            const data = fs.readFileSync(filePath);
            const compressed = zlib.gzipSync(data);

            // Vérifier si la compression est bénéfique
            if (compressed.length < data.length * 0.9) {
                fs.writeFileSync(filePath + '.gz', compressed);
                fs.unlinkSync(filePath); // Supprimer l'original
                console.log(`🗜️ Fichier compressé: ${filePath}`);

                // Mémoriser la compression
                await this.thermalMemory.add(
                    `auto_compression_${Date.now()}`,
                    `Compression automatique: ${filePath} - Ratio: ${(compressed.length/data.length*100).toFixed(1)}%`,
                    0.5,
                    'file_management'
                );
            }
        } catch (error) {
            console.error(`❌ Erreur compression ${filePath}:`, error);
        }
    }

    /**
     * Décompression automatique sécurisée
     */
    async decompressFile(filePath) {
        try {
            if (!filePath.endsWith('.gz')) return null;

            const compressed = fs.readFileSync(filePath);
            const decompressed = zlib.gunzipSync(compressed);

            const originalPath = filePath.slice(0, -3); // Enlever .gz
            fs.writeFileSync(originalPath, decompressed);

            console.log(`📂 Fichier décompressé: ${originalPath}`);
            return originalPath;
        } catch (error) {
            console.error(`❌ Erreur décompression ${filePath}:`, error);
            return null;
        }
    }

    /**
     * Optimisation automatique des performances
     */
    async automaticPerformanceOptimization() {
        try {
            const performance = await this.getCurrentPerformance();
            this.performanceHistory.push({
                timestamp: Date.now(),
                ...performance
            });

            // Garder seulement les 100 dernières mesures
            if (this.performanceHistory.length > 100) {
                this.performanceHistory.shift();
            }

            // Optimiser si nécessaire
            if (performance.overall < this.config.performanceThreshold) {
                await this.optimizePerformance(performance);
            }
        } catch (error) {
            console.error('❌ Erreur optimisation automatique:', error);
        }
    }

    /**
     * Obtient les performances actuelles
     */
    async getCurrentPerformance() {
        const memoryStats = this.thermalMemory.getMemoryStats();
        const temperatures = this.thermalMemory.getSystemTemperatures();
        const learningMetrics = this.thermalMemory.getLearningPerformanceMetrics();

        return {
            memoryEfficiency: learningMetrics.memoryEfficiency || 0,
            thermalStability: learningMetrics.temperatureStability || 0,
            cpuUsage: temperatures.cpu / 100,
            responseTime: Math.random() * 500 + 200, // Simulé pour l'instant
            overall: learningMetrics.overallPerformance || 0
        };
    }

    /**
     * Optimise les performances
     */
    async optimizePerformance(performance) {
        console.log('⚡ Optimisation automatique des performances...');

        // Forcer l'optimisation de la mémoire thermique
        this.thermalMemory.forceOptimization();

        // Nettoyer les accélérateurs inefficaces
        await this.cleanupIneffectiveAccelerators();

        // Mémoriser l'optimisation
        await this.thermalMemory.add(
            `auto_optimization_${Date.now()}`,
            `Optimisation automatique: Performance globale ${(performance.overall*100).toFixed(1)}%`,
            0.7,
            'auto_optimization'
        );
    }

    /**
     * Nettoie les accélérateurs inefficaces
     */
    async cleanupIneffectiveAccelerators() {
        for (const [type, accelerator] of this.autoAccelerators) {
            if (accelerator.efficiency < 0.5) {
                console.log(`🗑️ Suppression accélérateur inefficace: ${type}`);
                this.autoAccelerators.delete(type);

                // Supprimer le fichier
                const filePath = path.join(__dirname, 'data', 'auto_accelerators', `${type}.json`);
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                }
            }
        }
    }

    /**
     * Sauvegarde automatique de l'état
     */
    async automaticSave() {
        try {
            console.log('💾 Sauvegarde automatique...');

            const state = {
                timestamp: new Date().toISOString(),
                performanceHistory: this.performanceHistory.slice(-10),
                autoAccelerators: Array.from(this.autoAccelerators.entries()),
                config: this.config
            };

            const savePath = path.join(__dirname, 'data', 'auto_intelligence_state.json');
            fs.writeFileSync(savePath, JSON.stringify(state, null, 2));

            console.log('✅ Sauvegarde automatique terminée');
        } catch (error) {
            console.error('❌ Erreur sauvegarde automatique:', error);
        }
    }

    /**
     * Arrête tous les processus automatiques
     */
    stop() {
        console.log('🛑 Arrêt du système d\'intelligence automatique...');
        this.isRunning = false;

        for (const [name, interval] of Object.entries(this.intervals)) {
            clearInterval(interval);
            console.log(`✅ Processus ${name} arrêté`);
        }

        this.intervals = {};
    }

    /**
     * Configuration initiale des accélérateurs automatiques
     */
    setupAutoAccelerators() {
        console.log('⚙️ Configuration des accélérateurs automatiques...');

        // Charger les accélérateurs sauvegardés
        this.loadSavedAccelerators();

        console.log(`✅ ${this.autoAccelerators.size} accélérateurs automatiques configurés`);
    }

    /**
     * Charge les accélérateurs sauvegardés
     */
    loadSavedAccelerators() {
        try {
            const acceleratorPath = path.join(__dirname, 'data', 'auto_accelerators');

            if (!fs.existsSync(acceleratorPath)) {
                return;
            }

            const files = fs.readdirSync(acceleratorPath);

            for (const file of files) {
                if (file.endsWith('.json')) {
                    const filePath = path.join(acceleratorPath, file);
                    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                    const type = file.replace('.json', '');

                    this.autoAccelerators.set(type, data);
                    console.log(`📂 Accélérateur chargé: ${type}`);
                }
            }
        } catch (error) {
            console.error('❌ Erreur chargement accélérateurs:', error);
        }
    }

    /**
     * Obtient le statut du système
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            activeProcesses: Object.keys(this.intervals).length,
            autoAccelerators: this.autoAccelerators.size,
            performanceHistory: this.performanceHistory.length,
            config: this.config
        };
    }

    /**
     * MÉTHODES MANQUANTES CRITIQUES - AJOUTÉES POUR COMPATIBILITÉ COMPLÈTE
     */

    /**
     * Force l'optimisation immédiate du système
     */
    async forceOptimization() {
        console.log('🚀 Optimisation forcée du système...');

        try {
            // Forcer un cycle de mémoire
            this.thermalMemory.performMemoryCycle();

            // Optimiser les performances
            const performance = await this.getCurrentPerformance();
            await this.optimizePerformance(performance);

            // Nettoyer les fichiers temporaires
            await this.cleanupTempFiles();

            // Réorganiser les accélérateurs
            await this.reorganizeAccelerators();

            console.log('✅ Optimisation forcée terminée');
            return true;
        } catch (error) {
            console.error('❌ Erreur optimisation forcée:', error);
            return false;
        }
    }

    /**
     * Nettoie les fichiers temporaires
     */
    async cleanupTempFiles() {
        try {
            const tempDir = path.join(__dirname, 'temp');
            if (fs.existsSync(tempDir)) {
                const files = fs.readdirSync(tempDir);
                for (const file of files) {
                    const filePath = path.join(tempDir, file);
                    const stats = fs.statSync(filePath);

                    // Supprimer les fichiers de plus de 1 heure
                    if (Date.now() - stats.mtime.getTime() > 3600000) {
                        fs.unlinkSync(filePath);
                        console.log(`🗑️ Fichier temporaire supprimé: ${file}`);
                    }
                }
            }
        } catch (error) {
            console.error('❌ Erreur nettoyage fichiers temporaires:', error);
        }
    }

    /**
     * Réorganise les accélérateurs pour optimiser les performances
     */
    async reorganizeAccelerators() {
        console.log('🔄 Réorganisation des accélérateurs...');

        const acceleratorArray = Array.from(this.autoAccelerators.entries());

        // Trier par efficacité
        acceleratorArray.sort((a, b) => b[1].efficiency - a[1].efficiency);

        // Recréer la Map avec l'ordre optimisé
        this.autoAccelerators.clear();
        for (const [type, accelerator] of acceleratorArray) {
            this.autoAccelerators.set(type, accelerator);
        }

        console.log('✅ Accélérateurs réorganisés par efficacité');
    }

    /**
     * Analyse intelligente des patterns de performance
     */
    analyzePerformancePatterns() {
        if (this.performanceHistory.length < 10) {
            return { patterns: [], recommendations: [] };
        }

        const patterns = [];
        const recommendations = [];

        // Analyser les tendances
        const recent = this.performanceHistory.slice(-10);
        const older = this.performanceHistory.slice(-20, -10);

        const recentAvg = recent.reduce((sum, p) => sum + p.overall, 0) / recent.length;
        const olderAvg = older.length > 0 ? older.reduce((sum, p) => sum + p.overall, 0) / older.length : recentAvg;

        if (recentAvg < olderAvg * 0.9) {
            patterns.push('Performance en déclin');
            recommendations.push('Augmenter la fréquence d\'optimisation');
        } else if (recentAvg > olderAvg * 1.1) {
            patterns.push('Performance en amélioration');
            recommendations.push('Maintenir les optimisations actuelles');
        }

        // Analyser la stabilité
        const variance = recent.reduce((sum, p) => sum + Math.pow(p.overall - recentAvg, 2), 0) / recent.length;
        if (variance > 0.1) {
            patterns.push('Performance instable');
            recommendations.push('Ajouter des stabilisateurs thermiques');
        }

        return { patterns, recommendations };
    }

    /**
     * Génère un rapport de performance détaillé
     */
    generatePerformanceReport() {
        const currentPerformance = this.performanceHistory[this.performanceHistory.length - 1];
        const patterns = this.analyzePerformancePatterns();

        return {
            timestamp: new Date().toISOString(),
            currentPerformance,
            patterns: patterns.patterns,
            recommendations: patterns.recommendations,
            accelerators: {
                total: this.autoAccelerators.size,
                active: Array.from(this.autoAccelerators.values()).filter(a => a.efficiency > 0.5).length,
                efficiency: Array.from(this.autoAccelerators.values()).reduce((sum, a) => sum + a.efficiency, 0) / this.autoAccelerators.size
            },
            memory: {
                totalEntries: this.thermalMemory.getMemoryStats().totalEntries,
                averageTemperature: this.thermalMemory.getMemoryStats().averageTemperature,
                cyclesPerformed: this.thermalMemory.getMemoryStats().cyclesPerformed
            }
        };
    }

    /**
     * Applique des optimisations intelligentes basées sur l'apprentissage
     */
    async applyIntelligentOptimizations() {
        console.log('🧠 Application d\'optimisations intelligentes...');

        try {
            const report = this.generatePerformanceReport();

            // Optimisations basées sur les patterns détectés
            for (const pattern of report.patterns) {
                switch (pattern) {
                    case 'Performance en déclin':
                        await this.increaseOptimizationFrequency();
                        break;
                    case 'Performance instable':
                        await this.addThermalStabilizers();
                        break;
                    case 'Performance en amélioration':
                        await this.maintainCurrentSettings();
                        break;
                }
            }

            // Appliquer les recommandations
            for (const recommendation of report.recommendations) {
                await this.applyRecommendation(recommendation);
            }

            console.log('✅ Optimisations intelligentes appliquées');
            return report;
        } catch (error) {
            console.error('❌ Erreur optimisations intelligentes:', error);
            return null;
        }
    }

    /**
     * Augmente la fréquence d'optimisation
     */
    async increaseOptimizationFrequency() {
        this.config.memoryCheckInterval = Math.max(15000, this.config.memoryCheckInterval * 0.8);
        this.config.acceleratorCheckInterval = Math.max(10000, this.config.acceleratorCheckInterval * 0.8);

        // Redémarrer les intervalles avec la nouvelle fréquence
        this.restartIntervals();

        console.log('⚡ Fréquence d\'optimisation augmentée');
    }

    /**
     * Ajoute des stabilisateurs thermiques
     */
    async addThermalStabilizers() {
        if (!this.autoAccelerators.has('thermal_stabilizer')) {
            const performance = await this.getCurrentPerformance();
            await this.addAutoAccelerator('thermal_stabilizer', performance);
        }
    }

    /**
     * Maintient les paramètres actuels
     */
    async maintainCurrentSettings() {
        console.log('✅ Maintien des paramètres actuels - Performance stable');
    }

    /**
     * Applique une recommandation spécifique
     */
    async applyRecommendation(recommendation) {
        console.log(`📋 Application de la recommandation: ${recommendation}`);

        switch (recommendation) {
            case 'Augmenter la fréquence d\'optimisation':
                await this.increaseOptimizationFrequency();
                break;
            case 'Ajouter des stabilisateurs thermiques':
                await this.addThermalStabilizers();
                break;
            case 'Maintenir les optimisations actuelles':
                await this.maintainCurrentSettings();
                break;
            default:
                console.log(`⚠️ Recommandation non reconnue: ${recommendation}`);
        }
    }

    /**
     * Redémarre les intervalles avec les nouveaux paramètres
     */
    restartIntervals() {
        // Arrêter les intervalles actuels
        for (const [name, interval] of Object.entries(this.intervals)) {
            clearInterval(interval);
        }

        // Redémarrer avec les nouveaux paramètres
        this.intervals = {};
        this.startAutomaticProcesses();
    }

    /**
     * Exporte l'état complet du système
     */
    exportSystemState() {
        return {
            config: this.config,
            isRunning: this.isRunning,
            performanceHistory: this.performanceHistory,
            autoAccelerators: Array.from(this.autoAccelerators.entries()),
            intervals: Object.keys(this.intervals),
            exportDate: new Date().toISOString(),
            version: '2.0.0'
        };
    }

    /**
     * Importe un état de système
     */
    importSystemState(state) {
        try {
            if (state.config) {
                Object.assign(this.config, state.config);
            }

            if (state.performanceHistory) {
                this.performanceHistory = state.performanceHistory;
            }

            if (state.autoAccelerators) {
                this.autoAccelerators.clear();
                for (const [type, accelerator] of state.autoAccelerators) {
                    this.autoAccelerators.set(type, accelerator);
                }
            }

            console.log('✅ État du système importé avec succès');
            return true;
        } catch (error) {
            console.error('❌ Erreur importation état système:', error);
            return false;
        }
    }

    /**
     * Réinitialise le système à l'état par défaut
     */
    resetToDefaults() {
        console.log('🔄 Réinitialisation du système...');

        // Arrêter tous les processus
        this.stop();

        // Réinitialiser la configuration
        this.config = {
            memoryCheckInterval: 30000,
            acceleratorCheckInterval: 15000,
            compressionThreshold: 1024 * 1024,
            performanceThreshold: 0.7,
            autoOptimizationEnabled: true,
            intelligenceTransferEnabled: true
        };

        // Vider l'historique et les accélérateurs
        this.performanceHistory = [];
        this.autoAccelerators.clear();

        // Redémarrer
        this.initializeAutoSystems();

        console.log('✅ Système réinitialisé');
    }

    /**
     * Obtient le statut du système
     */
    getSystemStatus() {
        return {
            isRunning: this.isRunning,
            config: this.config,
            stats: {
                performanceHistory: this.performanceHistory.length,
                autoAccelerators: this.autoAccelerators.size,
                processesRunning: Object.keys(this.intervals).length
            },
            lastOptimization: this.performanceHistory.length > 0 ?
                this.performanceHistory[this.performanceHistory.length - 1].timestamp : null,
            timestamp: Date.now()
        };
    }

    /**
     * Obtient les métriques de performance
     */
    getPerformanceMetrics() {
        if (this.performanceHistory.length === 0) {
            return {
                current: null,
                average: null,
                trend: 'stable',
                history: []
            };
        }

        const recent = this.performanceHistory.slice(-10);
        const current = recent[recent.length - 1];

        const average = {
            memoryEfficiency: recent.reduce((sum, p) => sum + p.memoryEfficiency, 0) / recent.length,
            thermalStability: recent.reduce((sum, p) => sum + p.thermalStability, 0) / recent.length,
            cpuUsage: recent.reduce((sum, p) => sum + p.cpuUsage, 0) / recent.length,
            responseTime: recent.reduce((sum, p) => sum + p.responseTime, 0) / recent.length,
            overall: recent.reduce((sum, p) => sum + p.overall, 0) / recent.length
        };

        // Calculer la tendance
        let trend = 'stable';
        if (recent.length >= 3) {
            const firstHalf = recent.slice(0, Math.floor(recent.length / 2));
            const secondHalf = recent.slice(Math.floor(recent.length / 2));

            const firstAvg = firstHalf.reduce((sum, p) => sum + p.overall, 0) / firstHalf.length;
            const secondAvg = secondHalf.reduce((sum, p) => sum + p.overall, 0) / secondHalf.length;

            if (secondAvg > firstAvg + 0.05) trend = 'improving';
            else if (secondAvg < firstAvg - 0.05) trend = 'declining';
        }

        return {
            current,
            average,
            trend,
            history: this.performanceHistory.slice(-50)
        };
    }

    /**
     * Force une optimisation complète
     */
    async forceOptimization() {
        try {
            console.log('🚀 Optimisation forcée du système d\'intelligence...');

            // Effectuer tous les cycles d'optimisation
            await this.automaticMemoryCycle();
            await this.checkAndAddAccelerators();
            await this.automaticPerformanceOptimization();
            await this.automaticFileCompression();
            await this.automaticSave();

            console.log('✅ Optimisation forcée terminée');
            return {
                success: true,
                timestamp: Date.now(),
                optimizations: [
                    'memory_cycle',
                    'accelerator_check',
                    'performance_optimization',
                    'file_compression',
                    'auto_save'
                ]
            };
        } catch (error) {
            console.error('❌ Erreur lors de l\'optimisation forcée:', error);
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    /**
     * Obtient les accélérateurs automatiques
     */
    getAutoAccelerators() {
        const accelerators = [];

        for (const [type, accelerator] of this.autoAccelerators) {
            accelerators.push({
                type,
                ...accelerator,
                isActive: true,
                createdAt: accelerator.createdAt || new Date().toISOString()
            });
        }

        return {
            total: accelerators.length,
            accelerators,
            types: Array.from(this.autoAccelerators.keys()),
            timestamp: Date.now()
        };
    }

    /**
     * Redémarre le système
     */
    async restart() {
        console.log('🔄 Redémarrage du système d\'intelligence automatique...');

        this.stop();
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.startAutomaticProcesses();

        console.log('✅ Système d\'intelligence automatique redémarré');
        return true;
    }
}

module.exports = AutoIntelligenceSystem;
