/**
 * Script de correction de navigation pour Louna
 * Ce script corrige les problèmes de navigation entre les interfaces
 */

// Fonction pour corriger les liens incorrects
function fixIncorrectLinks() {
  // Sélectionner tous les liens dans la barre latérale
  const sidebarLinks = document.querySelectorAll('.sidebar-nav a');

  // Parcourir tous les liens et corriger les chemins incorrects
  sidebarLinks.forEach(link => {
    const href = link.getAttribute('href');

    // Corriger les liens qui commencent par /louna/ au lieu de /luna/
    if (href && href.startsWith('/louna/')) {
      const correctedHref = href.replace('/louna/', '/luna/');
      link.setAttribute('href', correctedHref);
      console.log(`Lien corrigé: ${href} -> ${correctedHref}`);
    }
  });

  console.log('Correction des liens terminée');
}

// Fonction pour ajouter un gestionnaire d'erreurs de navigation
function addNavigationErrorHandler() {
  // Stocker l'URL actuelle dans le stockage local
  if (!window.location.pathname.includes('/error')) {
    localStorage.setItem('lastValidUrl', window.location.href);
    console.log('URL valide stockée:', window.location.href);
  }

  // Ajouter un gestionnaire d'événements pour les erreurs
  window.addEventListener('error', function(event) {
    console.error('Erreur détectée:', event.error);

    // Vérifier si l'erreur est liée à la navigation
    if (event.error && (
        event.error.message.includes('navigation') ||
        event.error.message.includes('route') ||
        event.error.message.includes('undefined') ||
        event.error.message.includes('Cannot read') ||
        event.error.message.includes('null') ||
        event.error.message.includes('not defined')
      )) {
      console.log('Erreur de navigation détectée, tentative de récupération...');

      // Récupérer la dernière URL valide
      const lastValidUrl = localStorage.getItem('lastValidUrl') || '/luna';

      // Rediriger vers la dernière URL valide
      window.location.href = lastValidUrl;
    }
  });

  console.log('Gestionnaire d\'erreurs de navigation ajouté');
}

// Fonction pour intercepter les clics sur les liens
function interceptLinkClicks() {
  // Intercepter tous les clics sur les liens
  document.addEventListener('click', function(event) {
    // Vérifier si l'élément cliqué est un lien ou est contenu dans un lien
    const link = event.target.closest('a');

    if (link) {
      const href = link.getAttribute('href');

      // Ne pas intercepter les liens externes ou les liens avec des attributs spéciaux
      if (!href || href.startsWith('http') || href.startsWith('#') ||
          link.getAttribute('target') === '_blank' ||
          link.getAttribute('data-no-intercept')) {
        return;
      }

      // Corriger les liens qui commencent par /louna/ au lieu de /luna/
      if (href.startsWith('/louna/')) {
        event.preventDefault();
        const correctedHref = href.replace('/louna/', '/luna/');
        console.log(`Navigation corrigée: ${href} -> ${correctedHref}`);
        window.location.href = correctedHref;
      }

      // Stocker l'URL actuelle dans le stockage local
      localStorage.setItem('lastValidUrl', window.location.href);
      console.log('URL valide stockée (clic):', window.location.href);
    }
  });

  console.log('Interception des clics sur les liens activée');
}

// Exécuter les fonctions lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
  console.log('Script de correction de navigation chargé');

  // Corriger les liens incorrects
  fixIncorrectLinks();

  // Ajouter un gestionnaire d'erreurs de navigation
  addNavigationErrorHandler();

  // Intercepter les clics sur les liens
  interceptLinkClicks();

  console.log('Correction de navigation terminée');
});
