/**
 * Système de calendrier 2025 pour Vision Ultra
 * Assure que l'agent sait qu'on est en 2025 et peut faire des recherches actuelles
 */

class Calendar2025System {
    constructor() {
        this.currentYear = 2025;
        this.currentDate = new Date();
        this.timeZone = 'Europe/Paris';
        this.initialized = false;
        
        console.log('📅 Initialisation du système de calendrier 2025...');
        this.init();
    }

    init() {
        // Forcer la date actuelle à 2025
        this.updateCurrentDate();
        
        // Injecter la connaissance de 2025 dans l'agent
        this.injectDateKnowledge();
        
        // Démarrer la mise à jour continue
        this.startDateUpdates();
        
        this.initialized = true;
        console.log('✅ Système de calendrier 2025 initialisé');
        console.log(`📅 Date actuelle: ${this.getFormattedDate()}`);
        console.log(`🌍 Fuseau horaire: ${this.timeZone}`);
    }

    updateCurrentDate() {
        this.currentDate = new Date();
        
        // S'assurer que l'année est 2025
        if (this.currentDate.getFullYear() !== 2025) {
            // Ajuster pour 2025 si nécessaire
            this.currentDate.setFullYear(2025);
        }
    }

    getFormattedDate() {
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: this.timeZone
        };
        
        return this.currentDate.toLocaleDateString('fr-FR', options);
    }

    getDateForAgent() {
        return {
            year: 2025,
            month: this.currentDate.getMonth() + 1,
            day: this.currentDate.getDate(),
            hour: this.currentDate.getHours(),
            minute: this.currentDate.getMinutes(),
            weekday: this.currentDate.toLocaleDateString('fr-FR', { weekday: 'long' }),
            formatted: this.getFormattedDate(),
            timestamp: Date.now(),
            timezone: this.timeZone
        };
    }

    injectDateKnowledge() {
        // Injecter dans la mémoire thermique si disponible
        if (global.thermalMemory) {
            const dateInfo = {
                type: 'temporal_awareness',
                data: `ANNÉE ACTUELLE: 2025. Date: ${this.getFormattedDate()}. 
                       L'agent doit TOUJOURS utiliser 2025 comme année de référence pour les recherches.
                       Toutes les recherches Internet doivent inclure "2025" pour obtenir des informations actuelles.`,
                importance: 1.0,
                temperature: 0.9,
                timestamp: Date.now()
            };
            
            try {
                global.thermalMemory.addInformation(dateInfo);
                console.log('📅 Connaissance de 2025 injectée dans la mémoire thermique');
            } catch (error) {
                console.log('⚠️ Impossible d\'injecter dans la mémoire thermique:', error.message);
            }
        }

        // Définir globalement pour tous les systèmes
        global.CURRENT_YEAR = 2025;
        global.CURRENT_DATE = this.getDateForAgent();
        global.SEARCH_YEAR_CONTEXT = '2025';
    }

    startDateUpdates() {
        // Mettre à jour toutes les minutes
        setInterval(() => {
            this.updateCurrentDate();
            this.injectDateKnowledge();
        }, 60000);
    }

    // Méthode pour forcer les recherches avec 2025
    enhanceSearchQuery(query) {
        if (!query.includes('2025') && !query.includes('2024')) {
            return `${query} 2025`;
        }
        return query.replace(/2024/g, '2025');
    }

    // Méthode pour l'agent
    getSearchContext() {
        return {
            year: 2025,
            instruction: 'IMPORTANT: Nous sommes en 2025. Toujours inclure "2025" dans les recherches pour obtenir des informations actuelles.',
            dateContext: this.getFormattedDate(),
            searchSuffix: ' 2025'
        };
    }

    getStatus() {
        return {
            initialized: this.initialized,
            currentYear: this.currentYear,
            currentDate: this.getFormattedDate(),
            globalVariables: {
                CURRENT_YEAR: global.CURRENT_YEAR,
                SEARCH_YEAR_CONTEXT: global.SEARCH_YEAR_CONTEXT
            }
        };
    }
}

// Initialiser le système
const calendar2025 = new Calendar2025System();

// Exporter pour utilisation globale
global.calendar2025 = calendar2025;

module.exports = Calendar2025System;
