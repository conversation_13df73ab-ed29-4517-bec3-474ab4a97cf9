/**
 * Script pour convertir l'icône SVG en formats PNG, ICO et ICNS
 * Ce script utilise sharp pour convertir l'icône SVG en différents formats
 * pour les différentes plateformes (Windows, macOS, Linux)
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const { execSync } = require('child_process');

// Chemin vers le dossier d'icônes
const iconDir = path.join(__dirname, 'public', 'img');

// Chemin vers l'icône SVG
const svgPath = path.join(iconDir, 'louna-icon.svg');

// Vérifier si l'icône SVG existe
if (!fs.existsSync(svgPath)) {
  console.error(`L'icône SVG n'existe pas : ${svgPath}`);
  process.exit(1);
}

// Tailles d'icônes pour les différentes plateformes
const sizes = [16, 32, 48, 64, 128, 256, 512, 1024];

// Fonction pour convertir l'icône SVG en PNG
async function convertToPng() {
  console.log('Conversion de l\'icône SVG en PNG...');
  
  try {
    // Créer un dossier temporaire pour les icônes PNG
    const tempDir = path.join(iconDir, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir);
    }
    
    // Convertir l'icône SVG en PNG pour chaque taille
    for (const size of sizes) {
      const pngPath = path.join(tempDir, `icon-${size}.png`);
      
      await sharp(svgPath)
        .resize(size, size)
        .png()
        .toFile(pngPath);
      
      console.log(`Icône PNG créée : ${pngPath}`);
    }
    
    // Créer une icône PNG principale
    await sharp(svgPath)
      .resize(512, 512)
      .png()
      .toFile(path.join(iconDir, 'louna-icon.png'));
    
    console.log('Conversion en PNG terminée');
    
    return tempDir;
  } catch (error) {
    console.error('Erreur lors de la conversion en PNG :', error);
    throw error;
  }
}

// Fonction pour convertir les PNG en ICO (Windows)
async function convertToIco(tempDir) {
  console.log('Conversion en ICO pour Windows...');
  
  try {
    // Vérifier si le module png-to-ico est installé
    try {
      require.resolve('png-to-ico');
    } catch (error) {
      console.log('Installation du module png-to-ico...');
      execSync('npm install --no-save png-to-ico');
    }
    
    const pngToIco = require('png-to-ico');
    
    // Chemins vers les icônes PNG
    const pngPaths = sizes.map(size => path.join(tempDir, `icon-${size}.png`));
    
    // Convertir les PNG en ICO
    const icoBuffer = await pngToIco(pngPaths);
    
    // Écrire le fichier ICO
    const icoPath = path.join(iconDir, 'louna-icon.ico');
    fs.writeFileSync(icoPath, icoBuffer);
    
    console.log(`Icône ICO créée : ${icoPath}`);
  } catch (error) {
    console.error('Erreur lors de la conversion en ICO :', error);
    console.log('Vous pouvez convertir manuellement les PNG en ICO en utilisant un outil en ligne.');
  }
}

// Fonction pour convertir les PNG en ICNS (macOS)
async function convertToIcns(tempDir) {
  console.log('Conversion en ICNS pour macOS...');
  
  try {
    // Vérifier si le module png-to-icns est installé
    try {
      require.resolve('png-to-icns');
    } catch (error) {
      console.log('Installation du module png-to-icns...');
      execSync('npm install --no-save png-to-icns');
    }
    
    const pngToIcns = require('png-to-icns');
    
    // Chemin vers l'icône PNG 1024x1024
    const pngPath = path.join(tempDir, 'icon-1024.png');
    
    // Chemin vers le fichier ICNS
    const icnsPath = path.join(iconDir, 'louna-icon.icns');
    
    // Convertir le PNG en ICNS
    await pngToIcns({
      input: pngPath,
      output: icnsPath
    });
    
    console.log(`Icône ICNS créée : ${icnsPath}`);
  } catch (error) {
    console.error('Erreur lors de la conversion en ICNS :', error);
    console.log('Vous pouvez convertir manuellement les PNG en ICNS en utilisant un outil en ligne.');
  }
}

// Fonction principale
async function main() {
  try {
    // Convertir l'icône SVG en PNG
    const tempDir = await convertToPng();
    
    // Convertir les PNG en ICO (Windows)
    await convertToIco(tempDir);
    
    // Convertir les PNG en ICNS (macOS)
    await convertToIcns(tempDir);
    
    // Supprimer le dossier temporaire
    fs.rmSync(tempDir, { recursive: true, force: true });
    
    console.log('Conversion des icônes terminée');
  } catch (error) {
    console.error('Erreur lors de la conversion des icônes :', error);
  }
}

// Exécuter la fonction principale
main();
