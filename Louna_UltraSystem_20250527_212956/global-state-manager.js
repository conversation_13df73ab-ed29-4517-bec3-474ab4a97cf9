#!/usr/bin/env node

/**
 * GESTIONNAIRE D'ÉTAT GLOBAL POUR LOUNA
 * Synchronise les données entre toutes les interfaces
 * Évite la perte de mémoire et assure la cohérence
 */

const fs = require('fs');
const path = require('path');

class GlobalStateManager {
    constructor() {
        this.state = {
            // Données de l'agent
            agent: {
                qi: 150,
                neurones: 71,
                temperature: 42.3,
                accelerateurs: 8,
                evolution_level: 85,
                last_update: new Date().toISOString()
            },
            
            // Mémoire thermique
            thermal_memory: {
                global_temp: 0.38,
                zones: [
                    { name: "Sensoriel<PERSON>", temperature: 0.3, count: 15, active: true },
                    { name: "Travail", temperature: 0.5, count: 8, active: true },
                    { name: "Long Terme", temperature: 0.2, count: 25, active: true },
                    { name: "Émotionnelle", temperature: 0.4, count: 12, active: true },
                    { name: "<PERSON>cédural<PERSON>", temperature: 0.3, count: 18, active: true },
                    { name: "Créative", temperature: 0.6, count: 7, active: true }
                ],
                total_memories: 85,
                hot_memories: 12,
                cycles_count: 1247,
                efficiency: 85,
                last_backup: new Date().toISOString()
            },
            
            // Système de performance
            performance: {
                cpu_usage: 45,
                memory_usage: 67,
                disk_usage: 23,
                network_status: 'connected',
                uptime: 0,
                last_restart: new Date().toISOString()
            },
            
            // Conversations et interactions
            conversations: {
                total_messages: 0,
                active_sessions: 1,
                last_interaction: new Date().toISOString(),
                user_preferences: {
                    name: "Jean-Luc Passave",
                    location: "Sainte-Anne, Guadeloupe",
                    role: "Créateur de Louna",
                    preferences: ["mémoire thermique", "réflexions visibles", "interfaces cohérentes"]
                }
            },
            
            // Métadonnées système
            system: {
                version: "1.0.0",
                build: Date.now(),
                environment: "production",
                features_enabled: ["thermal_memory", "reflections", "internet_search", "kyber_accelerators"],
                last_sync: new Date().toISOString()
            }
        };
        
        this.stateFile = path.join(__dirname, 'data', 'global_state.json');
        this.backupDir = path.join(__dirname, 'data', 'state_backups');
        
        // Créer les répertoires si nécessaire
        this.ensureDirectories();
        
        // Charger l'état existant
        this.loadState();
        
        // Démarrer la synchronisation automatique
        this.startAutoSync();
        
        console.log('🌐 Gestionnaire d\'état global initialisé');
    }
    
    /**
     * Assurer que les répertoires existent
     */
    ensureDirectories() {
        const dirs = [
            path.join(__dirname, 'data'),
            this.backupDir
        ];
        
        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`📁 Répertoire créé: ${dir}`);
            }
        });
    }
    
    /**
     * Charger l'état depuis le fichier
     */
    loadState() {
        try {
            if (fs.existsSync(this.stateFile)) {
                const savedState = JSON.parse(fs.readFileSync(this.stateFile, 'utf8'));
                
                // Fusionner avec l'état par défaut pour éviter les propriétés manquantes
                this.state = this.mergeDeep(this.state, savedState);
                
                console.log('📖 État global chargé depuis le fichier');
                console.log(`   - QI: ${this.state.agent.qi}`);
                console.log(`   - Neurones: ${this.state.agent.neurones}`);
                console.log(`   - Mémoires: ${this.state.thermal_memory.total_memories}`);
            } else {
                console.log('🆕 Nouvel état global créé');
                this.saveState();
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement de l\'état:', error);
            console.log('🔄 Utilisation de l\'état par défaut');
        }
    }
    
    /**
     * Sauvegarder l'état dans le fichier
     */
    saveState() {
        try {
            // Mettre à jour le timestamp
            this.state.system.last_sync = new Date().toISOString();
            
            // Sauvegarder l'état principal
            fs.writeFileSync(this.stateFile, JSON.stringify(this.state, null, 2));
            
            // Créer une sauvegarde
            const backupFile = path.join(this.backupDir, `state_backup_${Date.now()}.json`);
            fs.writeFileSync(backupFile, JSON.stringify(this.state, null, 2));
            
            // Nettoyer les anciennes sauvegardes (garder seulement les 10 dernières)
            this.cleanupBackups();
            
            console.log('💾 État global sauvegardé');
        } catch (error) {
            console.error('❌ Erreur lors de la sauvegarde de l\'état:', error);
        }
    }
    
    /**
     * Nettoyer les anciennes sauvegardes
     */
    cleanupBackups() {
        try {
            const files = fs.readdirSync(this.backupDir)
                .filter(file => file.startsWith('state_backup_'))
                .map(file => ({
                    name: file,
                    path: path.join(this.backupDir, file),
                    time: fs.statSync(path.join(this.backupDir, file)).mtime
                }))
                .sort((a, b) => b.time - a.time);
            
            // Supprimer les fichiers au-delà des 10 plus récents
            files.slice(10).forEach(file => {
                fs.unlinkSync(file.path);
            });
        } catch (error) {
            console.error('❌ Erreur lors du nettoyage des sauvegardes:', error);
        }
    }
    
    /**
     * Fusionner profondément deux objets
     */
    mergeDeep(target, source) {
        const output = Object.assign({}, target);
        if (this.isObject(target) && this.isObject(source)) {
            Object.keys(source).forEach(key => {
                if (this.isObject(source[key])) {
                    if (!(key in target))
                        Object.assign(output, { [key]: source[key] });
                    else
                        output[key] = this.mergeDeep(target[key], source[key]);
                } else {
                    Object.assign(output, { [key]: source[key] });
                }
            });
        }
        return output;
    }
    
    /**
     * Vérifier si une valeur est un objet
     */
    isObject(item) {
        return item && typeof item === 'object' && !Array.isArray(item);
    }
    
    /**
     * Mettre à jour une partie de l'état
     */
    updateState(path, value) {
        const keys = path.split('.');
        let current = this.state;
        
        // Naviguer jusqu'au parent de la propriété à modifier
        for (let i = 0; i < keys.length - 1; i++) {
            if (!current[keys[i]]) {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }
        
        // Mettre à jour la valeur
        current[keys[keys.length - 1]] = value;
        
        // Sauvegarder automatiquement
        this.saveState();
        
        console.log(`🔄 État mis à jour: ${path} = ${JSON.stringify(value)}`);
    }
    
    /**
     * Obtenir une partie de l'état
     */
    getState(path = null) {
        if (!path) {
            return this.state;
        }
        
        const keys = path.split('.');
        let current = this.state;
        
        for (const key of keys) {
            if (current[key] === undefined) {
                return null;
            }
            current = current[key];
        }
        
        return current;
    }
    
    /**
     * Incrémenter une valeur numérique
     */
    incrementValue(path, amount = 1) {
        const currentValue = this.getState(path) || 0;
        this.updateState(path, currentValue + amount);
    }
    
    /**
     * Démarrer la synchronisation automatique
     */
    startAutoSync() {
        // Sauvegarder toutes les 30 secondes
        setInterval(() => {
            this.saveState();
        }, 30000);
        
        // Mettre à jour l'uptime toutes les minutes
        setInterval(() => {
            this.incrementValue('performance.uptime', 1);
        }, 60000);
        
        console.log('⏰ Synchronisation automatique démarrée');
    }
    
    /**
     * Obtenir un résumé de l'état pour les APIs
     */
    getApiSummary() {
        return {
            agent: this.state.agent,
            thermal_memory: {
                global_temp: this.state.thermal_memory.global_temp,
                total_memories: this.state.thermal_memory.total_memories,
                zones: this.state.thermal_memory.zones.map(zone => ({
                    name: zone.name,
                    temperature: zone.temperature,
                    count: zone.count
                }))
            },
            performance: this.state.performance,
            system: {
                version: this.state.system.version,
                uptime: this.state.performance.uptime,
                last_sync: this.state.system.last_sync
            }
        };
    }
}

module.exports = GlobalStateManager;
