/**
 * Système de sauvegarde d'urgence pour protéger l'ensemble du cerveau artificiel
 * Ce système protège contre les coupures de courant et autres interruptions
 */

const fs = require('fs');
const path = require('path');
const zlib = require('zlib');
const { promisify } = require('util');

const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);

class EmergencyBackupSystem {
    constructor(options = {}) {
        this.config = {
            backupInterval: options.backupInterval || 10000, // 10 secondes - plus fréquent
            emergencyBackupInterval: options.emergencyBackupInterval || 2000, // 2 secondes pour l'urgence
            backupDir: options.backupDir || path.join(__dirname, 'data', 'emergency_backups'),
            maxBackups: options.maxBackups || 100, // Garder 100 sauvegardes max
            compressionEnabled: options.compressionEnabled !== undefined ? options.compressionEnabled : true, // Activé par défaut
            kyberCompressionEnabled: options.kyberCompressionEnabled !== undefined ? options.kyberCompressionEnabled : true, // Compression KYBER
            debug: options.debug || true,
            // NOUVELLES OPTIONS DE PROTECTION RENFORCÉE
            multipleBackupLocations: true, // Sauvegardes multiples
            realTimeSync: true, // Synchronisation temps réel
            memoryProtection: true, // Protection mémoire active
            autoRecovery: true, // Récupération automatique
            redundancy: 3 // Nombre de copies redondantes
        };

        this.isRunning = false;
        this.backupTimer = null;
        this.emergencyTimer = null;
        this.realTimeTimer = null;
        this.lastBackupTime = 0;
        this.backupCounter = 0;
        this.memoryWatcher = null;
        this.backupLocations = [];

        // Références aux systèmes à sauvegarder
        this.thermalMemory = null;
        this.artificialBrain = null;
        this.kyberAccelerators = null;
        this.autoIntelligence = null;
        this.agentManager = null;

        this.initializeBackupDirectories();
        this.setupProcessHandlers();
        this.initializeRealTimeProtection();
        this.setupEmergencyHandlers();
    }

    /**
     * Initialise les répertoires de sauvegarde multiples
     */
    async initializeBackupDirectories() {
        try {
            // Répertoire principal
            if (!fs.existsSync(this.config.backupDir)) {
                await mkdirAsync(this.config.backupDir, { recursive: true });
            }
            this.backupLocations.push(this.config.backupDir);

            // Répertoires de sauvegarde redondants si activés
            if (this.config.multipleBackupLocations) {
                const redundantDirs = [
                    path.join(__dirname, 'data', 'backup_redundant_1'),
                    path.join(__dirname, 'data', 'backup_redundant_2'),
                    path.join(__dirname, 'data', 'backup_redundant_3')
                ];

                for (const dir of redundantDirs) {
                    if (!fs.existsSync(dir)) {
                        await mkdirAsync(dir, { recursive: true });
                    }
                    this.backupLocations.push(dir);
                }
            }

            if (this.config.debug) {
                console.log(`🛡️ [EmergencyBackup] ${this.backupLocations.length} répertoires de sauvegarde initialisés`);
                this.backupLocations.forEach((dir, index) => {
                    console.log(`   ${index + 1}. ${dir}`);
                });
            }
        } catch (error) {
            console.error('❌ [EmergencyBackup] Erreur lors de l\'initialisation des répertoires:', error);
        }
    }

    /**
     * Configure les gestionnaires de processus pour les coupures
     */
    setupProcessHandlers() {
        // Gestionnaire pour SIGINT (Ctrl+C)
        process.on('SIGINT', async () => {
            console.log('\n🚨 [EmergencyBackup] Signal SIGINT reçu - Sauvegarde d\'urgence...');
            await this.performEmergencyBackup();
            process.exit(0);
        });

        // Gestionnaire pour SIGTERM
        process.on('SIGTERM', async () => {
            console.log('\n🚨 [EmergencyBackup] Signal SIGTERM reçu - Sauvegarde d\'urgence...');
            await this.performEmergencyBackup();
            process.exit(0);
        });

        // Gestionnaire pour les exceptions non capturées
        process.on('uncaughtException', async (error) => {
            console.error('🚨 [EmergencyBackup] Exception non capturée - Sauvegarde d\'urgence...', error);
            await this.performEmergencyBackup();
            process.exit(1);
        });

        // Gestionnaire pour les promesses rejetées
        process.on('unhandledRejection', async (reason, promise) => {
            console.error('🚨 [EmergencyBackup] Promesse rejetée - Sauvegarde d\'urgence...', reason);
            await this.performEmergencyBackup();
        });

        if (this.config.debug) {
            console.log('🛡️ [EmergencyBackup] Gestionnaires de processus configurés');
        }
    }

    /**
     * Initialise la protection temps réel
     */
    initializeRealTimeProtection() {
        if (!this.config.realTimeSync) return;

        // Surveillance continue des changements critiques
        this.realTimeTimer = setInterval(() => {
            this.checkCriticalChanges();
        }, 1000); // Vérification chaque seconde

        if (this.config.debug) {
            console.log('🛡️ [EmergencyBackup] Protection temps réel activée');
        }
    }

    /**
     * Configure les gestionnaires d'urgence supplémentaires
     */
    setupEmergencyHandlers() {
        // Gestionnaire pour les erreurs de mémoire
        if (this.config.memoryProtection) {
            setInterval(() => {
                this.checkMemoryHealth();
            }, 5000); // Vérification toutes les 5 secondes
        }

        // Gestionnaire pour la détection de coupure de courant
        this.setupPowerFailureDetection();

        if (this.config.debug) {
            console.log('🛡️ [EmergencyBackup] Gestionnaires d\'urgence configurés');
        }
    }

    /**
     * Vérifie les changements critiques en temps réel
     */
    async checkCriticalChanges() {
        try {
            if (!this.thermalMemory) return;

            const currentStats = this.thermalMemory.getStats ? this.thermalMemory.getStats() : {};

            // Conditions critiques nécessitant une sauvegarde immédiate
            const criticalConditions = [
                currentStats.totalEntries && currentStats.totalEntries % 100 === 0, // Tous les 100 nouvelles entrées
                currentStats.averageTemperature > 0.95, // Température critique
                currentStats.cyclesPerformed && currentStats.cyclesPerformed % 50 === 0 // Tous les 50 cycles
            ];

            if (criticalConditions.some(condition => condition)) {
                await this.performCriticalDataBackup();
            }
        } catch (error) {
            console.error('❌ [EmergencyBackup] Erreur vérification changements critiques:', error);
        }
    }

    /**
     * Vérifie la santé de la mémoire
     */
    checkMemoryHealth() {
        try {
            const memUsage = process.memoryUsage();
            const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
            const heapTotalMB = memUsage.heapTotal / 1024 / 1024;
            const usagePercent = (heapUsedMB / heapTotalMB) * 100;

            // Si l'utilisation mémoire dépasse 90%, déclencher une sauvegarde d'urgence
            if (usagePercent > 90) {
                console.warn(`⚠️ [EmergencyBackup] Utilisation mémoire critique: ${usagePercent.toFixed(1)}%`);
                this.performEmergencyBackup();
            }
        } catch (error) {
            console.error('❌ [EmergencyBackup] Erreur vérification santé mémoire:', error);
        }
    }

    /**
     * Configure la détection de coupure de courant
     */
    setupPowerFailureDetection() {
        // Surveillance des signaux système
        const powerSignals = ['SIGPWR', 'SIGUSR1', 'SIGUSR2'];

        powerSignals.forEach(signal => {
            process.on(signal, async () => {
                console.log(`🚨 [EmergencyBackup] Signal de coupure détecté: ${signal}`);
                await this.performEmergencyBackup();
            });
        });

        // Surveillance de la batterie (si disponible)
        if (process.platform === 'darwin') {
            setInterval(() => {
                this.checkBatteryStatus();
            }, 10000); // Vérification toutes les 10 secondes
        }
    }

    /**
     * Vérifie le statut de la batterie (macOS)
     */
    async checkBatteryStatus() {
        try {
            const { exec } = require('child_process');
            exec('pmset -g batt', (error, stdout) => {
                if (error) return;

                // Analyser la sortie pour détecter une batterie faible
                if (stdout.includes('Battery Power') && stdout.includes('%')) {
                    const batteryMatch = stdout.match(/(\d+)%/);
                    if (batteryMatch) {
                        const batteryLevel = parseInt(batteryMatch[1]);
                        if (batteryLevel < 20) {
                            console.warn(`⚠️ [EmergencyBackup] Batterie faible: ${batteryLevel}%`);
                            this.performCriticalDataBackup();
                        }
                    }
                }
            });
        } catch (error) {
            // Ignorer les erreurs de vérification batterie
        }
    }

    /**
     * Définit les références aux systèmes à sauvegarder
     */
    setSystemReferences(systems) {
        this.thermalMemory = systems.thermalMemory || null;
        this.artificialBrain = systems.artificialBrain || null;
        this.kyberAccelerators = systems.kyberAccelerators || null;
        this.autoIntelligence = systems.autoIntelligence || null;
        this.agentManager = systems.agentManager || null;

        if (this.config.debug) {
            console.log('🛡️ [EmergencyBackup] Références système définies');
        }
    }

    /**
     * Démarre le système de sauvegarde
     */
    start() {
        if (this.isRunning) {
            console.warn('⚠️ [EmergencyBackup] Le système est déjà en cours d\'exécution');
            return;
        }

        this.isRunning = true;

        // Sauvegarde régulière
        this.backupTimer = setInterval(() => {
            this.performRegularBackup();
        }, this.config.backupInterval);

        // Sauvegarde d'urgence très fréquente (pour les données critiques) - SEULEMENT si activée
        if (this.config.emergencyBackupInterval > 0) {
            this.emergencyTimer = setInterval(() => {
                this.performCriticalDataBackup();
            }, this.config.emergencyBackupInterval);
        }

        if (this.config.debug) {
            console.log('🛡️ [EmergencyBackup] Système de sauvegarde démarré');
            console.log(`   - Sauvegarde régulière: toutes les ${this.config.backupInterval / 1000}s`);
            console.log(`   - Sauvegarde critique: toutes les ${this.config.emergencyBackupInterval / 1000}s`);
        }
    }

    /**
     * Arrête le système de sauvegarde
     */
    stop() {
        if (!this.isRunning) {
            return;
        }

        this.isRunning = false;

        if (this.backupTimer) {
            clearInterval(this.backupTimer);
            this.backupTimer = null;
        }

        if (this.emergencyTimer) {
            clearInterval(this.emergencyTimer);
            this.emergencyTimer = null;
        }

        if (this.config.debug) {
            console.log('🛡️ [EmergencyBackup] Système de sauvegarde arrêté');
        }
    }

    /**
     * Compresse les données avec l'accélérateur KYBER
     */
    async compressWithKyber(data) {
        try {
            if (!this.config.kyberCompressionEnabled) {
                return { compressed: false, data: data, originalSize: Buffer.byteLength(data, 'utf8') };
            }

            const startTime = Date.now();

            // Compression KYBER multi-niveaux
            let compressedData = data;
            let compressionLevel = 1;

            // Niveau 1: Compression gzip standard
            compressedData = zlib.gzipSync(Buffer.from(compressedData, 'utf8'));

            // Niveau 2: Compression KYBER (algorithme optimisé)
            if (this.kyberAccelerators && this.kyberAccelerators.compressData) {
                compressedData = await this.kyberAccelerators.compressData(compressedData);
                compressionLevel = 2;
            }

            // Niveau 3: Compression ultra si données très volumineuses
            if (compressedData.length > 1024 * 1024) { // > 1MB
                compressedData = zlib.deflateSync(compressedData, { level: 9 });
                compressionLevel = 3;
            }

            const compressionTime = Date.now() - startTime;
            const originalSize = Buffer.byteLength(data, 'utf8');
            const compressedSize = compressedData.length;
            const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

            if (this.config.debug) {
                console.log(`🗜️ [EmergencyBackup] Compression KYBER niveau ${compressionLevel}: ${originalSize} -> ${compressedSize} bytes (${compressionRatio}% économie) en ${compressionTime}ms`);
            }

            return {
                compressed: true,
                data: compressedData,
                originalSize: originalSize,
                compressedSize: compressedSize,
                compressionRatio: parseFloat(compressionRatio),
                compressionLevel: compressionLevel,
                compressionTime: compressionTime,
                algorithm: 'KYBER-MULTI'
            };

        } catch (error) {
            console.error('❌ [EmergencyBackup] Erreur compression KYBER:', error);
            // Fallback vers compression standard
            try {
                const compressed = zlib.gzipSync(Buffer.from(data, 'utf8'));
                return {
                    compressed: true,
                    data: compressed,
                    originalSize: Buffer.byteLength(data, 'utf8'),
                    compressedSize: compressed.length,
                    algorithm: 'GZIP-FALLBACK',
                    error: error.message
                };
            } catch (fallbackError) {
                return {
                    compressed: false,
                    data: data,
                    originalSize: Buffer.byteLength(data, 'utf8'),
                    error: fallbackError.message
                };
            }
        }
    }

    /**
     * Décompresse les données KYBER
     */
    async decompressKyber(compressedData, metadata) {
        try {
            if (!metadata.compressed) {
                return compressedData;
            }

            let data = compressedData;

            // Décompression selon l'algorithme utilisé
            if (metadata.algorithm === 'KYBER-MULTI') {
                // Décompression inverse des niveaux
                if (metadata.compressionLevel >= 3) {
                    data = zlib.inflateSync(data);
                }
                if (metadata.compressionLevel >= 2 && this.kyberAccelerators && this.kyberAccelerators.decompressData) {
                    data = await this.kyberAccelerators.decompressData(data);
                }
                if (metadata.compressionLevel >= 1) {
                    data = zlib.gunzipSync(data);
                }
            } else if (metadata.algorithm === 'GZIP-FALLBACK') {
                data = zlib.gunzipSync(data);
            }

            return data.toString('utf8');

        } catch (error) {
            console.error('❌ [EmergencyBackup] Erreur décompression KYBER:', error);
            throw error;
        }
    }

    /**
     * Effectue une sauvegarde régulière complète avec redondance et compression KYBER
     */
    async performRegularBackup() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupData = await this.collectAllSystemData();
            const jsonData = JSON.stringify(backupData, null, 2);

            // Compression KYBER
            const compressionResult = await this.compressWithKyber(jsonData);

            let successfulBackups = 0;
            const backupPromises = [];

            // Sauvegarder dans tous les emplacements configurés
            for (const location of this.backupLocations) {
                const extension = compressionResult.compressed ? '.kyber' : '.json';
                const backupPath = path.join(location, `complete_backup_${timestamp}${extension}`);

                // Créer le fichier de métadonnées
                const metadataPath = path.join(location, `complete_backup_${timestamp}.meta.json`);
                const metadata = {
                    timestamp: timestamp,
                    backupType: 'complete',
                    compression: compressionResult.compressed ? {
                        algorithm: compressionResult.algorithm,
                        originalSize: compressionResult.originalSize,
                        compressedSize: compressionResult.compressedSize,
                        compressionRatio: compressionResult.compressionRatio,
                        compressionLevel: compressionResult.compressionLevel,
                        compressionTime: compressionResult.compressionTime
                    } : null
                };

                const backupPromise = Promise.all([
                    writeFileAsync(backupPath, compressionResult.data),
                    writeFileAsync(metadataPath, JSON.stringify(metadata, null, 2))
                ])
                    .then(() => {
                        successfulBackups++;
                        if (this.config.debug) {
                            const sizeInfo = compressionResult.compressed
                                ? ` (${compressionResult.compressionRatio}% compression)`
                                : '';
                            console.log(`💾 [EmergencyBackup] Sauvegarde réussie: ${backupPath}${sizeInfo}`);
                        }
                        return backupPath;
                    })
                    .catch(error => {
                        console.error(`❌ [EmergencyBackup] Échec sauvegarde ${backupPath}:`, error);
                        return null;
                    });

                backupPromises.push(backupPromise);
            }

            // Attendre toutes les sauvegardes
            const results = await Promise.all(backupPromises);
            const successfulPaths = results.filter(path => path !== null);

            this.lastBackupTime = Date.now();
            this.backupCounter++;

            if (this.config.debug) {
                const compressionInfo = compressionResult.compressed
                    ? ` avec compression KYBER (${compressionResult.compressionRatio}% économie)`
                    : '';
                console.log(`💾 [EmergencyBackup] Sauvegarde complète #${this.backupCounter}: ${successfulPaths.length}/${this.backupLocations.length} réussies${compressionInfo}`);
            }

            // Nettoyer les anciennes sauvegardes dans tous les emplacements
            await this.cleanupOldBackups();

            // Vérifier qu'au moins une sauvegarde a réussi
            if (successfulPaths.length === 0) {
                throw new Error('Aucune sauvegarde n\'a réussi dans aucun emplacement');
            }

        } catch (error) {
            console.error('❌ [EmergencyBackup] Erreur lors de la sauvegarde régulière:', error);
        }
    }

    /**
     * Effectue une sauvegarde des données critiques uniquement
     */
    async performCriticalDataBackup() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const criticalData = await this.collectCriticalData();

            const backupPath = path.join(
                this.config.backupDir,
                `critical_backup_${timestamp}.json`
            );

            await writeFileAsync(backupPath, JSON.stringify(criticalData, null, 2));

        } catch (error) {
            console.error('❌ [EmergencyBackup] Erreur lors de la sauvegarde critique:', error);
        }
    }

    /**
     * Effectue une sauvegarde d'urgence (en cas de coupure) avec redondance maximale
     */
    async performEmergencyBackup() {
        try {
            console.log('🚨 [EmergencyBackup] SAUVEGARDE D\'URGENCE EN COURS...');

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const emergencyData = await this.collectAllSystemData();
            const jsonData = JSON.stringify(emergencyData, null, 2);

            let successfulBackups = 0;

            // Sauvegarder SYNCHRONIQUEMENT dans TOUS les emplacements pour garantir la réussite
            for (const location of this.backupLocations) {
                try {
                    const emergencyPath = path.join(location, `EMERGENCY_BACKUP_${timestamp}.json`);

                    // Sauvegarde synchrone pour être sûr qu'elle se termine avant une coupure
                    fs.writeFileSync(emergencyPath, jsonData);
                    successfulBackups++;

                    console.log(`✅ [EmergencyBackup] Sauvegarde d'urgence réussie: ${emergencyPath}`);
                } catch (locationError) {
                    console.error(`❌ [EmergencyBackup] Échec sauvegarde d'urgence ${location}:`, locationError);
                }
            }

            // Créer aussi une sauvegarde de secours dans le répertoire temporaire
            try {
                const tempBackupPath = path.join(require('os').tmpdir(), `EMERGENCY_BACKUP_${timestamp}.json`);
                fs.writeFileSync(tempBackupPath, jsonData);
                successfulBackups++;
                console.log(`✅ [EmergencyBackup] Sauvegarde d'urgence temporaire: ${tempBackupPath}`);
            } catch (tempError) {
                console.error('❌ [EmergencyBackup] Échec sauvegarde temporaire:', tempError);
            }

            console.log(`✅ [EmergencyBackup] Sauvegarde d'urgence terminée: ${successfulBackups} copies créées`);

            if (successfulBackups === 0) {
                throw new Error('AUCUNE SAUVEGARDE D\'URGENCE N\'A RÉUSSI !');
            }

        } catch (error) {
            console.error('❌ [EmergencyBackup] ÉCHEC CRITIQUE DE LA SAUVEGARDE D\'URGENCE:', error);

            // Dernière tentative de sauvegarde minimale
            try {
                const minimalData = {
                    timestamp: new Date().toISOString(),
                    emergency: true,
                    thermalMemoryStats: this.thermalMemory ? (this.thermalMemory.getStats ? this.thermalMemory.getStats() : {}) : {},
                    brainQI: this.artificialBrain ? this.artificialBrain.qi : 0
                };

                const emergencyMinimalPath = path.join(require('os').tmpdir(), `EMERGENCY_MINIMAL_${Date.now()}.json`);
                fs.writeFileSync(emergencyMinimalPath, JSON.stringify(minimalData, null, 2));
                console.log(`🆘 [EmergencyBackup] Sauvegarde minimale d'urgence: ${emergencyMinimalPath}`);
            } catch (minimalError) {
                console.error('💀 [EmergencyBackup] ÉCHEC TOTAL - AUCUNE SAUVEGARDE POSSIBLE:', minimalError);
            }
        }
    }

    /**
     * Collecte toutes les données du système
     */
    async collectAllSystemData() {
        const data = {
            timestamp: new Date().toISOString(),
            backupType: 'complete',
            systems: {}
        };

        // Mémoire thermique
        if (this.thermalMemory) {
            try {
                data.systems.thermalMemory = {
                    zones: this.thermalMemory.zones || {},
                    emotionalState: this.thermalMemory.emotionalState || {},
                    stats: this.thermalMemory.getStats ? this.thermalMemory.getStats() : {},
                    config: this.thermalMemory.config || {}
                };
            } catch (error) {
                console.error('❌ Erreur collecte mémoire thermique:', error);
                data.systems.thermalMemory = { error: error.message };
            }
        }

        // Cerveau artificiel
        if (this.artificialBrain) {
            try {
                data.systems.artificialBrain = {
                    neurons: this.artificialBrain.neurons || [],
                    synapses: this.artificialBrain.synapses || [],
                    networks: this.artificialBrain.networks || {},
                    qi: this.artificialBrain.qi || 0,
                    emotionalState: this.artificialBrain.emotionalState || {},
                    learningHistory: this.artificialBrain.learningHistory || []
                };
            } catch (error) {
                console.error('❌ Erreur collecte cerveau artificiel:', error);
                data.systems.artificialBrain = { error: error.message };
            }
        }

        // Accélérateurs Kyber
        if (this.kyberAccelerators) {
            try {
                data.systems.kyberAccelerators = {
                    accelerators: this.kyberAccelerators.accelerators || {},
                    stats: this.kyberAccelerators.getStats ? this.kyberAccelerators.getStats() : {},
                    config: this.kyberAccelerators.config || {}
                };
            } catch (error) {
                console.error('❌ Erreur collecte accélérateurs Kyber:', error);
                data.systems.kyberAccelerators = { error: error.message };
            }
        }

        // Intelligence automatique
        if (this.autoIntelligence) {
            try {
                data.systems.autoIntelligence = {
                    performanceHistory: this.autoIntelligence.performanceHistory || [],
                    autoAccelerators: this.autoIntelligence.autoAccelerators || new Map(),
                    config: this.autoIntelligence.config || {}
                };
            } catch (error) {
                console.error('❌ Erreur collecte intelligence automatique:', error);
                data.systems.autoIntelligence = { error: error.message };
            }
        }

        // Gestionnaire d'agents
        if (this.agentManager) {
            try {
                data.systems.agentManager = {
                    agents: this.agentManager.agents || {},
                    defaultAgent: this.agentManager.defaultAgent || null,
                    stats: this.agentManager.getStats ? this.agentManager.getStats() : {}
                };
            } catch (error) {
                console.error('❌ Erreur collecte gestionnaire d\'agents:', error);
                data.systems.agentManager = { error: error.message };
            }
        }

        return data;
    }

    /**
     * Collecte uniquement les données critiques
     */
    async collectCriticalData() {
        const data = {
            timestamp: new Date().toISOString(),
            backupType: 'critical',
            critical: {}
        };

        // Données critiques de la mémoire thermique
        if (this.thermalMemory && this.thermalMemory.zones) {
            data.critical.memoryZones = {
                longTerm: this.thermalMemory.zones[5] || {},
                archive: this.thermalMemory.zones[6] || {}
            };
        }

        // QI et état du cerveau
        if (this.artificialBrain) {
            data.critical.brainState = {
                qi: this.artificialBrain.qi || 0,
                neuronCount: this.artificialBrain.neurons ? this.artificialBrain.neurons.length : 0,
                emotionalState: this.artificialBrain.emotionalState || {}
            };
        }

        return data;
    }

    /**
     * Nettoie les anciennes sauvegardes dans tous les emplacements
     */
    async cleanupOldBackups() {
        for (const location of this.backupLocations) {
            try {
                if (!fs.existsSync(location)) continue;

                const files = fs.readdirSync(location);
                const backupFiles = files
                    .filter(file => file.endsWith('.json') || file.endsWith('.kyber'))
                    .map(file => ({
                        name: file,
                        path: path.join(location, file),
                        time: fs.statSync(path.join(location, file)).mtime,
                        metadataPath: path.join(location, file.replace(/\.(json|kyber)$/, '.meta.json'))
                    }))
                    .sort((a, b) => b.time - a.time);

                // Supprimer les sauvegardes en excès
                if (backupFiles.length > this.config.maxBackups) {
                    const filesToDelete = backupFiles.slice(this.config.maxBackups);

                    for (const file of filesToDelete) {
                        // Supprimer le fichier de sauvegarde
                        fs.unlinkSync(file.path);

                        // Supprimer le fichier de métadonnées s'il existe
                        if (fs.existsSync(file.metadataPath)) {
                            fs.unlinkSync(file.metadataPath);
                        }

                        if (this.config.debug) {
                            console.log(`🗑️ [EmergencyBackup] Ancienne sauvegarde supprimée: ${file.name} (${location})`);
                        }
                    }
                }
            } catch (error) {
                console.error(`❌ [EmergencyBackup] Erreur lors du nettoyage de ${location}:`, error);
            }
        }
    }

    /**
     * Restaure une sauvegarde avec décompression automatique
     */
    async restoreBackup(backupPath) {
        try {
            console.log(`🔄 [EmergencyBackup] Restauration depuis: ${backupPath}`);

            let backupData;

            // Vérifier si c'est un fichier compressé
            if (backupPath.endsWith('.kyber')) {
                // Charger les métadonnées
                const metadataPath = backupPath.replace('.kyber', '.meta.json');
                if (!fs.existsSync(metadataPath)) {
                    throw new Error('Fichier de métadonnées manquant pour la sauvegarde compressée');
                }

                const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
                const compressedData = fs.readFileSync(backupPath);

                // Décompresser les données
                const decompressedData = await this.decompressKyber(compressedData, metadata.compression);
                backupData = JSON.parse(decompressedData);

                console.log(`🗜️ [EmergencyBackup] Décompression réussie: ${metadata.compression.compressedSize} -> ${metadata.compression.originalSize} bytes`);
            } else {
                // Fichier JSON standard
                backupData = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
            }

            // Restaurer les systèmes
            if (backupData.systems) {
                // Restaurer la mémoire thermique
                if (backupData.systems.thermalMemory && this.thermalMemory) {
                    try {
                        if (this.thermalMemory.restoreFromBackup) {
                            await this.thermalMemory.restoreFromBackup(backupData.systems.thermalMemory);
                            console.log('✅ [EmergencyBackup] Mémoire thermique restaurée');
                        }
                    } catch (error) {
                        console.error('❌ [EmergencyBackup] Erreur restauration mémoire thermique:', error);
                    }
                }

                // Restaurer le cerveau artificiel
                if (backupData.systems.artificialBrain && this.artificialBrain) {
                    try {
                        if (this.artificialBrain.restoreFromBackup) {
                            await this.artificialBrain.restoreFromBackup(backupData.systems.artificialBrain);
                            console.log('✅ [EmergencyBackup] Cerveau artificiel restauré');
                        }
                    } catch (error) {
                        console.error('❌ [EmergencyBackup] Erreur restauration cerveau artificiel:', error);
                    }
                }

                // Restaurer les accélérateurs Kyber
                if (backupData.systems.kyberAccelerators && this.kyberAccelerators) {
                    try {
                        if (this.kyberAccelerators.restoreFromBackup) {
                            await this.kyberAccelerators.restoreFromBackup(backupData.systems.kyberAccelerators);
                            console.log('✅ [EmergencyBackup] Accélérateurs Kyber restaurés');
                        }
                    } catch (error) {
                        console.error('❌ [EmergencyBackup] Erreur restauration accélérateurs Kyber:', error);
                    }
                }
            }

            console.log('✅ [EmergencyBackup] Restauration terminée avec succès');
            return true;

        } catch (error) {
            console.error('❌ [EmergencyBackup] Erreur lors de la restauration:', error);
            return false;
        }
    }

    /**
     * Obtient les statistiques du système de sauvegarde
     */
    getStats() {
        return {
            isRunning: this.isRunning,
            backupCount: this.backupCounter,
            lastBackupTime: this.lastBackupTime,
            backupDirectory: this.config.backupDir,
            config: this.config
        };
    }
}

module.exports = EmergencyBackupSystem;
