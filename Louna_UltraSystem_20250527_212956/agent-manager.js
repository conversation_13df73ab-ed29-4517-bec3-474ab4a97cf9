/**
 * Gestionnaire d'agents pour l'application Louna
 * Ce module permet de gérer les agents (installation, configuration, suppression)
 * et leur intégration avec la mémoire thermique
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { spawn, exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
const EventEmitter = require('events');

// URL de l'API Ollama (utiliser IPv4 pour éviter les problèmes de résolution)
const OLLAMA_API_URL = 'http://127.0.0.1:11434/api';

/**
 * Classe AgentManager - Gère les agents de l'application
 */
class AgentManager extends EventEmitter {
  /**
   * Initialise le gestionnaire d'agents
   * @param {Object} options - Options de configuration
   */
  constructor(options = {}) {
    super();

    this.options = {
      configPath: options.configPath || path.join(__dirname, 'data', 'config'),
      agentsPath: options.agentsPath || path.join(__dirname, 'data', 'agents'),
      defaultAgentConfig: options.defaultAgentConfig || 'default-agent.json',
      thermalMemory: options.thermalMemory || null,
      kyberAccelerators: options.kyberAccelerators || null,
      debug: options.debug || false
    };

    // Créer les dossiers nécessaires
    if (!fs.existsSync(this.options.configPath)) {
      fs.mkdirSync(this.options.configPath, { recursive: true });
    }

    if (!fs.existsSync(this.options.agentsPath)) {
      fs.mkdirSync(this.options.agentsPath, { recursive: true });
    }

    // Chemin vers le fichier de configuration des agents
    this.agentsConfigPath = path.join(this.options.configPath, 'agents.json');

    // Initialiser la configuration des agents
    this.agents = this.loadAgentsConfig();

    // Agent actif
    this.activeAgent = null;

    // État d'Ollama
    this.ollamaStatus = {
      isRunning: false,
      version: null,
      lastCheck: null
    };

    // Statistiques
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTokensGenerated: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    };

    // Mémoire de travail pour les agents
    this.workingMemory = {
      shortTerm: [],
      contextual: {},
      sessionData: {}
    };

    this.log('Gestionnaire d\'agents initialisé');
  }

  /**
   * Charge la configuration des agents
   * @returns {Object} - Configuration des agents
   */
  loadAgentsConfig() {
    try {
      if (fs.existsSync(this.agentsConfigPath)) {
        const config = JSON.parse(fs.readFileSync(this.agentsConfigPath, 'utf8'));
        this.log(`Configuration des agents chargée: ${Object.keys(config.agents).length} agents trouvés`);
        return config;
      }
    } catch (error) {
      this.log(`Erreur lors du chargement de la configuration des agents: ${error.message}`, 'error');
    }

    // Configuration par défaut
    return {
      defaultAgent: null,
      agents: {}
    };
  }

  /**
   * Sauvegarde la configuration des agents
   * @returns {boolean} - True si la sauvegarde a réussi
   */
  saveAgentsConfig() {
    try {
      fs.writeFileSync(this.agentsConfigPath, JSON.stringify(this.agents, null, 2));
      this.log('Configuration des agents sauvegardée');
      return true;
    } catch (error) {
      this.log(`Erreur lors de la sauvegarde de la configuration des agents: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * Vérifie si Ollama est disponible
   * @returns {Promise<boolean>} - True si Ollama est disponible
   */
  async isOllamaAvailable() {
    try {
      // Vérifier si la dernière vérification date de moins de 10 secondes
      const now = Date.now();
      if (this.ollamaStatus.lastCheck && now - this.ollamaStatus.lastCheck < 10000) {
        return this.ollamaStatus.isRunning;
      }

      const response = await axios.get(`${OLLAMA_API_URL}/version`);
      this.ollamaStatus = {
        isRunning: true,
        version: response.data.version,
        lastCheck: now
      };

      this.log(`Ollama est disponible (version ${response.data.version})`);
      return true;
    } catch (error) {
      this.ollamaStatus = {
        isRunning: false,
        version: null,
        lastCheck: Date.now()
      };

      this.log('Ollama n\'est pas disponible', 'warn');
      return false;
    }
  }

  /**
   * Démarre Ollama
   * @returns {Promise<boolean>} - True si Ollama a été démarré avec succès
   */
  async startOllama() {
    try {
      // Vérifier si Ollama est déjà en cours d'exécution
      if (await this.isOllamaAvailable()) {
        this.log('Ollama est déjà en cours d\'exécution');
        return true;
      }

      this.log('Démarrage d\'Ollama...');

      // Démarrer Ollama en fonction du système d'exploitation
      if (process.platform === 'win32') {
        // Windows
        spawn('ollama', ['serve'], { detached: true, stdio: 'ignore' }).unref();
      } else {
        // macOS/Linux
        spawn('ollama', ['serve'], { detached: true, stdio: 'ignore' }).unref();
      }

      // Attendre que Ollama démarre
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (await this.isOllamaAvailable()) {
          this.log('Ollama a été démarré avec succès');
          return true;
        }

        attempts++;
      }

      this.log('Impossible de démarrer Ollama après plusieurs tentatives', 'error');
      return false;
    } catch (error) {
      this.log(`Erreur lors du démarrage d'Ollama: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * Récupère la liste des modèles disponibles dans Ollama
   * @returns {Promise<Array>} - Liste des modèles disponibles
   */
  async getAvailableModels() {
    try {
      // Vérifier si Ollama est disponible
      if (!await this.isOllamaAvailable()) {
        await this.startOllama();

        if (!await this.isOllamaAvailable()) {
          return [];
        }
      }

      const response = await axios.get(`${OLLAMA_API_URL}/tags`);
      const models = response.data.models || [];

      this.log(`${models.length} modèles disponibles dans Ollama`);
      return models;
    } catch (error) {
      this.log(`Erreur lors de la récupération des modèles disponibles: ${error.message}`, 'error');
      return [];
    }
  }

  /**
   * Installe un modèle dans Ollama
   * @param {string} modelName - Nom du modèle à installer
   * @returns {Promise<Object>} - Résultat de l'installation
   */
  async installModel(modelName) {
    try {
      // Vérifier si Ollama est disponible
      if (!await this.isOllamaAvailable()) {
        await this.startOllama();

        if (!await this.isOllamaAvailable()) {
          return { success: false, error: 'Ollama n\'est pas disponible' };
        }
      }

      this.log(`Installation du modèle ${modelName}...`);

      // Lancer l'installation du modèle
      const { stdout, stderr } = await execAsync(`ollama pull ${modelName}`);

      this.log(`Modèle ${modelName} installé avec succès`);
      return { success: true, stdout, stderr };
    } catch (error) {
      this.log(`Erreur lors de l'installation du modèle ${modelName}: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * Supprime un modèle d'Ollama
   * @param {string} modelName - Nom du modèle à supprimer
   * @returns {Promise<Object>} - Résultat de la suppression
   */
  async removeModel(modelName) {
    try {
      // Vérifier si Ollama est disponible
      if (!await this.isOllamaAvailable()) {
        return { success: false, error: 'Ollama n\'est pas disponible' };
      }

      this.log(`Suppression du modèle ${modelName}...`);

      // Lancer la suppression du modèle
      const { stdout, stderr } = await execAsync(`ollama rm ${modelName}`);

      this.log(`Modèle ${modelName} supprimé avec succès`);
      return { success: true, stdout, stderr };
    } catch (error) {
      this.log(`Erreur lors de la suppression du modèle ${modelName}: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * Ajoute un agent à la configuration
   * @param {Object} agentConfig - Configuration de l'agent
   * @returns {Promise<Object>} - Résultat de l'ajout
   */
  async addAgent(agentConfig) {
    try {
      // Vérifier si l'agent existe déjà
      if (this.agents.agents[agentConfig.id]) {
        return { success: false, error: `L'agent ${agentConfig.id} existe déjà` };
      }

      // Ajouter l'agent à la configuration
      this.agents.agents[agentConfig.id] = {
        ...agentConfig,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Si c'est le premier agent, le définir comme agent par défaut
      if (!this.agents.defaultAgent) {
        this.agents.defaultAgent = agentConfig.id;
      }

      // Sauvegarder la configuration
      this.saveAgentsConfig();

      this.log(`Agent ${agentConfig.id} ajouté avec succès`);
      return { success: true, agent: this.agents.agents[agentConfig.id] };
    } catch (error) {
      this.log(`Erreur lors de l'ajout de l'agent: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * Met à jour la configuration d'un agent
   * @param {string} agentId - ID de l'agent à mettre à jour
   * @param {Object} agentConfig - Nouvelle configuration de l'agent
   * @returns {Object} - Résultat de la mise à jour
   */
  updateAgent(agentId, agentConfig) {
    try {
      // Vérifier si l'agent existe
      if (!this.agents.agents[agentId]) {
        return { success: false, error: `L'agent ${agentId} n'existe pas` };
      }

      // Mettre à jour l'agent
      this.agents.agents[agentId] = {
        ...this.agents.agents[agentId],
        ...agentConfig,
        updatedAt: new Date().toISOString()
      };

      // Sauvegarder la configuration
      this.saveAgentsConfig();

      this.log(`Agent ${agentId} mis à jour avec succès`);
      return { success: true, agent: this.agents.agents[agentId] };
    } catch (error) {
      this.log(`Erreur lors de la mise à jour de l'agent ${agentId}: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * Supprime un agent de la configuration
   * @param {string} agentId - ID de l'agent à supprimer
   * @returns {Object} - Résultat de la suppression
   */
  removeAgent(agentId) {
    try {
      // Vérifier si l'agent existe
      if (!this.agents.agents[agentId]) {
        return { success: false, error: `L'agent ${agentId} n'existe pas` };
      }

      // Supprimer l'agent
      delete this.agents.agents[agentId];

      // Si c'était l'agent par défaut, définir un autre agent comme agent par défaut
      if (this.agents.defaultAgent === agentId) {
        const agentIds = Object.keys(this.agents.agents);
        this.agents.defaultAgent = agentIds.length > 0 ? agentIds[0] : null;
      }

      // Sauvegarder la configuration
      this.saveAgentsConfig();

      this.log(`Agent ${agentId} supprimé avec succès`);
      return { success: true };
    } catch (error) {
      this.log(`Erreur lors de la suppression de l'agent ${agentId}: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * Définit l'agent par défaut
   * @param {string} agentId - ID de l'agent à définir comme agent par défaut
   * @returns {Object} - Résultat de l'opération
   */
  setDefaultAgent(agentId) {
    try {
      // Vérifier si l'agent existe
      if (!this.agents.agents[agentId]) {
        return { success: false, error: `L'agent ${agentId} n'existe pas` };
      }

      // Définir l'agent par défaut
      this.agents.defaultAgent = agentId;

      // Sauvegarder la configuration
      this.saveAgentsConfig();

      this.log(`Agent ${agentId} défini comme agent par défaut`);
      return { success: true };
    } catch (error) {
      this.log(`Erreur lors de la définition de l'agent par défaut: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * Récupère l'agent par défaut
   * @returns {Object|null} - Agent par défaut ou null si aucun agent par défaut n'est défini
   */
  getDefaultAgent() {
    if (!this.agents.defaultAgent) {
      return null;
    }

    return this.agents.agents[this.agents.defaultAgent] || null;
  }

  /**
   * Récupère l'agent principal (celui marqué comme isMainAgent)
   * @returns {Object|null} - Agent principal ou null si aucun agent principal n'est défini
   */
  getMainAgent() {
    for (const agentId in this.agents.agents) {
      const agent = this.agents.agents[agentId];
      if (agent.isMainAgent) {
        return agent;
      }
    }

    // Si aucun agent n'est marqué comme principal, utiliser l'agent par défaut
    return this.getDefaultAgent();
  }

  /**
   * Récupère l'agent de formation (celui marqué comme isTrainingAgent)
   * @returns {Object|null} - Agent de formation ou null si aucun agent de formation n'est défini
   */
  getTrainingAgent() {
    for (const agentId in this.agents.agents) {
      const agent = this.agents.agents[agentId];
      if (agent.isTrainingAgent) {
        return agent;
      }
    }

    // Si aucun agent n'est marqué comme agent de formation, retourner null
    return null;
  }

  /**
   * Active un agent
   * @param {string} agentId - ID de l'agent à activer
   * @returns {Promise<Object>} - Résultat de l'activation
   */
  async activateAgent(agentId) {
    try {
      // Vérifier si l'agent existe
      if (!this.agents.agents[agentId]) {
        return { success: false, error: `L'agent ${agentId} n'existe pas` };
      }

      const agent = this.agents.agents[agentId];

      // Vérifier si le modèle est disponible
      if (agent.type === 'ollama') {
        const models = await this.getAvailableModels();
        const modelExists = models.some(model => model.name === agent.model);

        if (!modelExists) {
          this.log(`Le modèle ${agent.model} n'est pas disponible, tentative d'installation...`, 'warn');

          const installResult = await this.installModel(agent.model);

          if (!installResult.success) {
            return { success: false, error: `Impossible d'installer le modèle ${agent.model}` };
          }
        }
      }

      // Activer l'agent
      this.activeAgent = agent;

      this.log(`Agent ${agentId} activé avec succès`);
      return { success: true, agent };
    } catch (error) {
      this.log(`Erreur lors de l'activation de l'agent ${agentId}: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * Désactive l'agent actif
   * @returns {Object} - Résultat de la désactivation
   */
  deactivateAgent() {
    try {
      if (!this.activeAgent) {
        return { success: false, error: 'Aucun agent actif' };
      }

      const agentId = this.activeAgent.id;
      this.activeAgent = null;

      this.log(`Agent ${agentId} désactivé avec succès`);
      return { success: true };
    } catch (error) {
      this.log(`Erreur lors de la désactivation de l'agent: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * Envoie un message à l'agent actif
   * @param {string} message - Message à envoyer
   * @param {Array} history - Historique de la conversation
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Object>} - Réponse de l'agent
   */
  async sendMessage(message, history = [], options = {}) {
    try {
      // Vérifier si un agent est actif
      if (!this.activeAgent) {
        // Essayer d'activer l'agent principal d'abord
        const mainAgent = this.getMainAgent();

        if (mainAgent) {
          this.log(`Activation de l'agent principal: ${mainAgent.id}`);
          const activationResult = await this.activateAgent(mainAgent.id);

          if (!activationResult.success) {
            this.log(`Impossible d'activer l'agent principal, tentative avec l'agent par défaut`, 'warn');

            // Si l'activation de l'agent principal échoue, essayer avec l'agent par défaut
            const defaultAgent = this.getDefaultAgent();

            if (!defaultAgent) {
              return { success: false, error: 'Aucun agent actif et impossible d\'activer l\'agent principal ou par défaut' };
            }

            const defaultActivationResult = await this.activateAgent(defaultAgent.id);

            if (!defaultActivationResult.success) {
              return { success: false, error: `Impossible d'activer l'agent par défaut: ${defaultActivationResult.error}` };
            }
          }
        } else {
          // Si aucun agent principal n'est défini, utiliser l'agent par défaut
          const defaultAgent = this.getDefaultAgent();

          if (!defaultAgent) {
            return { success: false, error: 'Aucun agent actif et aucun agent par défaut' };
          }

          const activationResult = await this.activateAgent(defaultAgent.id);

          if (!activationResult.success) {
            return { success: false, error: `Impossible d'activer l'agent par défaut: ${activationResult.error}` };
          }
        }
      }

      this.stats.totalRequests++;
      const startTime = Date.now();

      // Appliquer un boost avec les accélérateurs Kyber si disponibles
      let boostFactor = 1.0;
      if (this.options.kyberAccelerators) {
        boostFactor = this.applyKyberBoost('processing', 1.0);
      }

      // Récupérer des informations pertinentes de la mémoire thermique si disponible
      let relevantMemories = [];
      if (this.options.thermalMemory) {
        relevantMemories = this.getRelevantMemories(message, 5);

        // Ajouter le message à la mémoire thermique
        this.addToThermalMemory('user_message', message, 0.7, 'conversation');
      }

      // Ajouter le message à la mémoire de travail à court terme
      this.addToShortTermMemory({
        role: 'user',
        content: message
      });

      // Préparer le contexte enrichi pour l'agent
      let contextPrompt = "";
      if (relevantMemories.length > 0) {
        contextPrompt = "🧠 MÉMOIRE THERMIQUE ACTIVE - Informations contextuelles :\n";
        relevantMemories.forEach((memory, index) => {
          const memoryAge = Date.now() - memory.created;
          const ageText = memoryAge < 3600000 ? 'récente' : memoryAge < 86400000 ? 'aujourd\'hui' : 'ancienne';
          contextPrompt += `${index + 1}. [${ageText}] ${memory.data}\n`;
        });
        contextPrompt += "\n💭 INSTRUCTIONS MÉMOIRE :\n";
        contextPrompt += "- Utilise ces informations pour contextualiser ta réponse\n";
        contextPrompt += "- Fais des liens entre les informations si pertinent\n";
        contextPrompt += "- Indique si tu as besoin d'informations plus récentes\n";
        contextPrompt += "- Mémorise les nouvelles informations importantes\n\n";

        // Ajouter le contexte à l'historique
        if (contextPrompt) {
          history.push({
            role: "system",
            content: contextPrompt
          });
        }
      } else {
        // Pas de mémoire pertinente trouvée
        contextPrompt = "🧠 MÉMOIRE THERMIQUE : Aucune information contextuelle trouvée.\n";
        contextPrompt += "💭 INSTRUCTIONS : Réponds avec tes connaissances de base et indique si des informations récentes seraient utiles.\n\n";

        history.push({
          role: "system",
          content: contextPrompt
        });
      }

      // Envoyer le message en fonction du type d'agent
      let response;

      this.log(`🚀 Envoi du message à l'agent actif: ${this.activeAgent.id} (type: ${this.activeAgent.type})`);

      if (this.activeAgent.type === 'ollama') {
        this.log(`📡 Appel de sendMessageToOllama avec le modèle: ${this.activeAgent.model}`);
        response = await this.sendMessageToOllama(message, history, {
          ...options,
          boostFactor
        });
        this.log(`📨 Réponse reçue de sendMessageToOllama: ${JSON.stringify(response, null, 2)}`);
      } else {
        response = { success: false, error: `Type d'agent non pris en charge: ${this.activeAgent.type}` };
      }

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Mettre à jour les statistiques
      if (response.success) {
        this.stats.successfulRequests++;
        this.stats.totalResponseTime += responseTime;
        this.stats.averageResponseTime = this.stats.totalResponseTime / this.stats.successfulRequests;

        if (response.usage && response.usage.completion_tokens) {
          this.stats.totalTokensGenerated += response.usage.completion_tokens;
        }

        // Analyser et mémoriser intelligemment la réponse
        if (this.options.thermalMemory && response.message && response.message.content) {
          const responseContent = response.message.content;

          // Analyser l'importance de la réponse
          const importance = this.analyzeResponseImportance(responseContent, message);

          // Extraire les informations factuelles
          const factualInfo = this.extractFactualInformation(responseContent);

          // Ajouter la réponse complète à la mémoire
          this.addToThermalMemory('agent_response', responseContent, importance, 'conversation');

          // Ajouter les informations factuelles séparément si trouvées
          if (factualInfo.length > 0) {
            factualInfo.forEach(fact => {
              this.addToThermalMemory('factual_knowledge', fact, 0.8, 'knowledge');
            });
          }

          this.log(`Réponse mémorisée avec importance ${importance}, ${factualInfo.length} faits extraits`);
        }

        // Ajouter la réponse à la mémoire de travail à court terme
        if (response.message) {
          this.addToShortTermMemory({
            role: 'assistant',
            content: response.message.content,
            timestamp: Date.now(),
            importance: this.analyzeResponseImportance(response.message.content, message)
          });
        }
      } else {
        this.stats.failedRequests++;
      }

      // Ajouter des informations supplémentaires à la réponse
      return {
        ...response,
        processingTime: responseTime,
        boostFactor,
        relevantMemories: relevantMemories.length
      };
    } catch (error) {
      this.log(`Erreur lors de l'envoi du message: ${error.message}`, 'error');
      this.stats.failedRequests++;
      return { success: false, error: error.message };
    }
  }

  /**
   * Envoie un message à Ollama avec timeout optimisé
   * @param {string} message - Message à envoyer
   * @param {Array} history - Historique de la conversation
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Object>} - Réponse d'Ollama
   */
  async sendMessageToOllama(message, history = [], options = {}) {
    const startTime = Date.now();

    try {
      // Timeout spécifique pour Ollama (configurable)
      const ollamaTimeout = options.timeout || 25000; // 25 secondes max pour Ollama

      // Vérifier rapidement si Ollama est disponible
      const isAvailable = await Promise.race([
        this.isOllamaAvailable(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout vérification Ollama')), 2000))
      ]);

      if (!isAvailable) {
        this.log('Ollama non disponible, tentative de démarrage rapide...');

        // Tentative de démarrage avec timeout court
        try {
          await Promise.race([
            this.startOllama(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout démarrage Ollama')), 3000))
          ]);

          // Vérification rapide après démarrage
          const isNowAvailable = await Promise.race([
            this.isOllamaAvailable(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout vérification post-démarrage')), 1000))
          ]);

          if (!isNowAvailable) {
            return { success: false, error: 'Ollama non disponible après démarrage' };
          }
        } catch (startError) {
          this.log(`Impossible de démarrer Ollama: ${startError.message}`);
          return { success: false, error: 'Service Ollama indisponible' };
        }
      }

      this.log(`Envoi du message à l'agent ${this.activeAgent.model}: "${message.substring(0, 50)}..."`);

      // Optimiser la requête pour la vitesse
      const generateRequestData = {
        model: this.activeAgent.model,
        prompt: this.optimizePromptForSpeed(message, history),
        stream: false,
        options: {
          temperature: options.temperature || this.activeAgent.temperature || 0.7,
          num_predict: Math.min(options.maxTokens || this.activeAgent.maxTokens || 500, 500), // Limiter pour la vitesse
          top_k: 10, // Réduire pour la vitesse
          top_p: 0.9,
          repeat_penalty: 1.1
        }
      };

      // Appel avec timeout strict
      const generateResponse = await Promise.race([
        axios.post(`${OLLAMA_API_URL}/generate`, generateRequestData, {
          timeout: ollamaTimeout,
          headers: {
            'Content-Type': 'application/json'
          }
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout requête Ollama')), ollamaTimeout)
        )
      ]);

      const responseTime = Date.now() - startTime;
      this.log(`Réponse reçue de l'agent en ${responseTime}ms: "${generateResponse.data.response.substring(0, 100)}..."`);

      return {
        success: true,
        message: {
          role: 'assistant',
          content: generateResponse.data.response
        },
        usage: {
          prompt_tokens: generateResponse.data.prompt_eval_count || 0,
          completion_tokens: generateResponse.data.eval_count || 0,
          total_tokens: (generateResponse.data.prompt_eval_count || 0) + (generateResponse.data.eval_count || 0)
        },
        responseTime: responseTime
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.log(`Erreur lors de l'envoi du message à Ollama (${responseTime}ms): ${error.message}`, 'error');

      // Retourner une erreur spécifique selon le type
      if (error.message.includes('Timeout')) {
        return { success: false, error: 'Timeout Ollama', timeout: true, responseTime: responseTime };
      } else if (error.code === 'ECONNREFUSED') {
        return { success: false, error: 'Service Ollama non disponible', unavailable: true, responseTime: responseTime };
      } else {
        return { success: false, error: error.message, responseTime: responseTime };
      }
    }
  }

  /**
   * Optimise le prompt pour la vitesse
   */
  optimizePromptForSpeed(message, history = []) {
    // Limiter l'historique pour réduire le temps de traitement
    const limitedHistory = history.slice(-3); // Seulement les 3 derniers échanges

    // Construire un prompt optimisé
    let optimizedPrompt = '';

    // Ajouter l'historique limité
    if (limitedHistory.length > 0) {
      optimizedPrompt += 'Contexte récent:\n';
      limitedHistory.forEach(item => {
        if (item.role === 'user') {
          optimizedPrompt += `Utilisateur: ${item.content.substring(0, 100)}\n`;
        } else if (item.role === 'assistant') {
          optimizedPrompt += `Assistant: ${item.content.substring(0, 100)}\n`;
        }
      });
      optimizedPrompt += '\n';
    }

    // Ajouter le message principal
    optimizedPrompt += `Question: ${message}\n\nRéponds de manière concise et utile:`;

    return optimizedPrompt;
  }

  /**
   * Définit la mémoire thermique à utiliser
   * @param {Object} thermalMemory - Instance de la mémoire thermique
   */
  setThermalMemory(thermalMemory) {
    this.options.thermalMemory = thermalMemory;
    this.log('Mémoire thermique définie');
  }

  /**
   * Définit les accélérateurs Kyber à utiliser
   * @param {Object} kyberAccelerators - Instance des accélérateurs Kyber
   */
  setKyberAccelerators(kyberAccelerators) {
    this.options.kyberAccelerators = kyberAccelerators;
    this.log('Accélérateurs Kyber définis');
  }

  /**
   * Récupère des informations pertinentes de la mémoire thermique
   * @param {string} query - Requête pour rechercher des informations
   * @param {number} limit - Nombre maximum d'entrées à récupérer
   * @returns {Array} - Entrées de mémoire pertinentes
   */
  getRelevantMemories(query, limit = 5) {
    if (!this.options.thermalMemory) {
      this.log('Mémoire thermique non disponible', 'warn');
      return [];
    }

    try {
      return this.options.thermalMemory.getRecentMemoriesForContext(query, limit);
    } catch (error) {
      this.log(`Erreur lors de la récupération des mémoires pertinentes: ${error.message}`, 'error');
      return [];
    }
  }

  /**
   * Ajoute une entrée à la mémoire thermique
   * @param {string} key - Clé de l'entrée
   * @param {string} data - Données de l'entrée
   * @param {number} importance - Importance de l'entrée (0-1)
   * @param {string} category - Catégorie de l'entrée
   * @returns {string|null} - ID de l'entrée ou null en cas d'erreur
   */
  addToThermalMemory(key, data, importance = 0.5, category = 'agent') {
    if (!this.options.thermalMemory) {
      this.log('Mémoire thermique non disponible', 'warn');
      return null;
    }

    try {
      const entryId = this.options.thermalMemory.add(key, data, importance, category);
      this.log(`Entrée ajoutée à la mémoire thermique: ${entryId}`);
      return entryId;
    } catch (error) {
      this.log(`Erreur lors de l'ajout à la mémoire thermique: ${error.message}`, 'error');
      return null;
    }
  }

  /**
   * Applique un boost avec les accélérateurs Kyber
   * @param {string} type - Type de boost (processing, memory, etc.)
   * @param {number} baseValue - Valeur de base
   * @returns {number} - Valeur boostée
   */
  applyKyberBoost(type, baseValue = 1.0) {
    if (!this.options.kyberAccelerators) {
      this.log('Accélérateurs Kyber non disponibles', 'warn');
      return baseValue;
    }

    try {
      const boostedValue = this.options.kyberAccelerators.applyBoost(type, baseValue);
      this.log(`Boost Kyber appliqué (${type}): ${baseValue} -> ${boostedValue}`);
      return boostedValue;
    } catch (error) {
      this.log(`Erreur lors de l'application du boost Kyber: ${error.message}`, 'error');
      return baseValue;
    }
  }

  /**
   * Ajoute une entrée à la mémoire de travail à court terme
   * @param {Object} entry - Entrée à ajouter
   * @param {number} maxEntries - Nombre maximum d'entrées à conserver
   */
  addToShortTermMemory(entry, maxEntries = 10) {
    // Ajouter l'entrée avec un timestamp
    this.workingMemory.shortTerm.push({
      ...entry,
      timestamp: Date.now()
    });

    // Limiter la taille de la mémoire à court terme
    if (this.workingMemory.shortTerm.length > maxEntries) {
      this.workingMemory.shortTerm.shift();
    }
  }

  /**
   * Récupère les entrées de la mémoire de travail à court terme
   * @param {number} limit - Nombre maximum d'entrées à récupérer
   * @returns {Array} - Entrées de la mémoire à court terme
   */
  getShortTermMemory(limit = 10) {
    // Trier par timestamp (plus récent en premier)
    const sorted = [...this.workingMemory.shortTerm].sort((a, b) => b.timestamp - a.timestamp);

    // Limiter le nombre d'entrées
    return sorted.slice(0, limit);
  }

  /**
   * Définit une valeur dans la mémoire contextuelle
   * @param {string} key - Clé de la valeur
   * @param {any} value - Valeur à définir
   */
  setContextualMemory(key, value) {
    this.workingMemory.contextual[key] = value;
  }

  /**
   * Récupère une valeur de la mémoire contextuelle
   * @param {string} key - Clé de la valeur
   * @param {any} defaultValue - Valeur par défaut si la clé n'existe pas
   * @returns {any} - Valeur récupérée
   */
  getContextualMemory(key, defaultValue = null) {
    return this.workingMemory.contextual[key] !== undefined ?
      this.workingMemory.contextual[key] : defaultValue;
  }

  /**
   * Synchronise la mémoire entre l'agent principal et l'agent de formation
   * @param {Object} options - Options de synchronisation
   * @returns {Promise<Object>} - Résultat de la synchronisation
   */
  async synchronizeAgentMemory(options = {}) {
    try {
      if (!this.options.thermalMemory) {
        return { success: false, error: 'Mémoire thermique non disponible' };
      }

      const mainAgent = this.getMainAgent();
      const trainingAgent = this.getTrainingAgent();

      if (!mainAgent) {
        return { success: false, error: 'Agent principal non disponible' };
      }

      if (!trainingAgent) {
        return { success: false, error: 'Agent de formation non disponible' };
      }

      this.log(`Synchronisation de la mémoire entre ${mainAgent.id} et ${trainingAgent.id}...`);

      // Récupérer les entrées de mémoire de l'agent principal
      const mainAgentMemories = this.options.thermalMemory.getAllEntries().filter(entry =>
        entry.metadata && entry.metadata.agentId === mainAgent.id
      );

      // Récupérer les entrées de mémoire de l'agent de formation
      const trainingAgentMemories = this.options.thermalMemory.getAllEntries().filter(entry =>
        entry.metadata && entry.metadata.agentId === trainingAgent.id
      );

      this.log(`Entrées de l'agent principal: ${mainAgentMemories.length}, Entrées de l'agent de formation: ${trainingAgentMemories.length}`);

      // Déterminer la direction de synchronisation
      const direction = options.direction || 'main_to_training';

      let syncedEntries = 0;
      let skippedEntries = 0;

      if (direction === 'main_to_training') {
        // Synchroniser de l'agent principal vers l'agent de formation
        for (const entry of mainAgentMemories) {
          // Vérifier si l'entrée existe déjà dans l'agent de formation
          const existsInTraining = trainingAgentMemories.some(trainingEntry =>
            trainingEntry.key === entry.key && trainingEntry.data === entry.data
          );

          if (!existsInTraining) {
            // Ajouter l'entrée à l'agent de formation
            const newEntry = { ...entry };
            newEntry.metadata = { ...newEntry.metadata, agentId: trainingAgent.id, syncedFrom: mainAgent.id };

            // Ajuster la température en fonction de la priorité de l'agent de formation
            if (trainingAgent.memoryPriority === 'low') {
              newEntry.temperature = Math.min(newEntry.temperature, 0.6);
            }

            this.options.thermalMemory.addExistingEntry(newEntry);
            syncedEntries++;
          } else {
            skippedEntries++;
          }
        }
      } else if (direction === 'training_to_main') {
        // Synchroniser de l'agent de formation vers l'agent principal
        for (const entry of trainingAgentMemories) {
          // Vérifier si l'entrée existe déjà dans l'agent principal
          const existsInMain = mainAgentMemories.some(mainEntry =>
            mainEntry.key === entry.key && mainEntry.data === entry.data
          );

          if (!existsInMain) {
            // Ajouter l'entrée à l'agent principal
            const newEntry = { ...entry };
            newEntry.metadata = { ...newEntry.metadata, agentId: mainAgent.id, syncedFrom: trainingAgent.id };

            // Ajuster la température en fonction de la priorité de l'agent principal
            if (mainAgent.memoryPriority === 'high') {
              newEntry.temperature = Math.max(newEntry.temperature, 0.7);
            }

            this.options.thermalMemory.addExistingEntry(newEntry);
            syncedEntries++;
          } else {
            skippedEntries++;
          }
        }
      } else if (direction === 'bidirectional') {
        // Synchronisation bidirectionnelle

        // De l'agent principal vers l'agent de formation
        for (const entry of mainAgentMemories) {
          const existsInTraining = trainingAgentMemories.some(trainingEntry =>
            trainingEntry.key === entry.key && trainingEntry.data === entry.data
          );

          if (!existsInTraining) {
            const newEntry = { ...entry };
            newEntry.metadata = { ...newEntry.metadata, agentId: trainingAgent.id, syncedFrom: mainAgent.id };

            if (trainingAgent.memoryPriority === 'low') {
              newEntry.temperature = Math.min(newEntry.temperature, 0.6);
            }

            this.options.thermalMemory.addExistingEntry(newEntry);
            syncedEntries++;
          } else {
            skippedEntries++;
          }
        }

        // De l'agent de formation vers l'agent principal
        for (const entry of trainingAgentMemories) {
          const existsInMain = mainAgentMemories.some(mainEntry =>
            mainEntry.key === entry.key && mainEntry.data === entry.data
          );

          if (!existsInMain) {
            const newEntry = { ...entry };
            newEntry.metadata = { ...newEntry.metadata, agentId: mainAgent.id, syncedFrom: trainingAgent.id };

            if (mainAgent.memoryPriority === 'high') {
              newEntry.temperature = Math.max(newEntry.temperature, 0.7);
            }

            this.options.thermalMemory.addExistingEntry(newEntry);
            syncedEntries++;
          } else {
            skippedEntries++;
          }
        }
      }

      // Exécuter un cycle de mémoire pour appliquer les changements
      this.options.thermalMemory.performMemoryCycle();

      this.log(`Synchronisation terminée: ${syncedEntries} entrées synchronisées, ${skippedEntries} entrées ignorées`);

      return {
        success: true,
        syncedEntries,
        skippedEntries,
        direction,
        mainAgent: mainAgent.id,
        trainingAgent: trainingAgent.id
      };
    } catch (error) {
      this.log(`Erreur lors de la synchronisation de la mémoire: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  /**
   * Récupère les statistiques du gestionnaire d'agents
   * @returns {Object} - Statistiques
   */
  getStats() {
    const mainAgent = this.getMainAgent();
    const trainingAgent = this.getTrainingAgent();

    return {
      ...this.stats,
      ollamaStatus: this.ollamaStatus,
      activeAgent: this.activeAgent ? this.activeAgent.id : null,
      mainAgent: mainAgent ? mainAgent.id : null,
      trainingAgent: trainingAgent ? trainingAgent.id : null,
      workingMemorySize: {
        shortTerm: this.workingMemory.shortTerm.length,
        contextual: Object.keys(this.workingMemory.contextual).length,
        sessionData: Object.keys(this.workingMemory.sessionData).length
      }
    };
  }

  /**
   * Analyse l'importance d'une réponse pour la mémoire thermique
   * @param {string} responseContent - Contenu de la réponse
   * @param {string} originalMessage - Message original de l'utilisateur
   * @returns {number} - Score d'importance (0-1)
   */
  analyzeResponseImportance(responseContent, originalMessage) {
    let importance = 0.5; // Base

    // Augmenter l'importance pour les informations factuelles
    if (responseContent.includes('est') || responseContent.includes('sont') ||
        responseContent.includes('fait') || responseContent.includes('vérité')) {
      importance += 0.2;
    }

    // Augmenter pour les dates et chiffres
    if (/\d{4}/.test(responseContent) || /\d+/.test(responseContent)) {
      importance += 0.1;
    }

    // Augmenter pour les définitions
    if (responseContent.includes('définition') || responseContent.includes('signifie') ||
        responseContent.includes('c\'est') || responseContent.includes('désigne')) {
      importance += 0.2;
    }

    // Augmenter pour les explications détaillées
    if (responseContent.length > 200) {
      importance += 0.1;
    }

    // Augmenter pour les questions importantes
    const lowerMessage = originalMessage.toLowerCase();
    if (lowerMessage.includes('important') || lowerMessage.includes('crucial') ||
        lowerMessage.includes('essentiel') || lowerMessage.includes('capital')) {
      importance += 0.2;
    }

    return Math.min(1.0, importance);
  }

  /**
   * Extrait les informations factuelles d'une réponse
   * @param {string} responseContent - Contenu de la réponse
   * @returns {Array} - Liste des faits extraits
   */
  extractFactualInformation(responseContent) {
    const facts = [];

    // Extraire les phrases avec des faits (contiennent "est", "sont", etc.)
    const sentences = responseContent.split(/[.!?]+/);

    sentences.forEach(sentence => {
      const trimmed = sentence.trim();
      if (trimmed.length > 10) {
        // Détecter les faits
        if (trimmed.includes(' est ') || trimmed.includes(' sont ') ||
            trimmed.includes(' était ') || trimmed.includes(' étaient ') ||
            trimmed.includes(' a ') || trimmed.includes(' ont ')) {
          facts.push(trimmed);
        }

        // Détecter les dates
        if (/\d{4}/.test(trimmed) && (trimmed.includes('en ') || trimmed.includes('depuis '))) {
          facts.push(trimmed);
        }

        // Détecter les définitions
        if (trimmed.includes('définit') || trimmed.includes('signifie') ||
            trimmed.includes('désigne') || trimmed.includes('représente')) {
          facts.push(trimmed);
        }
      }
    });

    return facts.slice(0, 5); // Limiter à 5 faits maximum
  }

  /**
   * Journalise un message
   * @param {string} message - Message à journaliser
   * @param {string} level - Niveau de journalisation (log, warn, error)
   */
  log(message, level = 'log') {
    if (!this.options.debug && level === 'log') {
      return;
    }

    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [AgentManager] ${message}`;

    switch (level) {
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'error':
        console.error(formattedMessage);
        break;
      default:
        console.log(formattedMessage);
    }

    // Émettre un événement de journalisation
    this.emit('log', { timestamp, level, message });
  }
}

module.exports = AgentManager;
