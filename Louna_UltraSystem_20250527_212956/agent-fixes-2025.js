/**
 * Corrections pour l'agent principal Vision Ultra
 * Intégration du système 2025, accès Internet MCP et optimisations
 */

const axios = require('axios');

class AgentFixes2025 {
    constructor() {
        this.calendar2025 = null;
        this.enhancedAgent = null;
        this.mcpController = null;
        this.initialized = false;
    }

    /**
     * Initialise les corrections avec les systèmes existants
     */
    async initialize() {
        try {
            console.log('🔧 Initialisation des corrections agent 2025...');

            // Récupérer les références globales
            this.calendar2025 = global.calendar2025;
            this.enhancedAgent = global.enhancedAgent;
            this.mcpController = global.mcpController;

            if (!this.calendar2025) {
                console.log('⚠️ Système calendrier 2025 non trouvé');
                return false;
            }

            if (!this.enhancedAgent) {
                console.log('⚠️ Agent amélioré non trouvé');
                return false;
            }

            console.log('✅ Références système récupérées');
            this.initialized = true;
            return true;
        } catch (error) {
            console.error('❌ Erreur initialisation corrections:', error);
            return false;
        }
    }

    /**
     * Corrige la fonction sendMessageToOllama pour intégrer 2025 et MCP
     */
    patchSendMessageToOllama(agentManager) {
        if (!this.initialized) {
            console.log('⚠️ Corrections non initialisées');
            return false;
        }

        console.log('🔧 Application du patch sendMessageToOllama...');

        // Sauvegarder la fonction originale
        const originalSendMessageToOllama = agentManager.sendMessageToOllama.bind(agentManager);

        // Remplacer par la version corrigée
        agentManager.sendMessageToOllama = async (message, history = [], options = {}) => {
            try {
                console.log('🚀 [PATCH] Envoi message à Ollama avec corrections 2025...');

                // 1. Enrichir le message avec le contexte 2025
                const enrichedMessage = this.enrichMessageWith2025Context(message);

                // 2. Ajouter les résultats MCP si disponibles
                let mcpResults = null;
                if (this.shouldPerformInternetSearch(enrichedMessage)) {
                    mcpResults = await this.performMCPInternetSearch(enrichedMessage);
                }

                // 3. Construire le prompt optimisé avec toutes les informations
                const optimizedPrompt = this.buildOptimizedPrompt(enrichedMessage, history, mcpResults);

                // 4. Appeler Ollama avec le prompt enrichi
                const response = await this.callOllamaWithEnhancedPrompt(
                    agentManager,
                    optimizedPrompt,
                    options
                );

                console.log('✅ [PATCH] Réponse Ollama avec corrections reçue');
                return response;

            } catch (error) {
                console.error('❌ [PATCH] Erreur dans sendMessageToOllama corrigé:', error);
                // Fallback vers la fonction originale
                return await originalSendMessageToOllama(message, history, options);
            }
        };

        console.log('✅ Patch sendMessageToOllama appliqué');
        return true;
    }

    /**
     * Enrichit le message avec le contexte 2025
     */
    enrichMessageWith2025Context(message) {
        if (!this.calendar2025) return message;

        try {
            const dateContext = this.calendar2025.getFormattedDate();
            const searchContext = this.calendar2025.getSearchContext();

            // Ajouter le contexte 2025 au début du message
            const enrichedMessage = `[CONTEXTE TEMPOREL 2025]
Date actuelle: ${dateContext}
Année: 2025
Instructions: ${searchContext.instruction}

[MESSAGE UTILISATEUR]
${message}

[INSTRUCTIONS AGENT]
Tu es Vision Ultra, l'agent principal de Louna. Nous sommes en 2025. 
Utilise cette information dans tes réponses et recherches.
Si l'utilisateur demande l'année ou des informations actuelles, mentionne explicitement que nous sommes en 2025.`;

            console.log('📅 Message enrichi avec contexte 2025');
            return enrichedMessage;
        } catch (error) {
            console.error('❌ Erreur enrichissement 2025:', error);
            return message;
        }
    }

    /**
     * Détermine si une recherche Internet est nécessaire
     */
    shouldPerformInternetSearch(message) {
        const searchKeywords = [
            'recherche', 'actualité', 'internet', 'web', 'google',
            'information', 'dernière', 'récent', 'nouveau', 'technologie',
            '2025', 'aujourd\'hui', 'maintenant', 'actuel'
        ];

        return searchKeywords.some(keyword => 
            message.toLowerCase().includes(keyword.toLowerCase())
        );
    }

    /**
     * Effectue une recherche Internet via MCP
     */
    async performMCPInternetSearch(message) {
        try {
            console.log('🔍 Recherche Internet MCP...');

            if (!this.enhancedAgent || !this.enhancedAgent.searchInternet) {
                console.log('⚠️ Fonction de recherche MCP non disponible');
                return null;
            }

            // Extraire les termes de recherche
            const searchQuery = this.extractSearchQuery(message);
            console.log(`🔍 Requête de recherche: "${searchQuery}"`);

            // Effectuer la recherche via l'agent amélioré
            const searchResults = await this.enhancedAgent.searchInternet(searchQuery);

            if (searchResults && searchResults.success) {
                console.log('✅ Résultats de recherche MCP obtenus');
                return {
                    query: searchQuery,
                    results: searchResults.results || searchResults.data,
                    source: 'MCP'
                };
            } else {
                console.log('⚠️ Recherche MCP échouée');
                return null;
            }
        } catch (error) {
            console.error('❌ Erreur recherche MCP:', error);
            return null;
        }
    }

    /**
     * Extrait la requête de recherche du message
     */
    extractSearchQuery(message) {
        // Nettoyer le message pour extraire les termes de recherche
        let query = message
            .replace(/\[.*?\]/g, '') // Supprimer les balises de contexte
            .replace(/recherche|actualité|internet|web|google/gi, '') // Supprimer les mots-clés de recherche
            .trim();

        // Si la requête est vide, utiliser des termes par défaut
        if (!query || query.length < 3) {
            query = 'actualités technologie 2025';
        }

        // Ajouter automatiquement 2025 si pas présent
        if (!query.includes('2025')) {
            query += ' 2025';
        }

        return query;
    }

    /**
     * Construit un prompt optimisé avec toutes les informations
     */
    buildOptimizedPrompt(message, history, mcpResults) {
        let prompt = '';

        // 1. Contexte système
        prompt += `Tu es Vision Ultra, l'agent principal de Louna. Nous sommes en 2025.
Réponds en français, sois précis et utile.

`;

        // 2. Historique limité
        if (history && history.length > 0) {
            prompt += 'Contexte récent:\n';
            const limitedHistory = history.slice(-2); // 2 derniers échanges
            limitedHistory.forEach(item => {
                if (item.role === 'user') {
                    prompt += `Utilisateur: ${item.content.substring(0, 150)}\n`;
                } else if (item.role === 'assistant') {
                    prompt += `Assistant: ${item.content.substring(0, 150)}\n`;
                }
            });
            prompt += '\n';
        }

        // 3. Résultats de recherche Internet si disponibles
        if (mcpResults && mcpResults.results) {
            prompt += `Informations Internet récentes (${mcpResults.source}):\n`;
            if (typeof mcpResults.results === 'string') {
                prompt += mcpResults.results.substring(0, 500) + '\n\n';
            } else if (Array.isArray(mcpResults.results)) {
                mcpResults.results.slice(0, 3).forEach((result, index) => {
                    prompt += `${index + 1}. ${result.title || result.snippet || result}\n`;
                });
                prompt += '\n';
            }
        }

        // 4. Message principal
        prompt += `Question actuelle: ${message}\n\n`;

        // 5. Instructions finales
        prompt += `Réponds de manière complète et utile. Si tu as utilisé des informations Internet, mentionne-le.
Si l'utilisateur demande l'année, confirme que nous sommes en 2025.`;

        return prompt;
    }

    /**
     * Appelle Ollama avec le prompt enrichi
     */
    async callOllamaWithEnhancedPrompt(agentManager, prompt, options) {
        try {
            const OLLAMA_API_URL = 'http://localhost:11434/api';
            const timeout = options.timeout || 15000; // Timeout plus long pour les recherches

            const requestData = {
                model: agentManager.activeAgent.model,
                prompt: prompt,
                stream: false,
                options: {
                    temperature: options.temperature || 0.7,
                    num_predict: options.maxTokens || 800, // Plus de tokens pour les réponses enrichies
                    top_k: 20,
                    top_p: 0.9,
                    repeat_penalty: 1.1
                }
            };

            console.log('📡 Appel Ollama avec prompt enrichi...');
            const startTime = Date.now();

            const response = await axios.post(`${OLLAMA_API_URL}/generate`, requestData, {
                timeout: timeout,
                headers: { 'Content-Type': 'application/json' }
            });

            const responseTime = Date.now() - startTime;
            console.log(`✅ Réponse Ollama enrichie reçue en ${responseTime}ms`);

            return {
                success: true,
                message: {
                    role: 'assistant',
                    content: response.data.response
                },
                usage: {
                    prompt_tokens: response.data.prompt_eval_count || 0,
                    completion_tokens: response.data.eval_count || 0,
                    total_tokens: (response.data.prompt_eval_count || 0) + (response.data.eval_count || 0)
                },
                responseTime: responseTime,
                enhanced: true,
                source: 'ollama-enhanced'
            };
        } catch (error) {
            console.error('❌ Erreur appel Ollama enrichi:', error);
            throw error;
        }
    }

    /**
     * Applique toutes les corrections
     */
    async applyAllFixes(agentManager) {
        try {
            console.log('🔧 Application de toutes les corrections agent...');

            // 1. Initialiser
            const initialized = await this.initialize();
            if (!initialized) {
                console.log('❌ Impossible d\'initialiser les corrections');
                return false;
            }

            // 2. Appliquer le patch principal
            const patched = this.patchSendMessageToOllama(agentManager);
            if (!patched) {
                console.log('❌ Impossible d\'appliquer le patch principal');
                return false;
            }

            // 3. Optimiser les timeouts
            this.optimizeTimeouts(agentManager);

            console.log('✅ Toutes les corrections appliquées avec succès');
            return true;
        } catch (error) {
            console.error('❌ Erreur application corrections:', error);
            return false;
        }
    }

    /**
     * Optimise les timeouts pour de meilleures performances
     */
    optimizeTimeouts(agentManager) {
        // Augmenter les timeouts pour permettre les recherches Internet
        if (agentManager.options) {
            agentManager.options.defaultTimeout = 15000; // 15 secondes
            agentManager.options.maxTimeout = 30000; // 30 secondes max
        }
        console.log('⚡ Timeouts optimisés pour les recherches Internet');
    }
}

// Exporter la classe
module.exports = AgentFixes2025;
