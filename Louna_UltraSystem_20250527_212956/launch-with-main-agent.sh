#!/bin/bash

# Script pour démarrer l'application avec l'agent principal
# Ce script démarre <PERSON>, vérifie que l'agent principal est disponible,
# puis démarre l'application

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher un message d'information
info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Fonction pour afficher un message de succès
success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Fonction pour afficher un avertissement
warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Fonction pour afficher une erreur
error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
    error "Ollama n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    error "Node.js n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    error "npm n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Démarrer Ollama en arrière-plan s'il n'est pas déjà en cours d'exécution
info "Vérification de l'état d'Ollama..."
if ! curl -s http://localhost:11434/api/version &> /dev/null; then
    info "Démarrage d'Ollama..."
    ollama serve &
    
    # Attendre que Ollama démarre
    max_attempts=10
    attempt=0
    while ! curl -s http://localhost:11434/api/version &> /dev/null; do
        attempt=$((attempt + 1))
        if [ $attempt -ge $max_attempts ]; then
            error "Impossible de démarrer Ollama après $max_attempts tentatives."
            exit 1
        fi
        info "Attente du démarrage d'Ollama (tentative $attempt/$max_attempts)..."
        sleep 2
    done
    
    success "Ollama démarré avec succès."
else
    success "Ollama est déjà en cours d'exécution."
fi

# Vérifier si le modèle Claude-like est disponible
info "Vérification de la disponibilité du modèle Claude-like..."
if ! ollama list | grep -q "incept5/llama3.1-claude"; then
    warning "Le modèle Claude-like n'est pas disponible. Installation en cours..."
    ollama pull incept5/llama3.1-claude:latest
    
    if [ $? -ne 0 ]; then
        error "Impossible d'installer le modèle Claude-like."
        exit 1
    fi
    
    success "Modèle Claude-like installé avec succès."
else
    success "Modèle Claude-like déjà disponible."
fi

# Démarrer l'application
info "Démarrage de l'application..."
node server.js &
SERVER_PID=$!

# Attendre que le serveur démarre
max_attempts=10
attempt=0
while ! curl -s http://localhost:3004/ &> /dev/null; do
    attempt=$((attempt + 1))
    if [ $attempt -ge $max_attempts ]; then
        error "Impossible de démarrer le serveur après $max_attempts tentatives."
        kill $SERVER_PID
        exit 1
    fi
    info "Attente du démarrage du serveur (tentative $attempt/$max_attempts)..."
    sleep 2
done

success "Serveur démarré avec succès."

# Démarrer l'interface Electron
info "Démarrage de l'interface Electron..."
npm run electron

# Si l'interface Electron se ferme, arrêter le serveur
info "Arrêt du serveur..."
kill $SERVER_PID

success "Application arrêtée avec succès."
