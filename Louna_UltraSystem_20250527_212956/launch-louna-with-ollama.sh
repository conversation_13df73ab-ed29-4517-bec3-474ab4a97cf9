#!/bin/bash

# Script pour lancer l'application Louna avec Ollama et configurer l'agent <PERSON>

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "██╗      ██████╗ ██╗   ██╗███╗   ██╗ █████╗ "
echo "██║     ██╔═══██╗██║   ██║████╗  ██║██╔══██╗"
echo "██║     ██║   ██║██║   ██║██╔██╗ ██║███████║"
echo "██║     ██║   ██║██║   ██║██║╚██╗██║██╔══██║"
echo "███████╗╚██████╔╝╚██████╔╝██║ ╚████║██║  ██║"
echo "╚══════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Démarrage de l'application Louna avec Ollama${NC}"
echo ""

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
  print_error "Node.js n'est pas installé. Veuillez installer Node.js pour exécuter cette application."
  echo "Vous pouvez télécharger Node.js depuis https://nodejs.org/"
  exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
  print_error "npm n'est pas installé. Veuillez installer npm pour exécuter cette application."
  echo "npm est généralement installé avec Node.js. Veuillez réinstaller Node.js."
  exit 1
fi

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
  print_error "Ollama n'est pas installé. Veuillez l'installer depuis https://ollama.com/download"
  exit 1
fi

# Vérifier si les dépendances sont installées
if [ ! -d "node_modules" ]; then
  print_message "Installation des dépendances..."
  npm install
fi

# Vérifier si les dossiers de données existent
if [ ! -d "data" ]; then
  print_message "Création du dossier de données..."
  mkdir -p data/memory
  mkdir -p data/accelerators
fi

# Vérifier si Ollama est en cours d'exécution
if ! curl -s http://localhost:11434/api/version &> /dev/null; then
  print_message "Démarrage d'Ollama..."
  ollama serve &
  OLLAMA_PID=$!

  # Attendre que Ollama démarre
  print_message "Attente du démarrage d'Ollama..."
  for i in {1..10}; do
    if curl -s http://localhost:11434/api/version &> /dev/null; then
      print_success "Ollama est démarré!"
      break
    fi

    if [ $i -eq 10 ]; then
      print_error "Impossible de démarrer Ollama. Veuillez vérifier l'installation."
      exit 1
    fi

    echo -n "."
    sleep 1
  done
  echo ""
else
  print_success "Ollama est déjà en cours d'exécution."
fi

# Vérifier si le modèle Claude est disponible
print_message "Vérification si le modèle Claude est disponible..."
if ! curl -s http://localhost:11434/api/tags | grep -q "incept5/llama3.1-claude"; then
  print_message "Le modèle Claude n'est pas disponible. Voulez-vous le télécharger ? (o/n)"
  read -r response
  if [[ "$response" =~ ^([oO][uU][iI]|[oO])$ ]]; then
    print_message "Téléchargement du modèle Claude..."
    ollama pull incept5/llama3.1-claude:latest
  else
    print_message "Vous devrez télécharger le modèle manuellement depuis l'interface."
  fi
else
  print_success "Le modèle Claude est disponible."
fi

# Vérifier si le modèle Llama 3 est disponible pour l'agent de formation
print_message "Vérification si le modèle Llama 3 est disponible pour l'agent de formation..."
if ! curl -s http://localhost:11434/api/tags | grep -q "llama3:8b"; then
  print_message "Le modèle Llama 3 n'est pas disponible. Voulez-vous le télécharger ? (o/n)"
  read -r response
  if [[ "$response" =~ ^([oO][uU][iI]|[oO])$ ]]; then
    print_message "Téléchargement du modèle Llama 3..."
    ollama pull llama3:8b

    # Vérifier si le téléchargement a réussi
    if ! curl -s http://localhost:11434/api/tags | grep -q "llama3:8b"; then
      print_error "Échec du téléchargement du modèle Llama 3. L'application fonctionnera sans agent de formation."
      LLAMA3_AVAILABLE=false
    else
      print_success "Le modèle Llama 3 a été téléchargé avec succès."
      LLAMA3_AVAILABLE=true
    fi
  else
    print_message "Vous devrez télécharger le modèle manuellement depuis l'interface."
    LLAMA3_AVAILABLE=false
  fi
else
  print_success "Le modèle Llama 3 est disponible."
  LLAMA3_AVAILABLE=true
fi

# Configurer l'agent Claude comme agent principal
print_message "Configuration de l'agent Claude comme agent principal..."

# Créer le fichier de configuration de l'agent Claude
cat > data/config/agent_claude.json <<EOF
{
  "id": "agent_claude",
  "name": "Claude (4GB)",
  "type": "ollama",
  "model": "incept5/llama3.1-claude:latest",
  "description": "Agent principal basé sur le modèle Claude. Ce modèle offre des performances similaires à Claude avec une taille d'environ 4 Go.",
  "temperature": 0.7,
  "maxTokens": 2000,
  "createdAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
  "updatedAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")"
}
EOF

# Configurer les agents
print_message "Configuration des agents..."

# Mettre à jour le fichier agents.json pour inclure l'agent Claude
if [ -f "data/config/agents.json" ]; then
  # Vérifier si l'agent Claude existe déjà
  if ! grep -q "agent_claude" data/config/agents.json; then
    # Ajouter l'agent Claude au fichier agents.json
    TMP_FILE=$(mktemp)
    jq '.agents += {"agent_claude": {"id": "agent_claude", "name": "Claude (4GB)", "type": "ollama", "model": "incept5/llama3.1-claude:latest", "description": "Agent principal basé sur le modèle Claude. Ce modèle offre des performances similaires à Claude avec une taille d\'environ 4 Go.", "temperature": 0.7, "maxTokens": 2000, "createdAt": "'$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")'", "updatedAt": "'$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")'"}}' data/config/agents.json > "$TMP_FILE"
    mv "$TMP_FILE" data/config/agents.json
  fi

  # Définir l'agent Claude comme agent par défaut
  TMP_FILE=$(mktemp)
  jq '.defaultAgent = "agent_claude"' data/config/agents.json > "$TMP_FILE"
  mv "$TMP_FILE" data/config/agents.json

  # Ajouter l'agent de formation si le modèle Llama 3 est disponible
  if [ "$LLAMA3_AVAILABLE" = true ]; then
    print_message "Configuration de l'agent de formation..."

    # Créer le fichier de configuration de l'agent de formation
    cat > data/config/agent_training.json <<EOF
{
  "id": "agent_training",
  "name": "Agent de Formation",
  "type": "ollama",
  "model": "llama3:8b",
  "description": "Agent de formation basé sur le modèle Llama 3 (8B). Cet agent est optimisé pour l'apprentissage et la formation de la mémoire thermique.",
  "temperature": 0.5,
  "maxTokens": 2000,
  "createdAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
  "updatedAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")"
}
EOF

    # Vérifier si l'agent de formation existe déjà
    if ! grep -q "agent_training" data/config/agents.json; then
      # Ajouter l'agent de formation au fichier agents.json
      TMP_FILE=$(mktemp)
      jq '.agents += {"agent_training": {"id": "agent_training", "name": "Agent de Formation", "type": "ollama", "model": "llama3:8b", "description": "Agent de formation basé sur le modèle Llama 3 (8B). Cet agent est optimisé pour l\'apprentissage et la formation de la mémoire thermique.", "temperature": 0.5, "maxTokens": 2000, "createdAt": "'$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")'", "updatedAt": "'$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")'"}}' data/config/agents.json > "$TMP_FILE"
      mv "$TMP_FILE" data/config/agents.json
    fi
  else
    print_message "L'agent de formation ne sera pas configuré car le modèle Llama 3 n'est pas disponible."
  fi
else
  # Créer le fichier agents.json s'il n'existe pas
  if [ "$LLAMA3_AVAILABLE" = true ]; then
    # Inclure l'agent de formation
    cat > data/config/agents.json <<EOF
{
  "defaultAgent": "agent_claude",
  "agents": {
    "agent_claude": {
      "id": "agent_claude",
      "name": "Claude (4GB)",
      "type": "ollama",
      "model": "incept5/llama3.1-claude:latest",
      "description": "Agent principal basé sur le modèle Claude. Ce modèle offre des performances similaires à Claude avec une taille d'environ 4 Go.",
      "temperature": 0.7,
      "maxTokens": 2000,
      "createdAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
      "updatedAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")"
    },
    "agent_training": {
      "id": "agent_training",
      "name": "Agent de Formation",
      "type": "ollama",
      "model": "llama3:8b",
      "description": "Agent de formation basé sur le modèle Llama 3 (8B). Cet agent est optimisé pour l'apprentissage et la formation de la mémoire thermique.",
      "temperature": 0.5,
      "maxTokens": 2000,
      "createdAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
      "updatedAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")"
    }
  }
}
EOF
  else
    # Ne pas inclure l'agent de formation
    cat > data/config/agents.json <<EOF
{
  "defaultAgent": "agent_claude",
  "agents": {
    "agent_claude": {
      "id": "agent_claude",
      "name": "Claude (4GB)",
      "type": "ollama",
      "model": "incept5/llama3.1-claude:latest",
      "description": "Agent principal basé sur le modèle Claude. Ce modèle offre des performances similaires à Claude avec une taille d'environ 4 Go.",
      "temperature": 0.7,
      "maxTokens": 2000,
      "createdAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
      "updatedAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")"
    }
  }
}
EOF
  fi
fi

# Mettre à jour le fichier default-agent.json
cat > data/config/default-agent.json <<EOF
{
  "id": "agent_claude",
  "name": "Claude (4GB)",
  "type": "ollama",
  "model": "incept5/llama3.1-claude:latest",
  "description": "Agent principal basé sur le modèle Claude. Ce modèle offre des performances similaires à Claude avec une taille d'environ 4 Go.",
  "temperature": 0.7,
  "maxTokens": 2000,
  "createdAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
  "updatedAt": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")"
}
EOF

print_success "Agent Claude configuré comme agent principal."

# Lancer l'application avec Electron
print_message "Lancement de l'application Louna..."
npm run electron

# Si l'application se ferme, arrêter Ollama si nous l'avons démarré
if [ -n "$OLLAMA_PID" ]; then
  print_message "Arrêt d'Ollama..."
  kill $OLLAMA_PID
fi

print_message "Application fermée."
