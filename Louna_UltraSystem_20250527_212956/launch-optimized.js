#!/usr/bin/env node

/**
 * Script de lancement optimisé pour Louna Electron
 * Évite les problèmes de mémoire et optimise les performances
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Lancement optimisé de Louna Electron...');

// Nettoyage des processus existants
console.log('🧹 Nettoyage des processus existants...');
const cleanup = spawn('pkill', ['-f', 'electron'], { stdio: 'inherit' });

cleanup.on('close', () => {
    setTimeout(() => {
        // Configuration optimisée pour éviter les problèmes de mémoire
        const electronArgs = [
            '.'
        ];

        const electronProcess = spawn('npx', ['electron', ...electronArgs], {
            cwd: process.cwd(),
            stdio: 'inherit',
            env: {
                ...process.env,
                NODE_OPTIONS: '--max-old-space-size=8192',
                ELECTRON_DISABLE_SECURITY_WARNINGS: 'true'
            }
        });

        electronProcess.on('error', (error) => {
            console.error('❌ Erreur lors du lancement:', error);
        });

        electronProcess.on('close', (code) => {
            console.log(`📱 Application fermée avec le code: ${code}`);
        });

        // Gestion des signaux pour un arrêt propre
        process.on('SIGINT', () => {
            console.log('🛑 Arrêt de l\'application...');
            electronProcess.kill('SIGTERM');
            process.exit(0);
        });

        console.log('✅ Application Louna Electron lancée avec optimisations mémoire');
        console.log('🌐 Interface disponible sur: http://localhost:3005');
        console.log('📊 Monitoring QI & Neurones: http://localhost:3005/qi-neuron-monitor.html');

    }, 2000); // Attendre 2 secondes après le nettoyage
});
