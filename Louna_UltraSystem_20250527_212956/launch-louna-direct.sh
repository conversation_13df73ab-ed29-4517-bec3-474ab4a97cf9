#!/bin/bash

echo "🚀 LANCEMENT DIRECT DE L'APPLICATION LOUNA"
echo "=========================================="

# Aller dans le bon répertoire
cd /Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933

echo "📁 Répertoire: $(pwd)"

# Vérifier que les fichiers existent
if [ ! -f "electron-main.js" ]; then
    echo "❌ Erreur: electron-main.js non trouvé"
    exit 1
fi

if [ ! -f "server.js" ]; then
    echo "❌ Erreur: server.js non trouvé"
    exit 1
fi

echo "✅ Fichiers trouvés"

# Tuer les processus existants
echo "🔄 Arrêt des processus existants..."
pkill -f "electron" 2>/dev/null || true
pkill -f "node.*server.js" 2>/dev/null || true
sleep 2

# Lancer l'application
echo "🚀 Lancement de Louna..."
echo "📱 L'application va s'ouvrir dans quelques secondes..."

# Lancer avec des logs détaillés
ELECTRON_ENABLE_LOGGING=1 npm run electron

echo "✅ Application lancée !"
