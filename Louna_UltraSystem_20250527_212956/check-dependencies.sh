#!/bin/bash

# Script pour vérifier et installer les dépendances nécessaires pour l'application Louna

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Afficher un message de bienvenue
clear
echo -e "${PURPLE}"
echo "██╗      ██████╗ ██╗   ██╗███╗   ██╗ █████╗ "
echo "██║     ██╔═══██╗██║   ██║████╗  ██║██╔══██╗"
echo "██║     ██║   ██║██║   ██║██╔██╗ ██║███████║"
echo "██║     ██║   ██║██║   ██║██║╚██╗██║██╔══██║"
echo "███████╗╚██████╔╝╚██████╔╝██║ ╚████║██║  ██║"
echo "╚══════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Vérification des dépendances pour l'application Louna${NC}"
echo ""

# Vérifier si Node.js est installé
print_message "Vérification de Node.js..."
if ! command -v node &> /dev/null; then
  print_error "Node.js n'est pas installé."
  print_message "Voulez-vous installer Node.js ? (o/n)"
  read -r response
  if [[ "$response" =~ ^([oO][uU][iI]|[oO])$ ]]; then
    print_message "Installation de Node.js..."
    if command -v brew &> /dev/null; then
      brew install node
    else
      print_message "Homebrew n'est pas installé. Installation de Homebrew..."
      /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
      print_message "Installation de Node.js avec Homebrew..."
      brew install node
    fi

    if ! command -v node &> /dev/null; then
      print_error "Échec de l'installation de Node.js. Veuillez l'installer manuellement depuis https://nodejs.org/"
      exit 1
    else
      print_success "Node.js a été installé avec succès."
    fi
  else
    print_error "Node.js est requis pour exécuter l'application Louna. Veuillez l'installer manuellement depuis https://nodejs.org/"
    exit 1
  fi
else
  NODE_VERSION=$(node -v)
  print_success "Node.js est installé (version $NODE_VERSION)."
fi

# Vérifier si npm est installé
print_message "Vérification de npm..."
if ! command -v npm &> /dev/null; then
  print_error "npm n'est pas installé."
  print_message "npm est généralement installé avec Node.js. Veuillez réinstaller Node.js."
  exit 1
else
  NPM_VERSION=$(npm -v)
  print_success "npm est installé (version $NPM_VERSION)."
fi

# Vérifier si Ollama est installé
print_message "Vérification d'Ollama..."
if ! command -v ollama &> /dev/null; then
  print_error "Ollama n'est pas installé."
  print_message "Voulez-vous installer Ollama ? (o/n)"
  read -r response
  if [[ "$response" =~ ^([oO][uU][iI]|[oO])$ ]]; then
    print_message "Installation d'Ollama..."
    if command -v brew &> /dev/null; then
      brew install ollama
    else
      print_message "Téléchargement d'Ollama depuis le site officiel..."
      curl -fsSL https://ollama.com/download/ollama-darwin-amd64 -o /tmp/ollama
      chmod +x /tmp/ollama
      sudo mv /tmp/ollama /usr/local/bin/ollama
    fi

    if ! command -v ollama &> /dev/null; then
      print_error "Échec de l'installation d'Ollama. Veuillez l'installer manuellement depuis https://ollama.com/download"
      exit 1
    else
      print_success "Ollama a été installé avec succès."
    fi
  else
    print_error "Ollama est requis pour exécuter l'application Louna avec les modèles locaux. Veuillez l'installer manuellement depuis https://ollama.com/download"
    exit 1
  fi
else
  print_success "Ollama est installé."
fi

# Vérifier si Electron est installé
print_message "Vérification d'Electron..."
if ! npm list -g electron &> /dev/null; then
  print_message "Electron n'est pas installé globalement. Installation en cours..."
  npm install -g electron

  if ! npm list -g electron &> /dev/null; then
    print_error "Échec de l'installation d'Electron. Veuillez l'installer manuellement avec 'npm install -g electron'."
    exit 1
  else
    print_success "Electron a été installé avec succès."
  fi
else
  ELECTRON_VERSION=$(npm list -g electron | grep electron | awk -F@ '{print $2}')
  print_success "Electron est installé (version $ELECTRON_VERSION)."
fi

# Vérifier si jq est installé (pour la manipulation de fichiers JSON)
print_message "Vérification de jq..."
if ! command -v jq &> /dev/null; then
  print_message "jq n'est pas installé. Installation en cours..."
  if command -v brew &> /dev/null; then
    brew install jq
  else
    print_error "Homebrew n'est pas installé. Impossible d'installer jq automatiquement."
    print_message "Veuillez installer jq manuellement ou installer Homebrew puis exécuter 'brew install jq'."
    exit 1
  fi

  if ! command -v jq &> /dev/null; then
    print_error "Échec de l'installation de jq. Veuillez l'installer manuellement avec 'brew install jq'."
    exit 1
  else
    print_success "jq a été installé avec succès."
  fi
else
  JQ_VERSION=$(jq --version)
  print_success "jq est installé (version $JQ_VERSION)."
fi

# Vérifier si les dépendances npm sont installées
print_message "Vérification des dépendances npm..."
if [ ! -d "node_modules" ]; then
  print_message "Installation des dépendances npm..."
  npm install

  if [ ! -d "node_modules" ]; then
    print_error "Échec de l'installation des dépendances npm. Veuillez les installer manuellement avec 'npm install'."
    exit 1
  else
    print_success "Les dépendances npm ont été installées avec succès."
  fi
else
  print_success "Les dépendances npm sont déjà installées."
fi

# Vérifier si les dossiers de données existent
print_message "Vérification des dossiers de données..."
if [ ! -d "data" ]; then
  print_message "Création des dossiers de données..."
  mkdir -p data/memory
  mkdir -p data/accelerators
  mkdir -p data/config
  print_success "Les dossiers de données ont été créés avec succès."
else
  print_success "Les dossiers de données existent déjà."
fi

# Vérifier si Ollama est en cours d'exécution
print_message "Vérification si Ollama est en cours d'exécution..."
if ! curl -s http://localhost:11434/api/version &> /dev/null; then
  print_message "Démarrage d'Ollama..."
  ollama serve &
  OLLAMA_PID=$!

  # Attendre que Ollama démarre
  print_message "Attente du démarrage d'Ollama..."
  for i in {1..10}; do
    if curl -s http://localhost:11434/api/version &> /dev/null; then
      print_success "Ollama est démarré!"
      break
    fi

    if [ $i -eq 10 ]; then
      print_error "Impossible de démarrer Ollama. Veuillez vérifier l'installation."
      exit 1
    fi

    echo -n "."
    sleep 1
  done
  echo ""
else
  print_success "Ollama est déjà en cours d'exécution."
fi

# Vérifier si le modèle Claude est disponible
print_message "Vérification si le modèle Claude est disponible..."
if ! curl -s http://localhost:11434/api/tags | grep -q "incept5/llama3.1-claude"; then
  print_message "Le modèle Claude n'est pas disponible. Voulez-vous le télécharger ? (o/n)"
  read -r response
  if [[ "$response" =~ ^([oO][uU][iI]|[oO])$ ]]; then
    print_message "Téléchargement du modèle Claude..."
    ollama pull incept5/llama3.1-claude:latest

    if ! curl -s http://localhost:11434/api/tags | grep -q "incept5/llama3.1-claude"; then
      print_error "Échec du téléchargement du modèle Claude. Veuillez le télécharger manuellement avec 'ollama pull incept5/llama3.1-claude:latest'."
    else
      print_success "Le modèle Claude a été téléchargé avec succès."
    fi
  else
    print_message "Vous devrez télécharger le modèle manuellement depuis l'interface ou avec 'ollama pull incept5/llama3.1-claude:latest'."
  fi
else
  print_success "Le modèle Claude est disponible."
fi

# Vérifier si le modèle Llama 3 est disponible pour l'agent de formation
print_message "Vérification si le modèle Llama 3 est disponible pour l'agent de formation..."
if ! curl -s http://localhost:11434/api/tags | grep -q "llama3:8b"; then
  print_message "Le modèle Llama 3 n'est pas disponible. Voulez-vous le télécharger ? (o/n)"
  read -r response
  if [[ "$response" =~ ^([oO][uU][iI]|[oO])$ ]]; then
    print_message "Téléchargement du modèle Llama 3..."
    ollama pull llama3:8b

    if ! curl -s http://localhost:11434/api/tags | grep -q "llama3:8b"; then
      print_error "Échec du téléchargement du modèle Llama 3. Veuillez le télécharger manuellement avec 'ollama pull llama3:8b'."
    else
      print_success "Le modèle Llama 3 a été téléchargé avec succès."
    fi
  else
    print_message "Vous devrez télécharger le modèle manuellement depuis l'interface ou avec 'ollama pull llama3:8b'."
  fi
else
  print_success "Le modèle Llama 3 est disponible."
fi

# Si nous avons démarré Ollama, l'arrêter
if [ -n "$OLLAMA_PID" ]; then
  print_message "Arrêt d'Ollama..."
  kill $OLLAMA_PID
fi

print_success "Toutes les dépendances sont installées et configurées."
print_message "Vous pouvez maintenant lancer l'application Louna avec Ollama en exécutant './launch-louna-with-ollama.sh'."
