/**
 * Correction pour permettre à l'agent principal de répondre
 * en désactivant le pré-processeur pour certaines requêtes
 */

class PreprocessorBypass {
    constructor() {
        this.initialized = false;
        this.originalPreprocessor = null;
    }

    /**
     * Applique le bypass du pré-processeur
     */
    async applyBypass() {
        try {
            console.log('🔧 Application du bypass du pré-processeur...');

            // Attendre que le pré-processeur soit disponible
            let attempts = 0;
            while (attempts < 10 && !global.ultraFastPreprocessor) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                attempts++;
            }

            if (!global.ultraFastPreprocessor) {
                console.log('⚠️ Pré-processeur ultra-rapide non trouvé');
                return false;
            }

            // Sauvegarder la fonction originale
            this.originalPreprocessor = global.ultraFastPreprocessor.processMessage;

            // Remplacer par une version qui laisse passer l'agent principal
            global.ultraFastPreprocessor.processMessage = (message, options = {}) => {
                return this.smartPreprocessor(message, options);
            };

            console.log('✅ Bypass du pré-processeur appliqué');
            this.initialized = true;
            return true;

        } catch (error) {
            console.error('❌ Erreur bypass pré-processeur:', error);
            return false;
        }
    }

    /**
     * Pré-processeur intelligent qui laisse passer l'agent principal
     */
    smartPreprocessor(message, options = {}) {
        try {
            // Mots-clés qui forcent l'utilisation de l'agent principal
            const agentPrincipalKeywords = [
                'agent principal', 'vision ultra', 'créateur', 'ollama',
                'recherche internet', 'actualité', '2025', 'année',
                'fallback', 'principal', 'recherche', 'internet'
            ];

            // Vérifier si le message contient des mots-clés importants
            const messageText = message.toLowerCase();
            const needsMainAgent = agentPrincipalKeywords.some(keyword => 
                messageText.includes(keyword.toLowerCase())
            );

            if (needsMainAgent) {
                console.log('🎯 Message important détecté - Redirection vers agent principal');
                // Retourner null pour forcer l'utilisation de l'agent principal
                return null;
            }

            // Pour les autres messages, utiliser le pré-processeur original
            if (this.originalPreprocessor) {
                return this.originalPreprocessor.call(global.ultraFastPreprocessor, message, options);
            }

            // Fallback si pas de pré-processeur original
            return null;

        } catch (error) {
            console.error('❌ Erreur pré-processeur intelligent:', error);
            // En cas d'erreur, laisser passer vers l'agent principal
            return null;
        }
    }

    /**
     * Désactive complètement le pré-processeur
     */
    disablePreprocessor() {
        try {
            if (global.ultraFastPreprocessor) {
                // Remplacer par une fonction qui ne fait rien
                global.ultraFastPreprocessor.processMessage = () => null;
                console.log('🚫 Pré-processeur ultra-rapide désactivé');
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Erreur désactivation pré-processeur:', error);
            return false;
        }
    }
}

// Exporter la classe
module.exports = PreprocessorBypass;

// Auto-application
if (require.main === module) {
    console.log('�� Application automatique du bypass pré-processeur...');
    
    setTimeout(async () => {
        const bypass = new PreprocessorBypass();
        await bypass.applyBypass();
    }, 5000);
}
