/**
 * Gestionnaire de Connectivité Avancé pour Louna
 *
 * Gère WiFi, Bluetooth, AirDrop et communication mobile
 */

const EventEmitter = require('events');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

class ConnectivityManager extends EventEmitter {
    constructor(options = {}) {
        super();

        this.config = {
            wifi: options.wifi !== undefined ? options.wifi : true,
            bluetooth: options.bluetooth !== undefined ? options.bluetooth : true,
            airdrop: options.airdrop !== undefined ? options.airdrop : true,
            autoConnect: options.autoConnect !== undefined ? options.autoConnect : true,
            secureMode: options.secureMode !== undefined ? options.secureMode : true,
            debug: options.debug !== undefined ? options.debug : true
        };

        this.state = {
            wifi: {
                enabled: false,
                connected: false,
                networks: [],
                currentNetwork: null,
                signal: 0
            },
            bluetooth: {
                enabled: false,
                discoverable: false,
                devices: [],
                connectedDevices: [],
                scanning: false
            },
            airdrop: {
                enabled: false,
                discoverable: false,
                contacts: [],
                transfers: []
            },
            mobile: {
                connected: false,
                devices: [],
                activeConnections: []
            }
        };

        this.intervals = {
            wifiScan: null,
            bluetoothScan: null,
            statusUpdate: null
        };
    }

    /**
     * Initialise le gestionnaire de connectivité
     */
    async initialize() {
        try {
            this.log('🔗 Initialisation du gestionnaire de connectivité...');

            // Vérifier les capacités du système
            await this.checkSystemCapabilities();

            // Initialiser les services
            if (this.config.wifi) {
                await this.initializeWiFi();
            }

            if (this.config.bluetooth) {
                await this.initializeBluetooth();
            }

            if (this.config.airdrop) {
                await this.initializeAirDrop();
            }

            // Démarrer la surveillance
            this.startMonitoring();

            this.log('✅ Gestionnaire de connectivité initialisé');
            this.emit('initialized');

        } catch (error) {
            this.log('❌ Erreur lors de l\'initialisation:', error);
            throw error;
        }
    }

    /**
     * Vérifie les capacités du système
     */
    async checkSystemCapabilities() {
        this.log('🔍 Vérification des capacités système...');

        try {
            // Vérifier WiFi
            const wifiCheck = await execAsync('networksetup -listallhardwareports | grep -A 1 Wi-Fi');
            this.log('✅ WiFi disponible');
        } catch (error) {
            this.log('⚠️ WiFi non disponible');
            this.config.wifi = false;
        }

        try {
            // Vérifier Bluetooth
            const bluetoothCheck = await execAsync('system_profiler SPBluetoothDataType');
            this.log('✅ Bluetooth disponible');
        } catch (error) {
            this.log('⚠️ Bluetooth non disponible');
            this.config.bluetooth = false;
        }

        try {
            // Vérifier AirDrop (macOS uniquement)
            const airdropCheck = await execAsync('defaults read com.apple.sharingd DiscoverableMode');
            this.log('✅ AirDrop disponible');
        } catch (error) {
            this.log('⚠️ AirDrop non disponible');
            this.config.airdrop = false;
        }
    }

    /**
     * Initialise le WiFi
     */
    async initializeWiFi() {
        this.log('📶 Initialisation WiFi...');

        try {
            // Activer WiFi si nécessaire
            await execAsync('networksetup -setairportpower en0 on');
            this.state.wifi.enabled = true;

            // Scanner les réseaux disponibles
            await this.scanWiFiNetworks();

            this.log('✅ WiFi initialisé');
            this.emit('wifi:initialized');

        } catch (error) {
            this.log('❌ Erreur WiFi:', error);
        }
    }

    /**
     * Initialise le Bluetooth
     */
    async initializeBluetooth() {
        this.log('🔵 Initialisation Bluetooth...');

        try {
            // Activer Bluetooth
            await execAsync('blueutil -p 1');
            this.state.bluetooth.enabled = true;

            // Scanner les appareils
            await this.scanBluetoothDevices();

            this.log('✅ Bluetooth initialisé');
            this.emit('bluetooth:initialized');

        } catch (error) {
            this.log('❌ Erreur Bluetooth:', error);
        }
    }

    /**
     * Initialise AirDrop
     */
    async initializeAirDrop() {
        this.log('📡 Initialisation AirDrop...');

        try {
            // Activer AirDrop
            await execAsync('defaults write com.apple.sharingd DiscoverableMode -string Contacts');
            this.state.airdrop.enabled = true;
            this.state.airdrop.discoverable = true;

            this.log('✅ AirDrop initialisé');
            this.emit('airdrop:initialized');

        } catch (error) {
            this.log('❌ Erreur AirDrop:', error);
        }
    }

    /**
     * Active le WiFi
     */
    async enableWiFi() {
        if (!this.config.wifi) return false;

        try {
            await execAsync('networksetup -setairportpower en0 on');
            this.state.wifi.enabled = true;

            if (this.config.autoConnect) {
                await this.connectToKnownNetwork();
            }

            this.emit('wifi:enabled');
            return true;

        } catch (error) {
            this.log('❌ Erreur activation WiFi:', error);
            return false;
        }
    }

    /**
     * Active le Bluetooth
     */
    async enableBluetooth() {
        if (!this.config.bluetooth) return false;

        try {
            await execAsync('blueutil -p 1');
            this.state.bluetooth.enabled = true;
            this.state.bluetooth.discoverable = true;

            // Démarrer la découverte
            await this.startBluetoothDiscovery();

            this.emit('bluetooth:enabled');
            return true;

        } catch (error) {
            this.log('❌ Erreur activation Bluetooth:', error);
            return false;
        }
    }

    /**
     * Active AirDrop
     */
    async enableAirDrop() {
        if (!this.config.airdrop) return false;

        try {
            await execAsync('defaults write com.apple.sharingd DiscoverableMode -string Everyone');
            this.state.airdrop.enabled = true;
            this.state.airdrop.discoverable = true;

            this.emit('airdrop:enabled');
            return true;

        } catch (error) {
            this.log('❌ Erreur activation AirDrop:', error);
            return false;
        }
    }

    /**
     * Scanne les réseaux WiFi
     */
    async scanWiFiNetworks() {
        try {
            const { stdout } = await execAsync('/System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport -s');

            const networks = stdout.split('\n')
                .slice(1) // Ignorer l'en-tête
                .filter(line => line.trim())
                .map(line => {
                    const parts = line.trim().split(/\s+/);
                    return {
                        ssid: parts[0],
                        bssid: parts[1],
                        rssi: parseInt(parts[2]),
                        channel: parseInt(parts[3]),
                        security: parts.slice(6).join(' ')
                    };
                });

            this.state.wifi.networks = networks;
            this.emit('wifi:networks_updated', networks);

            return networks;

        } catch (error) {
            this.log('❌ Erreur scan WiFi:', error);
            return [];
        }
    }

    /**
     * Scanne les appareils Bluetooth
     */
    async scanBluetoothDevices() {
        try {
            this.state.bluetooth.scanning = true;

            const { stdout } = await execAsync('blueutil --paired');

            const devices = stdout.split('\n')
                .filter(line => line.trim())
                .map(line => {
                    const match = line.match(/address: ([^,]+), (.+)/);
                    if (match) {
                        return {
                            address: match[1],
                            name: match[2],
                            connected: false,
                            type: 'unknown'
                        };
                    }
                    return null;
                })
                .filter(device => device !== null);

            this.state.bluetooth.devices = devices;
            this.state.bluetooth.scanning = false;

            this.emit('bluetooth:devices_updated', devices);

            return devices;

        } catch (error) {
            this.log('❌ Erreur scan Bluetooth:', error);
            this.state.bluetooth.scanning = false;
            return [];
        }
    }

    /**
     * Démarre la découverte Bluetooth
     */
    async startBluetoothDiscovery() {
        try {
            await execAsync('blueutil --discoverable 1');
            this.state.bluetooth.discoverable = true;

            this.emit('bluetooth:discoverable');

        } catch (error) {
            this.log('❌ Erreur découverte Bluetooth:', error);
        }
    }

    /**
     * Se connecte à un réseau WiFi connu
     */
    async connectToKnownNetwork() {
        try {
            // Obtenir la liste des réseaux préférés
            const { stdout } = await execAsync('networksetup -listpreferredwirelessnetworks en0');

            if (stdout.includes('Preferred networks on en0:')) {
                const networks = stdout.split('\n')
                    .slice(1)
                    .filter(line => line.trim())
                    .map(line => line.trim());

                for (const network of networks) {
                    try {
                        await execAsync(`networksetup -setairportnetwork en0 "${network}"`);
                        this.state.wifi.connected = true;
                        this.state.wifi.currentNetwork = network;

                        this.emit('wifi:connected', network);
                        return true;
                    } catch (error) {
                        continue;
                    }
                }
            }

        } catch (error) {
            this.log('❌ Erreur connexion WiFi:', error);
        }

        return false;
    }

    /**
     * Démarre la surveillance
     */
    startMonitoring() {
        // Surveillance WiFi
        if (this.config.wifi) {
            this.intervals.wifiScan = setInterval(() => {
                this.scanWiFiNetworks();
            }, 30000); // Toutes les 30 secondes
        }

        // Surveillance Bluetooth
        if (this.config.bluetooth) {
            this.intervals.bluetoothScan = setInterval(() => {
                this.scanBluetoothDevices();
            }, 60000); // Toutes les minutes
        }

        // Mise à jour du statut
        this.intervals.statusUpdate = setInterval(() => {
            this.updateStatus();
        }, 10000); // Toutes les 10 secondes
    }

    /**
     * Met à jour le statut
     */
    async updateStatus() {
        try {
            // Vérifier la connexion WiFi
            if (this.config.wifi) {
                const { stdout } = await execAsync('networksetup -getairportnetwork en0');
                this.state.wifi.connected = !stdout.includes('not associated');

                if (this.state.wifi.connected) {
                    const networkMatch = stdout.match(/Current Wi-Fi Network: (.+)/);
                    if (networkMatch) {
                        this.state.wifi.currentNetwork = networkMatch[1];
                    }
                }
            }

            this.emit('status:updated', this.getStatus());

        } catch (error) {
            this.log('❌ Erreur mise à jour statut:', error);
        }
    }

    /**
     * Obtient le statut complet
     */
    getStatus() {
        return {
            wifi: this.state.wifi,
            bluetooth: this.state.bluetooth,
            airdrop: this.state.airdrop,
            mobile: this.state.mobile,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Arrête le gestionnaire
     */
    async stop() {
        this.log('🛑 Arrêt du gestionnaire de connectivité...');

        // Arrêter les intervalles
        Object.values(this.intervals).forEach(interval => {
            if (interval) clearInterval(interval);
        });

        this.emit('stopped');
    }

    /**
     * Envoie un fichier via AirDrop
     */
    async sendViaAirDrop(filePath, recipient = null) {
        try {
            this.log(`📡 Envoi via AirDrop: ${filePath}`);

            // Utiliser l'API AirDrop de macOS
            const command = recipient
                ? `osascript -e 'tell application "AirDrop" to send POSIX file "${filePath}" to "${recipient}"'`
                : `open -a AirDrop "${filePath}"`;

            await execAsync(command);

            this.state.airdrop.transfers.push({
                file: filePath,
                recipient: recipient,
                timestamp: new Date().toISOString(),
                status: 'sent'
            });

            this.emit('airdrop:file_sent', { filePath, recipient });
            return true;

        } catch (error) {
            this.log('❌ Erreur envoi AirDrop:', error);
            return false;
        }
    }

    /**
     * Se connecte à un appareil Bluetooth
     */
    async connectBluetoothDevice(address) {
        try {
            this.log(`🔵 Connexion Bluetooth: ${address}`);

            await execAsync(`blueutil --connect ${address}`);

            // Mettre à jour l'état
            const device = this.state.bluetooth.devices.find(d => d.address === address);
            if (device) {
                device.connected = true;
                this.state.bluetooth.connectedDevices.push(device);
            }

            this.emit('bluetooth:device_connected', address);
            return true;

        } catch (error) {
            this.log('❌ Erreur connexion Bluetooth:', error);
            return false;
        }
    }

    /**
     * Déconnecte un appareil Bluetooth
     */
    async disconnectBluetoothDevice(address) {
        try {
            this.log(`🔵 Déconnexion Bluetooth: ${address}`);

            await execAsync(`blueutil --disconnect ${address}`);

            // Mettre à jour l'état
            const device = this.state.bluetooth.devices.find(d => d.address === address);
            if (device) {
                device.connected = false;
                this.state.bluetooth.connectedDevices =
                    this.state.bluetooth.connectedDevices.filter(d => d.address !== address);
            }

            this.emit('bluetooth:device_disconnected', address);
            return true;

        } catch (error) {
            this.log('❌ Erreur déconnexion Bluetooth:', error);
            return false;
        }
    }

    /**
     * Partage la connexion Internet via hotspot
     */
    async enableHotspot(ssid = 'Louna-Hotspot', password = 'LounaAgent2024') {
        try {
            this.log('📶 Activation du hotspot...');

            // Activer le partage de connexion Internet
            await execAsync('sudo networksetup -setnetworkserviceenabled "iPhone USB" on');

            this.state.wifi.hotspot = {
                enabled: true,
                ssid: ssid,
                password: password,
                clients: []
            };

            this.emit('wifi:hotspot_enabled', { ssid, password });
            return true;

        } catch (error) {
            this.log('❌ Erreur activation hotspot:', error);
            return false;
        }
    }

    /**
     * Fonction de logging
     */
    log(message, level = 'info') {
        if (!this.config.debug && level === 'debug') return;

        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] [ConnectivityManager] ${message}`);
    }
}

module.exports = ConnectivityManager;
