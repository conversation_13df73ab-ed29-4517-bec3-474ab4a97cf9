/**
 * Gestionnaire de raccourcis clavier globaux pour l'application Louna
 * Ce module permet d'enregistrer des raccourcis clavier globaux qui fonctionnent
 * même lorsque l'application n'est pas au premier plan
 */

const { globalShortcut, BrowserWindow, app } = require('electron');

// Configuration des raccourcis clavier
const shortcuts = {
  // Raccourci pour ouvrir/fermer la fenêtre principale
  toggleWindow: {
    key: 'CommandOrControl+Shift+L',
    description: 'Ouvrir/fermer la fenêtre principale'
  },
  
  // Raccourci pour ouvrir l'interface de chat
  openChat: {
    key: 'CommandOrControl+Shift+C',
    description: 'Ouvrir l\'interface de chat'
  },
  
  // Raccourci pour ouvrir l'interface de mémoire thermique
  openMemory: {
    key: 'CommandOrControl+Shift+M',
    description: 'Ouvrir l\'interface de mémoire thermique'
  },
  
  // Raccourci pour ouvrir le tableau de bord des accélérateurs Kyber
  openKyber: {
    key: 'CommandOrControl+Shift+K',
    description: 'Ouvrir le tableau de bord des accélérateurs Kyber'
  },
  
  // Raccourci pour ouvrir l'interface des rêves
  openDreams: {
    key: 'CommandOrControl+Shift+D',
    description: 'Ouvrir l\'interface des rêves'
  },
  
  // Raccourci pour ouvrir les paramètres
  openSettings: {
    key: 'CommandOrControl+Shift+S',
    description: 'Ouvrir les paramètres'
  }
};

/**
 * Enregistre les raccourcis clavier globaux
 * @param {Object} mainWindow - Fenêtre principale de l'application
 * @param {number} port - Port du serveur
 */
function registerGlobalShortcuts(mainWindow, port) {
  // Raccourci pour ouvrir/fermer la fenêtre principale
  globalShortcut.register(shortcuts.toggleWindow.key, () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        mainWindow.show();
        mainWindow.focus();
      }
    } else {
      // Créer une nouvelle fenêtre si elle n'existe pas
      const window = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        icon: app.getAppPath() + '/public/img/louna-icon.png',
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          preload: app.getAppPath() + '/preload.js'
        }
      });
      
      window.loadURL(`http://localhost:${port}/`);
    }
  });
  
  // Raccourci pour ouvrir l'interface de chat
  globalShortcut.register(shortcuts.openChat.key, () => {
    if (mainWindow) {
      mainWindow.loadURL(`http://localhost:${port}/chat.html`);
      mainWindow.show();
      mainWindow.focus();
    }
  });
  
  // Raccourci pour ouvrir l'interface de mémoire thermique
  globalShortcut.register(shortcuts.openMemory.key, () => {
    if (mainWindow) {
      mainWindow.loadURL(`http://localhost:${port}/futuristic-interface.html`);
      mainWindow.show();
      mainWindow.focus();
    }
  });
  
  // Raccourci pour ouvrir le tableau de bord des accélérateurs Kyber
  globalShortcut.register(shortcuts.openKyber.key, () => {
    if (mainWindow) {
      mainWindow.loadURL(`http://localhost:${port}/kyber-dashboard.html`);
      mainWindow.show();
      mainWindow.focus();
    }
  });
  
  // Raccourci pour ouvrir l'interface des rêves
  globalShortcut.register(shortcuts.openDreams.key, () => {
    if (mainWindow) {
      mainWindow.loadURL(`http://localhost:${port}/dreams-new.html`);
      mainWindow.show();
      mainWindow.focus();
    }
  });
  
  // Raccourci pour ouvrir les paramètres
  globalShortcut.register(shortcuts.openSettings.key, () => {
    if (mainWindow) {
      mainWindow.loadURL(`http://localhost:${port}/settings-new.html`);
      mainWindow.show();
      mainWindow.focus();
    }
  });
  
  console.log('Raccourcis clavier globaux enregistrés');
}

/**
 * Désenregistre tous les raccourcis clavier globaux
 */
function unregisterGlobalShortcuts() {
  globalShortcut.unregisterAll();
  console.log('Raccourcis clavier globaux désenregistrés');
}

/**
 * Récupère la liste des raccourcis clavier
 * @returns {Object} - Liste des raccourcis clavier
 */
function getShortcuts() {
  return shortcuts;
}

module.exports = {
  registerGlobalShortcuts,
  unregisterGlobalShortcuts,
  getShortcuts
};
