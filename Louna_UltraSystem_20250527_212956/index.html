<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>na - Agent à Mémoire Thermique</title>
    
    <!-- Styles de base -->
    <link rel="stylesheet" href="/css/futuristic-interface.css">
    <link rel="stylesheet" href="/css/kyber-styles.css">
    
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Police Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    
    <style>
        .welcome-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background-color: var(--bg-card);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            border: var(--border-light);
            text-align: center;
        }
        
        .welcome-title {
            font-size: 32px;
            margin-bottom: 20px;
            color: var(--accent);
        }
        
        .welcome-subtitle {
            font-size: 18px;
            margin-bottom: 30px;
            color: var(--text-secondary);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .feature-card {
            background-color: var(--bg-light);
            padding: 20px;
            border-radius: var(--border-radius);
            border: var(--border-light);
            transition: var(--transition);
            text-align: left;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border: var(--border-accent);
        }
        
        .feature-icon {
            font-size: 36px;
            margin-bottom: 15px;
            color: var(--accent);
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        .feature-description {
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
        }
        
        .action-button {
            padding: 12px 24px;
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: var(--border-radius-sm);
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-button:hover {
            background-color: var(--primary-light);
            box-shadow: var(--glow-effect);
        }
        
        .action-button.accent {
            background-color: var(--accent);
        }
        
        .action-button.accent:hover {
            background-color: var(--secondary);
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- Contenu principal -->
        <div class="main-content">
            <div class="app-container">
                <!-- En-tête principal -->
                <header>
                    <div class="logo">
                        <i class="fas fa-brain"></i>
                        <h1>Louna - Agent à Mémoire Thermique</h1>
                    </div>
                    <div id="app-status" class="connected">Connecté</div>
                </header>

                                                <!-- Navigation principale -->
                <nav>
                    <ul>
                        <li><a href="/" class="active"><i class="fas fa-home"></i> Accueil</a></li>
                        <li><a href="/chat"><i class="fas fa-crown" style="color: #ff6b6b;"></i> 🎯 HUB CENTRAL - MAÎTRE</a></li>
                        <li><a href="/futuristic-interface.html"><i class="fas fa-fire"></i> Interface Thermique</a></li>
                        <li><a href="/kyber-dashboard.html"><i class="fas fa-bolt"></i> Accélérateurs Kyber</a></li>
                        <li><a href="/chat-simple"><i class="fas fa-comments"></i> Chat Simple</a></li>
                        <li><a href="/chat-complet"><i class="fas fa-robot"></i> Chat Complet</a></li>
                        <li><a href="/chat-ultra-complet"><i class="fas fa-rocket"></i> Chat Ultra-Complet</a></li>
                    </ul>
                </nav>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <h3 class="feature-title">Mémoire Thermique</h3>
                            <p class="feature-description">Système de mémoire avancé qui organise les informations selon leur importance et leur fraîcheur, comme un cerveau humain.</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <h3 class="feature-title">Accélérateurs Kyber</h3>
                            <p class="feature-description">Optimisez les performances de la mémoire thermique avec des accélérateurs qui s'adaptent automatiquement aux besoins.</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h3 class="feature-title">Intelligence Adaptative</h3>
                            <p class="feature-description">L'agent apprend et s'adapte en continu, améliorant ses capacités au fil du temps.</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h3 class="feature-title">Visualisations Avancées</h3>
                            <p class="feature-description">Observez le fonctionnement interne de la mémoire thermique avec des visualisations interactives en temps réel.</p>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="/futuristic-interface.html" class="action-button">
                            <i class="fas fa-fire"></i> Interface Thermique
                        </a>
                        <a href="/kyber-dashboard.html" class="action-button accent">
                            <i class="fas fa-bolt"></i> Accélérateurs Kyber
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Vérifier l'état de la connexion
        document.addEventListener('DOMContentLoaded', () => {
            // Simuler une connexion réussie
            const appStatus = document.getElementById('app-status');
            if (appStatus) {
                appStatus.className = 'connected';
                appStatus.textContent = 'Connecté';
            }
        });
    <!-- Script de correction des boutons d'envoi -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="/js/chat-buttons-fix.js"></script>
    </script>
</body>
</html>
