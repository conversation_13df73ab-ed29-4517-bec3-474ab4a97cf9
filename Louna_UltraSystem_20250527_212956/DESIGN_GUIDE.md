# 🎨 GUIDE DE DESIGN LOUNA - COHÉRENCE PARFAITE

## 🎯 **VISION DESIGN**

Louna utilise un design system unifié basé sur :
- **Futurisme élégant** avec des effets de verre et de flou
- **Couleurs cohérentes** : <PERSON> (#ff69b4) et <PERSON><PERSON> (#4ecdc4)
- **Typographie moderne** : Roboto avec hiérarchie claire
- **Animations fluides** et transitions harmonieuses
- **Responsive design** pour tous les écrans

---

## 🎨 **PALETTE DE COULEURS**

### Couleurs Principales
```css
--primary-pink: #ff69b4        /* Rose principal */
--primary-dark-pink: #e91e63   /* <PERSON> foncé */
--secondary-cyan: #4ecdc4       /* Cyan principal */
--secondary-dark-cyan: #44a08d  /* <PERSON><PERSON> foncé */
```

### Couleurs de Fond
```css
--bg-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)
--bg-glass: rgba(255, 255, 255, 0.1)    /* Effet verre */
--bg-secondary: rgba(255, 255, 255, 0.1) /* Fond secondaire */
```

### Couleurs de Texte
```css
--text-primary: #ffffff         /* Texte principal */
--text-secondary: rgba(255, 255, 255, 0.9)  /* Texte secondaire */
--text-muted: rgba(255, 255, 255, 0.7)      /* Texte atténué */
```

### Couleurs d'État
```css
--success: #28a745    /* Succès */
--warning: #ffc107    /* Attention */
--error: #dc3545      /* Erreur */
--info: #17a2b8       /* Information */
```

---

## 📐 **ESPACEMENTS & DIMENSIONS**

### Espacements Standards
```css
--spacing-xs: 5px     /* Très petit */
--spacing-sm: 10px    /* Petit */
--spacing-md: 15px    /* Moyen */
--spacing-lg: 20px    /* Grand */
--spacing-xl: 30px    /* Très grand */
--spacing-xxl: 40px   /* Extra grand */
```

### Bordures
```css
--border-radius-sm: 5px    /* Petit rayon */
--border-radius-md: 10px   /* Moyen rayon */
--border-radius-lg: 15px   /* Grand rayon */
--border-radius-xl: 25px   /* Très grand rayon */
```

---

## 🔤 **TYPOGRAPHIE**

### Hiérarchie des Titres
- **H1** : 2rem (32px) - Titres principaux
- **H2** : 1.5rem (24px) - Sous-titres
- **H3** : 1.25rem (20px) - Sections
- **Body** : 1rem (16px) - Texte normal
- **Small** : 0.875rem (14px) - Texte secondaire

### Poids des Polices
- **300** : Light (texte secondaire)
- **400** : Regular (texte normal)
- **500** : Medium (labels, boutons)
- **700** : Bold (titres, emphasis)

---

## 🧩 **COMPOSANTS STANDARDS**

### 1. Header Unifié
```html
<div class="louna-header">
    <div class="louna-header-content">
        <div class="louna-header-title">
            <i class="fas fa-brain louna-header-icon"></i>
            <h1>Titre de la Page</h1>
        </div>
        <div class="louna-status online">
            <div class="louna-status-dot"></div>
            <span>Statut</span>
        </div>
    </div>
</div>
```

### 2. Cartes Unifiées
```html
<div class="louna-card">
    <div class="louna-card-header">
        <div class="louna-card-title">
            <i class="fas fa-icon louna-card-icon"></i>
            Titre de la Carte
        </div>
    </div>
    <!-- Contenu de la carte -->
</div>
```

### 3. Boutons Standards
```html
<!-- Bouton principal -->
<button class="louna-btn primary">
    <i class="fas fa-icon"></i>
    <span>Action Principale</span>
</button>

<!-- Bouton secondaire -->
<button class="louna-btn secondary">
    <i class="fas fa-icon"></i>
    <span>Action Secondaire</span>
</button>

<!-- Bouton fantôme -->
<button class="louna-btn ghost">
    <i class="fas fa-icon"></i>
    <span>Action Tertiaire</span>
</button>
```

### 4. Indicateurs d'État
```html
<div class="louna-status online">
    <div class="louna-status-dot"></div>
    <span>En ligne</span>
</div>

<div class="louna-status warning">
    <div class="louna-status-dot"></div>
    <span>Attention</span>
</div>

<div class="louna-status error">
    <div class="louna-status-dot"></div>
    <span>Erreur</span>
</div>
```

### 5. Métriques
```html
<div class="louna-metrics">
    <div class="louna-metric">
        <div class="louna-metric-value">150</div>
        <div class="louna-metric-label">QI Système</div>
        <div class="louna-metric-change positive">+5%</div>
    </div>
</div>
```

---

## 🎭 **ANIMATIONS & TRANSITIONS**

### Transitions Standards
```css
--transition-fast: 0.2s ease     /* Hover, focus */
--transition-normal: 0.3s ease   /* Changements d'état */
--transition-slow: 0.5s ease     /* Animations complexes */
```

### Animations Communes
- **fadeIn** : Apparition en fondu
- **slideIn** : Glissement depuis la droite
- **pulse** : Pulsation pour les indicateurs
- **spin** : Rotation pour les loaders

---

## 📱 **RESPONSIVE DESIGN**

### Points de Rupture
```css
/* Mobile */
@media (max-width: 768px) {
    /* Adaptations mobile */
}

/* Tablette */
@media (max-width: 1200px) {
    /* Adaptations tablette */
}

/* Desktop */
@media (min-width: 1201px) {
    /* Styles desktop */
}
```

### Grilles Responsives
```html
<!-- Grille adaptative -->
<div class="louna-grid cols-4">
    <!-- 4 colonnes sur desktop, 2 sur tablette, 1 sur mobile -->
</div>
```

---

## 🔔 **SYSTÈME DE NOTIFICATIONS**

### Types de Notifications
```javascript
// Succès
showSuccess('Opération réussie !');

// Erreur
showError('Une erreur est survenue');

// Attention
showWarning('Attention requise');

// Information
showInfo('Information importante');

// Chargement
showLoading('Chargement en cours...');
```

### Notifications Spécialisées
```javascript
// Système
LounaNotify.system('Message système', 'success');

// Agent
LounaNotify.agent('Message de Claude', 'info');

// Mémoire
LounaNotify.memory('État mémoire thermique', 'success');
```

---

## 🧭 **NAVIGATION UNIFIÉE**

### Utilisation du Système de Navigation
```javascript
// Configuration automatique
window.LounaNav.init({
    header: true,           // Header unifié
    quickNav: true,         // Navigation rapide
    breadcrumb: true,       // Fil d'Ariane
    sidebar: false          // Sidebar (optionnel)
});
```

---

## ✅ **CHECKLIST DE COHÉRENCE**

### Pour Chaque Nouvelle Interface :

#### 🎨 **Design**
- [ ] Utilise le CSS unifié (`louna-unified-design.css`)
- [ ] Respecte la palette de couleurs
- [ ] Utilise les espacements standards
- [ ] Applique les bordures arrondies cohérentes

#### 🧩 **Composants**
- [ ] Header unifié avec icône et titre
- [ ] Cartes avec structure standard
- [ ] Boutons avec classes appropriées
- [ ] Indicateurs d'état cohérents

#### 📱 **Responsive**
- [ ] Fonctionne sur mobile (< 768px)
- [ ] Adapté pour tablette (768px - 1200px)
- [ ] Optimisé pour desktop (> 1200px)

#### 🔔 **Interactions**
- [ ] Notifications pour les actions importantes
- [ ] Transitions fluides sur les interactions
- [ ] États de chargement visibles
- [ ] Feedback utilisateur approprié

#### 🧭 **Navigation**
- [ ] Intégration du système de navigation
- [ ] Bouton retour vers l'accueil
- [ ] Liens vers les autres interfaces
- [ ] Breadcrumb si nécessaire

---

## 🚀 **BONNES PRATIQUES**

### 1. **Cohérence Visuelle**
- Toujours utiliser les classes CSS unifiées
- Respecter la hiérarchie typographique
- Maintenir les espacements constants

### 2. **Expérience Utilisateur**
- Feedback immédiat sur les actions
- États de chargement visibles
- Messages d'erreur clairs et utiles

### 3. **Performance**
- Charger les CSS dans l'ordre correct
- Optimiser les animations
- Éviter les reflows inutiles

### 4. **Accessibilité**
- Contraste suffisant pour le texte
- Navigation au clavier possible
- Indicateurs d'état clairs

---

## 🎯 **RÉSULTAT ATTENDU**

Avec ce guide, toutes les interfaces Louna doivent :
- **Paraître cohérentes** et professionnelles
- **Fonctionner harmonieusement** ensemble
- **Offrir une expérience utilisateur** fluide et intuitive
- **Maintenir l'identité visuelle** de Louna

**🎉 OBJECTIF : UNE APPLICATION PARFAITEMENT COHÉRENTE ! 🎉**
