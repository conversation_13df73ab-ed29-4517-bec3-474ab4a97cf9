/**
 * Gestionnaire de démarrage automatique pour l'application Louna
 * Ce module permet de configurer le démarrage automatique de l'application
 * au démarrage du système
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Chemin vers le fichier de configuration
const configPath = path.join(app.getPath('userData'), 'auto-launch.json');

// Configuration par défaut
const defaultConfig = {
  enabled: false,
  minimized: true
};

/**
 * Charge la configuration de démarrage automatique
 * @returns {Object} - Configuration de démarrage automatique
 */
function loadConfig() {
  try {
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      return { ...defaultConfig, ...config };
    }
  } catch (error) {
    console.error('Erreur lors du chargement de la configuration de démarrage automatique :', error);
  }
  
  return defaultConfig;
}

/**
 * Sauvegarde la configuration de démarrage automatique
 * @param {Object} config - Configuration de démarrage automatique
 */
function saveConfig(config) {
  try {
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de la configuration de démarrage automatique :', error);
  }
}

/**
 * Configure le démarrage automatique de l'application
 * @param {boolean} enabled - Activer ou désactiver le démarrage automatique
 * @param {boolean} minimized - Démarrer l'application minimisée
 */
function setAutoLaunch(enabled, minimized = true) {
  // Charger la configuration actuelle
  const config = loadConfig();
  
  // Mettre à jour la configuration
  config.enabled = enabled;
  config.minimized = minimized;
  
  // Sauvegarder la configuration
  saveConfig(config);
  
  // Configurer le démarrage automatique
  if (process.platform === 'darwin') {
    // macOS
    app.setLoginItemSettings({
      openAtLogin: enabled,
      openAsHidden: minimized
    });
  } else if (process.platform === 'win32') {
    // Windows
    app.setLoginItemSettings({
      openAtLogin: enabled,
      openAsHidden: minimized,
      path: process.execPath,
      args: minimized ? ['--hidden'] : []
    });
  } else if (process.platform === 'linux') {
    // Linux
    const desktopEntry = `[Desktop Entry]
Name=Louna
Exec="${process.execPath}" ${minimized ? '--hidden' : ''}
Type=Application
Terminal=false
X-GNOME-Autostart-enabled=${enabled}
`;
    
    try {
      const autostartDir = path.join(os.homedir(), '.config', 'autostart');
      
      // Créer le dossier autostart s'il n'existe pas
      if (!fs.existsSync(autostartDir)) {
        fs.mkdirSync(autostartDir, { recursive: true });
      }
      
      const desktopFilePath = path.join(autostartDir, 'louna.desktop');
      
      if (enabled) {
        fs.writeFileSync(desktopFilePath, desktopEntry);
      } else if (fs.existsSync(desktopFilePath)) {
        fs.unlinkSync(desktopFilePath);
      }
    } catch (error) {
      console.error('Erreur lors de la configuration du démarrage automatique sous Linux :', error);
    }
  }
  
  console.log(`Démarrage automatique ${enabled ? 'activé' : 'désactivé'} (minimisé: ${minimized})`);
}

/**
 * Vérifie si le démarrage automatique est activé
 * @returns {boolean} - True si le démarrage automatique est activé
 */
function isAutoLaunchEnabled() {
  const config = loadConfig();
  return config.enabled;
}

/**
 * Vérifie si l'application doit démarrer minimisée
 * @returns {boolean} - True si l'application doit démarrer minimisée
 */
function isMinimizedOnLaunch() {
  const config = loadConfig();
  return config.minimized;
}

/**
 * Initialise le démarrage automatique
 */
function initialize() {
  const config = loadConfig();
  setAutoLaunch(config.enabled, config.minimized);
}

module.exports = {
  setAutoLaunch,
  isAutoLaunchEnabled,
  isMinimizedOnLaunch,
  initialize
};
