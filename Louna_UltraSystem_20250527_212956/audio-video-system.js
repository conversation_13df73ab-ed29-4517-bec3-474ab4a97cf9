/**
 * Système Audio/Vidéo complet pour Louna Electron
 * Gestion du micro, haut-parleurs, caméra et traitement vocal
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');

class AudioVideoSystem extends EventEmitter {
  constructor(options = {}) {
    super();

    this.config = {
      audio: {
        sampleRate: options.sampleRate || 44100,
        channels: options.channels || 2,
        bitDepth: options.bitDepth || 16,
        bufferSize: options.bufferSize || 4096,
        echoCancellation: options.echoCancellation !== false,
        noiseSuppression: options.noiseSuppression !== false,
        autoGainControl: options.autoGainControl !== false
      },
      video: {
        width: options.videoWidth || 1280,
        height: options.videoHeight || 720,
        frameRate: options.frameRate || 30,
        facingMode: options.facingMode || 'user'
      },
      voice: {
        language: options.language || 'fr-FR',
        voiceURI: options.voiceURI || null,
        rate: options.rate || 1.0,
        pitch: options.pitch || 1.0,
        volume: options.volume || 1.0
      },
      recording: {
        maxDuration: options.maxDuration || 300000, // 5 minutes
        format: options.format || 'webm',
        videoBitsPerSecond: options.videoBitsPerSecond || 2500000,
        audioBitsPerSecond: options.audioBitsPerSecond || 128000
      }
    };

    this.state = {
      audioContext: null,
      mediaStream: null,
      mediaRecorder: null,
      isRecording: false,
      isPlaying: false,
      isMuted: false,
      volume: 1.0,
      devices: {
        audioInputs: [],
        audioOutputs: [],
        videoInputs: []
      },
      currentDevices: {
        audioInput: null,
        audioOutput: null,
        videoInput: null
      }
    };

    this.audioElements = new Map();
    this.videoElements = new Map();
    this.recordedChunks = [];

    this.init();
  }

  /**
   * Initialise le système audio/vidéo
   */
  async init() {
    try {
      console.log('🎵 Initialisation du système audio/vidéo...');

      // Initialiser le contexte audio
      await this.initAudioContext();

      // Énumérer les périphériques
      await this.enumerateDevices();

      // Configurer les gestionnaires d'événements
      this.setupEventHandlers();

      console.log('✅ Système audio/vidéo initialisé');
      this.emit('initialized');

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation audio/vidéo:', error);
      this.emit('error', error);
    }
  }

  /**
   * Initialise le contexte audio
   */
  async initAudioContext() {
    if (!this.state.audioContext) {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      this.state.audioContext = new AudioContext({
        sampleRate: this.config.audio.sampleRate,
        latencyHint: 'interactive'
      });

      // Reprendre le contexte si suspendu
      if (this.state.audioContext.state === 'suspended') {
        await this.state.audioContext.resume();
      }
    }
  }

  /**
   * Énumère tous les périphériques audio/vidéo disponibles
   */
  async enumerateDevices() {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();

      this.state.devices.audioInputs = devices.filter(device => device.kind === 'audioinput');
      this.state.devices.audioOutputs = devices.filter(device => device.kind === 'audiooutput');
      this.state.devices.videoInputs = devices.filter(device => device.kind === 'videoinput');

      console.log(`📱 Périphériques détectés:
        - Micros: ${this.state.devices.audioInputs.length}
        - Haut-parleurs: ${this.state.devices.audioOutputs.length}
        - Caméras: ${this.state.devices.videoInputs.length}`);

      // Sélectionner les périphériques par défaut
      this.selectDefaultDevices();

      this.emit('devicesEnumerated', this.state.devices);

    } catch (error) {
      console.error('❌ Erreur lors de l\'énumération des périphériques:', error);
      throw error;
    }
  }

  /**
   * Sélectionne les périphériques par défaut
   */
  selectDefaultDevices() {
    if (this.state.devices.audioInputs.length > 0) {
      this.state.currentDevices.audioInput = this.state.devices.audioInputs[0];
    }

    if (this.state.devices.audioOutputs.length > 0) {
      this.state.currentDevices.audioOutput = this.state.devices.audioOutputs[0];
    }

    if (this.state.devices.videoInputs.length > 0) {
      this.state.currentDevices.videoInput = this.state.devices.videoInputs[0];
    }
  }

  /**
   * Configure les gestionnaires d'événements
   */
  setupEventHandlers() {
    // Écouter les changements de périphériques
    navigator.mediaDevices.addEventListener('devicechange', () => {
      this.enumerateDevices();
    });

    // Gérer la suspension/reprise du contexte audio
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.state.audioContext) {
        this.state.audioContext.suspend();
      } else if (!document.hidden && this.state.audioContext) {
        this.state.audioContext.resume();
      }
    });
  }

  /**
   * Démarre l'enregistrement audio
   */
  async startAudioRecording(options = {}) {
    try {
      console.log('🎤 Démarrage de l\'enregistrement audio...');

      const constraints = {
        audio: {
          deviceId: this.state.currentDevices.audioInput?.deviceId,
          sampleRate: this.config.audio.sampleRate,
          channelCount: this.config.audio.channels,
          echoCancellation: this.config.audio.echoCancellation,
          noiseSuppression: this.config.audio.noiseSuppression,
          autoGainControl: this.config.audio.autoGainControl
        }
      };

      this.state.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

      this.state.mediaRecorder = new MediaRecorder(this.state.mediaStream, {
        mimeType: 'audio/webm;codecs=opus',
        audioBitsPerSecond: this.config.recording.audioBitsPerSecond
      });

      this.recordedChunks = [];

      this.state.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.state.mediaRecorder.onstop = () => {
        this.processRecordedAudio();
      };

      this.state.mediaRecorder.start(100); // Collecte des données toutes les 100ms
      this.state.isRecording = true;

      // Arrêt automatique après la durée maximale
      setTimeout(() => {
        if (this.state.isRecording) {
          this.stopRecording();
        }
      }, this.config.recording.maxDuration);

      console.log('✅ Enregistrement audio démarré');
      this.emit('recordingStarted', 'audio');

    } catch (error) {
      console.error('❌ Erreur lors du démarrage de l\'enregistrement audio:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Démarre l'enregistrement vidéo
   */
  async startVideoRecording(options = {}) {
    try {
      console.log('📹 Démarrage de l\'enregistrement vidéo...');

      const constraints = {
        audio: {
          deviceId: this.state.currentDevices.audioInput?.deviceId,
          echoCancellation: this.config.audio.echoCancellation,
          noiseSuppression: this.config.audio.noiseSuppression
        },
        video: {
          deviceId: this.state.currentDevices.videoInput?.deviceId,
          width: { ideal: this.config.video.width },
          height: { ideal: this.config.video.height },
          frameRate: { ideal: this.config.video.frameRate },
          facingMode: this.config.video.facingMode
        }
      };

      this.state.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

      this.state.mediaRecorder = new MediaRecorder(this.state.mediaStream, {
        mimeType: 'video/webm;codecs=vp9,opus',
        videoBitsPerSecond: this.config.recording.videoBitsPerSecond,
        audioBitsPerSecond: this.config.recording.audioBitsPerSecond
      });

      this.recordedChunks = [];

      this.state.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.state.mediaRecorder.onstop = () => {
        this.processRecordedVideo();
      };

      this.state.mediaRecorder.start(100);
      this.state.isRecording = true;

      // Arrêt automatique après la durée maximale
      setTimeout(() => {
        if (this.state.isRecording) {
          this.stopRecording();
        }
      }, this.config.recording.maxDuration);

      console.log('✅ Enregistrement vidéo démarré');
      this.emit('recordingStarted', 'video');

    } catch (error) {
      console.error('❌ Erreur lors du démarrage de l\'enregistrement vidéo:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Arrête l'enregistrement
   */
  stopRecording() {
    if (this.state.mediaRecorder && this.state.isRecording) {
      console.log('⏹️ Arrêt de l\'enregistrement...');

      this.state.mediaRecorder.stop();
      this.state.isRecording = false;

      // Arrêter tous les tracks du stream
      if (this.state.mediaStream) {
        this.state.mediaStream.getTracks().forEach(track => track.stop());
        this.state.mediaStream = null;
      }

      console.log('✅ Enregistrement arrêté');
      this.emit('recordingStopped');
    }
  }

  /**
   * Traite l'audio enregistré
   */
  processRecordedAudio() {
    const blob = new Blob(this.recordedChunks, { type: 'audio/webm' });
    const audioUrl = URL.createObjectURL(blob);

    console.log('🎵 Audio enregistré traité');
    this.emit('audioRecorded', {
      blob,
      url: audioUrl,
      duration: this.calculateDuration(),
      size: blob.size
    });
  }

  /**
   * Traite la vidéo enregistrée
   */
  processRecordedVideo() {
    const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
    const videoUrl = URL.createObjectURL(blob);

    console.log('📹 Vidéo enregistrée traitée');
    this.emit('videoRecorded', {
      blob,
      url: videoUrl,
      duration: this.calculateDuration(),
      size: blob.size
    });
  }

  /**
   * Calcule la durée d'enregistrement
   */
  calculateDuration() {
    // Estimation basée sur le nombre de chunks et l'intervalle
    return this.recordedChunks.length * 100; // 100ms par chunk
  }

  /**
   * Lit un fichier audio
   */
  async playAudio(audioSource, options = {}) {
    try {
      const audioId = options.id || `audio_${Date.now()}`;

      let audio;
      if (typeof audioSource === 'string') {
        // URL ou chemin de fichier
        audio = new Audio(audioSource);
      } else if (audioSource instanceof Blob) {
        // Blob audio
        const url = URL.createObjectURL(audioSource);
        audio = new Audio(url);
      } else {
        throw new Error('Source audio non supportée');
      }

      // Configuration de l'audio
      audio.volume = options.volume || this.state.volume;
      audio.loop = options.loop || false;
      audio.preload = 'auto';

      // Sélectionner le périphérique de sortie si supporté
      if (audio.setSinkId && this.state.currentDevices.audioOutput) {
        await audio.setSinkId(this.state.currentDevices.audioOutput.deviceId);
      }

      // Gestionnaires d'événements
      audio.onloadstart = () => this.emit('audioLoadStart', audioId);
      audio.oncanplay = () => this.emit('audioCanPlay', audioId);
      audio.onplay = () => {
        this.state.isPlaying = true;
        this.emit('audioPlay', audioId);
      };
      audio.onpause = () => {
        this.state.isPlaying = false;
        this.emit('audioPause', audioId);
      };
      audio.onended = () => {
        this.state.isPlaying = false;
        this.audioElements.delete(audioId);
        this.emit('audioEnded', audioId);
      };
      audio.onerror = (error) => this.emit('audioError', audioId, error);

      // Stocker l'élément audio
      this.audioElements.set(audioId, audio);

      // Démarrer la lecture
      await audio.play();

      console.log(`🎵 Lecture audio démarrée: ${audioId}`);
      return audioId;

    } catch (error) {
      console.error('❌ Erreur lors de la lecture audio:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Lit un fichier vidéo
   */
  async playVideo(videoSource, videoElement, options = {}) {
    try {
      const videoId = options.id || `video_${Date.now()}`;

      if (!videoElement || !(videoElement instanceof HTMLVideoElement)) {
        throw new Error('Élément vidéo HTML requis');
      }

      // Configuration de la vidéo
      if (typeof videoSource === 'string') {
        videoElement.src = videoSource;
      } else if (videoSource instanceof Blob) {
        const url = URL.createObjectURL(videoSource);
        videoElement.src = url;
      } else {
        throw new Error('Source vidéo non supportée');
      }

      videoElement.volume = options.volume || this.state.volume;
      videoElement.loop = options.loop || false;
      videoElement.controls = options.controls !== false;
      videoElement.autoplay = options.autoplay || false;
      videoElement.muted = options.muted || this.state.isMuted;

      // Sélectionner le périphérique de sortie audio si supporté
      if (videoElement.setSinkId && this.state.currentDevices.audioOutput) {
        await videoElement.setSinkId(this.state.currentDevices.audioOutput.deviceId);
      }

      // Gestionnaires d'événements
      videoElement.onloadstart = () => this.emit('videoLoadStart', videoId);
      videoElement.oncanplay = () => this.emit('videoCanPlay', videoId);
      videoElement.onplay = () => {
        this.state.isPlaying = true;
        this.emit('videoPlay', videoId);
      };
      videoElement.onpause = () => {
        this.state.isPlaying = false;
        this.emit('videoPause', videoId);
      };
      videoElement.onended = () => {
        this.state.isPlaying = false;
        this.videoElements.delete(videoId);
        this.emit('videoEnded', videoId);
      };
      videoElement.onerror = (error) => this.emit('videoError', videoId, error);

      // Stocker l'élément vidéo
      this.videoElements.set(videoId, videoElement);

      console.log(`📹 Lecture vidéo configurée: ${videoId}`);
      return videoId;

    } catch (error) {
      console.error('❌ Erreur lors de la configuration vidéo:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Synthèse vocale (Text-to-Speech)
   */
  async speak(text, options = {}) {
    try {
      if (!('speechSynthesis' in window)) {
        throw new Error('Synthèse vocale non supportée par ce navigateur');
      }

      // Arrêter toute synthèse en cours
      speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);

      // Configuration de la voix
      utterance.lang = options.language || this.config.voice.language;
      utterance.rate = options.rate || this.config.voice.rate;
      utterance.pitch = options.pitch || this.config.voice.pitch;
      utterance.volume = options.volume || this.config.voice.volume;

      // Sélectionner une voix spécifique si demandée
      if (options.voiceURI || this.config.voice.voiceURI) {
        const voices = speechSynthesis.getVoices();
        const selectedVoice = voices.find(voice =>
          voice.voiceURI === (options.voiceURI || this.config.voice.voiceURI) ||
          voice.name === (options.voiceURI || this.config.voice.voiceURI)
        );
        if (selectedVoice) {
          utterance.voice = selectedVoice;
        }
      }

      // Gestionnaires d'événements
      utterance.onstart = () => {
        console.log('🗣️ Synthèse vocale démarrée');
        this.emit('speechStart', text);
      };

      utterance.onend = () => {
        console.log('✅ Synthèse vocale terminée');
        this.emit('speechEnd', text);
      };

      utterance.onerror = (error) => {
        console.error('❌ Erreur de synthèse vocale:', error);
        this.emit('speechError', error);
      };

      utterance.onpause = () => this.emit('speechPause');
      utterance.onresume = () => this.emit('speechResume');

      // Démarrer la synthèse
      speechSynthesis.speak(utterance);

      return new Promise((resolve, reject) => {
        utterance.onend = () => {
          this.emit('speechEnd', text);
          resolve();
        };
        utterance.onerror = (error) => {
          this.emit('speechError', error);
          reject(error);
        };
      });

    } catch (error) {
      console.error('❌ Erreur lors de la synthèse vocale:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Reconnaissance vocale (Speech-to-Text)
   */
  async startSpeechRecognition(options = {}) {
    try {
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        throw new Error('Reconnaissance vocale non supportée par ce navigateur');
      }

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();

      // Configuration de la reconnaissance
      recognition.lang = options.language || this.config.voice.language;
      recognition.continuous = options.continuous !== false;
      recognition.interimResults = options.interimResults !== false;
      recognition.maxAlternatives = options.maxAlternatives || 3;

      // Gestionnaires d'événements
      recognition.onstart = () => {
        console.log('🎤 Reconnaissance vocale démarrée');
        this.emit('recognitionStart');
      };

      recognition.onresult = (event) => {
        const results = [];

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          const transcript = result[0].transcript;
          const confidence = result[0].confidence;

          results.push({
            transcript,
            confidence,
            isFinal: result.isFinal
          });

          if (result.isFinal) {
            console.log(`🗣️ Reconnaissance finale: "${transcript}" (${Math.round(confidence * 100)}%)`);
            this.emit('recognitionResult', transcript, confidence);
          } else {
            console.log(`🗣️ Reconnaissance intermédiaire: "${transcript}"`);
            this.emit('recognitionInterim', transcript);
          }
        }

        this.emit('recognitionResults', results);
      };

      recognition.onerror = (error) => {
        console.error('❌ Erreur de reconnaissance vocale:', error);
        this.emit('recognitionError', error);
      };

      recognition.onend = () => {
        console.log('✅ Reconnaissance vocale terminée');
        this.emit('recognitionEnd');
      };

      recognition.onnomatch = () => {
        console.log('🤷 Aucune correspondance trouvée');
        this.emit('recognitionNoMatch');
      };

      recognition.onsoundstart = () => this.emit('recognitionSoundStart');
      recognition.onsoundend = () => this.emit('recognitionSoundEnd');
      recognition.onspeechstart = () => this.emit('recognitionSpeechStart');
      recognition.onspeechend = () => this.emit('recognitionSpeechEnd');

      // Démarrer la reconnaissance
      recognition.start();

      // Stocker la référence pour pouvoir l'arrêter
      this.currentRecognition = recognition;

      return recognition;

    } catch (error) {
      console.error('❌ Erreur lors du démarrage de la reconnaissance vocale:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Arrête la reconnaissance vocale
   */
  stopSpeechRecognition() {
    if (this.currentRecognition) {
      console.log('⏹️ Arrêt de la reconnaissance vocale');
      this.currentRecognition.stop();
      this.currentRecognition = null;
    }
  }

  /**
   * Obtient la liste des voix disponibles
   */
  getAvailableVoices() {
    if (!('speechSynthesis' in window)) {
      return [];
    }

    const voices = speechSynthesis.getVoices();
    return voices.map(voice => ({
      name: voice.name,
      lang: voice.lang,
      voiceURI: voice.voiceURI,
      localService: voice.localService,
      default: voice.default
    }));
  }

  /**
   * Change le périphérique audio d'entrée
   */
  async setAudioInputDevice(deviceId) {
    try {
      const device = this.state.devices.audioInputs.find(d => d.deviceId === deviceId);
      if (!device) {
        throw new Error('Périphérique audio d\'entrée non trouvé');
      }

      this.state.currentDevices.audioInput = device;
      console.log(`🎤 Périphérique d'entrée changé: ${device.label}`);
      this.emit('audioInputChanged', device);

    } catch (error) {
      console.error('❌ Erreur lors du changement de périphérique d\'entrée:', error);
      throw error;
    }
  }

  /**
   * Change le périphérique audio de sortie
   */
  async setAudioOutputDevice(deviceId) {
    try {
      const device = this.state.devices.audioOutputs.find(d => d.deviceId === deviceId);
      if (!device) {
        throw new Error('Périphérique audio de sortie non trouvé');
      }

      this.state.currentDevices.audioOutput = device;

      // Appliquer à tous les éléments audio/vidéo actifs
      for (const [id, audio] of this.audioElements) {
        if (audio.setSinkId) {
          await audio.setSinkId(deviceId);
        }
      }

      for (const [id, video] of this.videoElements) {
        if (video.setSinkId) {
          await video.setSinkId(deviceId);
        }
      }

      console.log(`🔊 Périphérique de sortie changé: ${device.label}`);
      this.emit('audioOutputChanged', device);

    } catch (error) {
      console.error('❌ Erreur lors du changement de périphérique de sortie:', error);
      throw error;
    }
  }

  /**
   * Change le périphérique vidéo
   */
  async setVideoInputDevice(deviceId) {
    try {
      const device = this.state.devices.videoInputs.find(d => d.deviceId === deviceId);
      if (!device) {
        throw new Error('Périphérique vidéo non trouvé');
      }

      this.state.currentDevices.videoInput = device;
      console.log(`📹 Périphérique vidéo changé: ${device.label}`);
      this.emit('videoInputChanged', device);

    } catch (error) {
      console.error('❌ Erreur lors du changement de périphérique vidéo:', error);
      throw error;
    }
  }

  /**
   * Contrôle du volume global
   */
  setVolume(volume) {
    this.state.volume = Math.max(0, Math.min(1, volume));

    // Appliquer à tous les éléments audio/vidéo actifs
    for (const [id, audio] of this.audioElements) {
      audio.volume = this.state.volume;
    }

    for (const [id, video] of this.videoElements) {
      video.volume = this.state.volume;
    }

    console.log(`🔊 Volume défini à: ${Math.round(this.state.volume * 100)}%`);
    this.emit('volumeChanged', this.state.volume);
  }

  /**
   * Active/désactive le mode muet
   */
  setMuted(muted) {
    this.state.isMuted = muted;

    // Appliquer à tous les éléments vidéo actifs
    for (const [id, video] of this.videoElements) {
      video.muted = this.state.isMuted;
    }

    // Arrêter la synthèse vocale si muet
    if (muted && 'speechSynthesis' in window) {
      speechSynthesis.cancel();
    }

    console.log(`🔇 Mode muet: ${muted ? 'activé' : 'désactivé'}`);
    this.emit('muteChanged', this.state.isMuted);
  }

  /**
   * Pause/reprend tous les médias
   */
  pauseAllMedia() {
    for (const [id, audio] of this.audioElements) {
      audio.pause();
    }

    for (const [id, video] of this.videoElements) {
      video.pause();
    }

    if ('speechSynthesis' in window) {
      speechSynthesis.pause();
    }

    console.log('⏸️ Tous les médias mis en pause');
    this.emit('allMediaPaused');
  }

  /**
   * Reprend tous les médias
   */
  resumeAllMedia() {
    for (const [id, audio] of this.audioElements) {
      if (audio.paused) {
        audio.play().catch(console.error);
      }
    }

    for (const [id, video] of this.videoElements) {
      if (video.paused) {
        video.play().catch(console.error);
      }
    }

    if ('speechSynthesis' in window) {
      speechSynthesis.resume();
    }

    console.log('▶️ Tous les médias repris');
    this.emit('allMediaResumed');
  }

  /**
   * Arrête tous les médias
   */
  stopAllMedia() {
    // Arrêter tous les audios
    for (const [id, audio] of this.audioElements) {
      audio.pause();
      audio.currentTime = 0;
    }
    this.audioElements.clear();

    // Arrêter toutes les vidéos
    for (const [id, video] of this.videoElements) {
      video.pause();
      video.currentTime = 0;
    }
    this.videoElements.clear();

    // Arrêter la synthèse vocale
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
    }

    // Arrêter la reconnaissance vocale
    this.stopSpeechRecognition();

    // Arrêter l'enregistrement
    this.stopRecording();

    console.log('⏹️ Tous les médias arrêtés');
    this.emit('allMediaStopped');
  }

  /**
   * Obtient l'état actuel du système
   */
  getState() {
    return {
      ...this.state,
      config: this.config,
      availableVoices: this.getAvailableVoices(),
      audioElementsCount: this.audioElements.size,
      videoElementsCount: this.videoElements.size
    };
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    console.log('🧹 Nettoyage du système audio/vidéo...');

    this.stopAllMedia();

    if (this.state.audioContext) {
      this.state.audioContext.close();
      this.state.audioContext = null;
    }

    // Nettoyer les URLs d'objets
    for (const [id, audio] of this.audioElements) {
      if (audio.src.startsWith('blob:')) {
        URL.revokeObjectURL(audio.src);
      }
    }

    for (const [id, video] of this.videoElements) {
      if (video.src.startsWith('blob:')) {
        URL.revokeObjectURL(video.src);
      }
    }

    this.removeAllListeners();

    console.log('✅ Nettoyage terminé');
  }
}

module.exports = AudioVideoSystem;
