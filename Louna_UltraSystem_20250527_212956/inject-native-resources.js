/**
 * Script pour injecter les ressources natives dans toutes les pages HTML
 * Ce script ajoute les fichiers CSS et JavaScript nécessaires pour l'application native
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const readdirAsync = promisify(fs.readdir);
const statAsync = promisify(fs.stat);

// Chemin vers le dossier public
const publicDir = path.join(__dirname, 'public');

// Ressources à injecter
const cssLink = '<link rel="stylesheet" href="css/native-app.css">';
const jsScript = '<script src="js/native-app.js"></script>';

// Fonction pour vérifier si un fichier est un fichier HTML
async function isHtmlFile(filePath) {
  try {
    const stat = await statAsync(filePath);
    return stat.isFile() && path.extname(filePath).toLowerCase() === '.html';
  } catch (error) {
    return false;
  }
}

// Fonction pour trouver tous les fichiers HTML dans un dossier et ses sous-dossiers
async function findHtmlFiles(dir) {
  const htmlFiles = [];
  
  async function scanDir(currentDir) {
    const files = await readdirAsync(currentDir);
    
    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const stat = await statAsync(filePath);
      
      if (stat.isDirectory()) {
        await scanDir(filePath);
      } else if (stat.isFile() && path.extname(file).toLowerCase() === '.html') {
        htmlFiles.push(filePath);
      }
    }
  }
  
  await scanDir(dir);
  return htmlFiles;
}

// Fonction pour injecter les ressources dans un fichier HTML
async function injectResources(filePath) {
  try {
    // Lire le contenu du fichier
    let content = await readFileAsync(filePath, 'utf8');
    
    // Vérifier si les ressources sont déjà injectées
    if (content.includes('native-app.css') || content.includes('native-app.js')) {
      console.log(`Les ressources sont déjà injectées dans ${filePath}`);
      return;
    }
    
    // Injecter le CSS avant la balise </head>
    if (content.includes('</head>')) {
      content = content.replace('</head>', `  ${cssLink}\n</head>`);
    }
    
    // Injecter le JavaScript avant la balise </body>
    if (content.includes('</body>')) {
      content = content.replace('</body>', `  ${jsScript}\n</body>`);
    }
    
    // Écrire le contenu modifié
    await writeFileAsync(filePath, content, 'utf8');
    
    console.log(`Ressources injectées dans ${filePath}`);
  } catch (error) {
    console.error(`Erreur lors de l'injection des ressources dans ${filePath}:`, error);
  }
}

// Fonction principale
async function main() {
  try {
    // Créer les dossiers s'ils n'existent pas
    const cssDir = path.join(publicDir, 'css');
    const jsDir = path.join(publicDir, 'js');
    
    if (!fs.existsSync(cssDir)) {
      fs.mkdirSync(cssDir, { recursive: true });
    }
    
    if (!fs.existsSync(jsDir)) {
      fs.mkdirSync(jsDir, { recursive: true });
    }
    
    // Trouver tous les fichiers HTML
    const htmlFiles = await findHtmlFiles(publicDir);
    
    console.log(`${htmlFiles.length} fichiers HTML trouvés`);
    
    // Injecter les ressources dans chaque fichier HTML
    for (const htmlFile of htmlFiles) {
      await injectResources(htmlFile);
    }
    
    console.log('Injection des ressources terminée');
  } catch (error) {
    console.error('Erreur lors de l\'injection des ressources:', error);
  }
}

// Exécuter la fonction principale
main();
