#!/bin/bash

# Script pour corriger les problèmes de navigation dans Louna
# Ce script corrige les problèmes de navigation entre les interfaces

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"
SIDEBAR_FILE="$APP_DIR/views/partials/luna-sidebar.ejs"
NAVIGATION_JS_FILE="$APP_DIR/public/js/luna-navigation.js"
NAVIGATION_FIX_SCRIPT="$APP_DIR/public/js/fix-navigation.js"
NAVIGATION_FIX_INJECTOR="$APP_DIR/public/js/navigation-fix-injector.js"

# Vérifier si les fichiers existent
if [ ! -f "$SIDEBAR_FILE" ]; then
  print_error "Le fichier de la barre latérale n'existe pas à l'emplacement: $SIDEBAR_FILE"
  exit 1
fi

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "███████╗██╗██╗  ██╗    ██╗      ██████╗ ██╗   ██╗███╗   ██╗ █████╗ "
echo "██╔════╝██║╚██╗██╔╝    ██║     ██╔═══██╗██║   ██║████╗  ██║██╔══██╗"
echo "█████╗  ██║ ╚███╔╝     ██║     ██║   ██║██║   ██║██╔██╗ ██║███████║"
echo "██╔══╝  ██║ ██╔██╗     ██║     ██║   ██║██║   ██║██║╚██╗██║██╔══██║"
echo "██║     ██║██╔╝ ██╗    ███████╗╚██████╔╝╚██████╔╝██║ ╚████║██║  ██║"
echo "╚═╝     ╚═╝╚═╝  ╚═╝    ╚══════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Correction des problèmes de navigation${NC}"
echo ""
print_message "Début de la correction des problèmes de navigation..."
sleep 1

# Étape 1 : Corriger les liens incorrects dans la barre latérale
print_message "Étape 1 : Correction des liens incorrects dans la barre latérale..."

# Sauvegarder une copie du fichier original
cp "$SIDEBAR_FILE" "$SIDEBAR_FILE.bak"
print_message "Sauvegarde du fichier original: $SIDEBAR_FILE.bak"

# Corriger les liens incorrects
sed -i '' 's|href="/louna/|href="/luna/|g' "$SIDEBAR_FILE"
print_success "Liens incorrects corrigés dans la barre latérale."

# Étape 2 : Créer un script de correction de navigation côté client
print_message "Étape 2 : Création d'un script de correction de navigation côté client..."

# Créer le script de correction de navigation
cat > "$NAVIGATION_FIX_SCRIPT" << 'EOL'
/**
 * Script de correction de navigation pour Louna
 * Ce script corrige les problèmes de navigation entre les interfaces
 */

// Fonction pour corriger les liens incorrects
function fixIncorrectLinks() {
  // Sélectionner tous les liens dans la barre latérale
  const sidebarLinks = document.querySelectorAll('.sidebar-nav a');

  // Parcourir tous les liens et corriger les chemins incorrects
  sidebarLinks.forEach(link => {
    const href = link.getAttribute('href');

    // Corriger les liens qui commencent par /louna/ au lieu de /luna/
    if (href && href.startsWith('/louna/')) {
      const correctedHref = href.replace('/louna/', '/luna/');
      link.setAttribute('href', correctedHref);
      console.log(`Lien corrigé: ${href} -> ${correctedHref}`);
    }
  });

  console.log('Correction des liens terminée');
}

// Fonction pour ajouter un gestionnaire d'erreurs de navigation
function addNavigationErrorHandler() {
  // Stocker l'URL actuelle dans le stockage local
  if (!window.location.pathname.includes('/error')) {
    localStorage.setItem('lastValidUrl', window.location.href);
    console.log('URL valide stockée:', window.location.href);
  }

  // Ajouter un gestionnaire d'événements pour les erreurs
  window.addEventListener('error', function(event) {
    console.error('Erreur détectée:', event.error);

    // Vérifier si l'erreur est liée à la navigation
    if (event.error && (
        event.error.message.includes('navigation') ||
        event.error.message.includes('route') ||
        event.error.message.includes('undefined') ||
        event.error.message.includes('Cannot read') ||
        event.error.message.includes('null') ||
        event.error.message.includes('not defined')
      )) {
      console.log('Erreur de navigation détectée, tentative de récupération...');

      // Récupérer la dernière URL valide
      const lastValidUrl = localStorage.getItem('lastValidUrl') || '/luna';

      // Rediriger vers la dernière URL valide
      window.location.href = lastValidUrl;
    }
  });

  console.log('Gestionnaire d\'erreurs de navigation ajouté');
}

// Fonction pour intercepter les clics sur les liens
function interceptLinkClicks() {
  // Intercepter tous les clics sur les liens
  document.addEventListener('click', function(event) {
    // Vérifier si l'élément cliqué est un lien ou est contenu dans un lien
    const link = event.target.closest('a');

    if (link) {
      const href = link.getAttribute('href');

      // Ne pas intercepter les liens externes ou les liens avec des attributs spéciaux
      if (!href || href.startsWith('http') || href.startsWith('#') ||
          link.getAttribute('target') === '_blank' ||
          link.getAttribute('data-no-intercept')) {
        return;
      }

      // Corriger les liens qui commencent par /louna/ au lieu de /luna/
      if (href.startsWith('/louna/')) {
        event.preventDefault();
        const correctedHref = href.replace('/louna/', '/luna/');
        console.log(`Navigation corrigée: ${href} -> ${correctedHref}`);
        window.location.href = correctedHref;
      }

      // Stocker l'URL actuelle dans le stockage local
      localStorage.setItem('lastValidUrl', window.location.href);
      console.log('URL valide stockée (clic):', window.location.href);
    }
  });

  console.log('Interception des clics sur les liens activée');
}

// Exécuter les fonctions lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
  console.log('Script de correction de navigation chargé');

  // Corriger les liens incorrects
  fixIncorrectLinks();

  // Ajouter un gestionnaire d'erreurs de navigation
  addNavigationErrorHandler();

  // Intercepter les clics sur les liens
  interceptLinkClicks();

  console.log('Correction de navigation terminée');
});
EOL

print_success "Script de correction de navigation créé: $NAVIGATION_FIX_SCRIPT"

# Étape 3 : Créer un script d'injection pour le script de correction
print_message "Étape 3 : Création d'un script d'injection pour le script de correction..."

# Créer le script d'injection
cat > "$NAVIGATION_FIX_INJECTOR" << 'EOL'
// Injecter le script de correction de navigation
document.addEventListener('DOMContentLoaded', function() {
  // Créer un élément script
  const script = document.createElement('script');
  script.src = '/js/fix-navigation.js';
  script.async = true;

  // Ajouter le script au document
  document.head.appendChild(script);

  console.log('Script de correction de navigation injecté');
});
EOL

print_success "Script d'injection créé: $NAVIGATION_FIX_INJECTOR"

# Étape 4 : Ajouter le script d'injection à toutes les vues
print_message "Étape 4 : Ajout du script d'injection à toutes les vues..."

# Trouver tous les fichiers de vue
VIEW_FILES=$(find "$APP_DIR/views" -name "*.ejs")

# Ajouter le script d'injection à chaque vue
for file in $VIEW_FILES; do
  # Vérifier si le script est déjà inclus
  if ! grep -q "navigation-fix-injector.js" "$file"; then
    # Ajouter le script avant la fermeture de la balise head
    sed -i '' 's|</head>|  <script src="/js/navigation-fix-injector.js"></script>\n</head>|g' "$file"
    print_message "Script d'injection ajouté à: $file"
  fi
done

print_success "Script d'injection ajouté à toutes les vues."

# Étape 5 : Redémarrer le serveur
print_message "Étape 5 : Redémarrage du serveur..."

# Vérifier si le serveur est en cours d'exécution
if lsof -i:3000 -t &> /dev/null; then
  print_message "Arrêt du serveur en cours..."
  kill -9 $(lsof -i:3000 -t) 2>/dev/null
  sleep 2
  print_success "Serveur arrêté."
fi

print_message "Démarrage du serveur..."
cd "$APP_DIR" || exit

# Lancer le serveur en arrière-plan
nohup node server.js > /dev/null 2>&1 &

# Attendre que le serveur démarre
sleep 3

print_success "Serveur démarré."
print_message "L'application est accessible à l'adresse: http://localhost:3000/louna"

# Ouvrir le navigateur
print_message "Ouverture de l'application dans le navigateur..."
open "http://localhost:3000/louna/system-dashboard"

print_success "Correction des problèmes de navigation terminée !"
print_message "Vous pouvez maintenant naviguer entre les interfaces sans problème."
