#!/bin/bash

# Script pour appliquer l'interface unifiée à toutes les pages de Louna
# Ce script copie les fichiers de template et CSS, puis modifie les routes pour utiliser le template unifié

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"
CSS_DIR="$APP_DIR/public/css"
VIEWS_DIR="$APP_DIR/views"
ROUTES_DIR="$APP_DIR/routes"
JS_DIR="$APP_DIR/public/js"

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "██╗   ██╗███╗   ██╗██╗███████╗██╗███████╗██████╗     ██╗     ██████╗ ██╗   ██╗███╗   ██╗ █████╗ "
echo "██║   ██║████╗  ██║██║██╔════╝██║██╔════╝██╔══██╗    ██║     ██╔══██╗██║   ██║████╗  ██║██╔══██╗"
echo "██║   ██║██╔██╗ ██║██║█████╗  ██║█████╗  ██║  ██║    ██║     ██║  ██║██║   ██║██╔██╗ ██║███████║"
echo "██║   ██║██║╚██╗██║██║██╔══╝  ██║██╔══╝  ██║  ██║    ██║     ██║  ██║██║   ██║██║╚██╗██║██╔══██║"
echo "╚██████╔╝██║ ╚████║██║██║     ██║███████╗██████╔╝    ███████╗██████╔╝╚██████╔╝██║ ╚████║██║  ██║"
echo " ╚═════╝ ╚═╝  ╚═══╝╚═╝╚═╝     ╚═╝╚══════╝╚═════╝     ╚══════╝╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Application de l'interface unifiée à toutes les pages de Louna${NC}"
echo ""
print_message "Début de l'application de l'interface unifiée..."
sleep 1

# Étape 1 : Copier les fichiers CSS et templates
print_message "Étape 1 : Copie des fichiers CSS et templates..."

# Créer les répertoires si nécessaire
mkdir -p "$CSS_DIR"
mkdir -p "$VIEWS_DIR/layouts"
mkdir -p "$VIEWS_DIR/partials"

# Copier le fichier CSS unifié
cp "louna-unified.css" "$CSS_DIR/louna-unified.css"
print_success "Fichier CSS unifié copié: $CSS_DIR/louna-unified.css"

# Copier le template principal
cp "louna-template.ejs" "$VIEWS_DIR/layouts/louna-template.ejs"
print_success "Template principal copié: $VIEWS_DIR/layouts/louna-template.ejs"

# Copier le template du tableau de bord
cp "louna-dashboard.ejs" "$VIEWS_DIR/louna-dashboard.ejs"
print_success "Template du tableau de bord copié: $VIEWS_DIR/louna-dashboard.ejs"

# Étape 2 : Créer un fichier JavaScript pour la navigation unifiée
print_message "Étape 2 : Création d'un fichier JavaScript pour la navigation unifiée..."

cat > "$JS_DIR/louna-navigation.js" << 'EOL'
/**
 * Script de navigation unifiée pour Louna
 * Ce script gère la navigation entre les différentes interfaces de Louna
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('Louna Navigation Script loaded');
  
  // Gérer les clics sur les liens de navigation
  document.querySelectorAll('.nav-item').forEach(function(link) {
    link.addEventListener('click', function(e) {
      // Stocker l'URL actuelle dans le stockage local
      localStorage.setItem('lastValidUrl', window.location.href);
      
      // Ajouter une classe active au lien cliqué
      document.querySelectorAll('.nav-item').forEach(function(item) {
        item.classList.remove('active');
      });
      this.classList.add('active');
    });
  });
  
  // Gérer les erreurs de navigation
  window.addEventListener('error', function(event) {
    console.error('Erreur détectée:', event.error);
    
    // Vérifier si l'erreur est liée à la navigation
    if (event.error && (
        event.error.message.includes('navigation') || 
        event.error.message.includes('route') || 
        event.error.message.includes('undefined')
      )) {
      console.log('Erreur de navigation détectée, tentative de récupération...');
      
      // Récupérer la dernière URL valide
      const lastValidUrl = localStorage.getItem('lastValidUrl') || '/louna';
      
      // Rediriger vers la dernière URL valide
      window.location.href = lastValidUrl;
    }
  });
  
  // Mettre à jour l'horloge en temps réel
  function updateClock() {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    const timeString = `${hours}:${minutes}:${seconds}`;
    
    // Mettre à jour tous les éléments d'horloge
    document.querySelectorAll('.clock-display').forEach(function(clock) {
      clock.textContent = timeString;
    });
    
    // Mettre à jour toutes les dernières mises à jour
    document.querySelectorAll('.last-update-time').forEach(function(element) {
      element.textContent = timeString;
    });
  }
  
  // Mettre à jour l'horloge toutes les secondes
  setInterval(updateClock, 1000);
  updateClock(); // Initialiser l'horloge
  
  // Simuler l'activité du système
  function simulateSystemActivity() {
    // Mettre à jour les barres de progression aléatoirement
    document.querySelectorAll('.progress-fill').forEach(function(bar) {
      // Ne pas modifier les barres qui ont une largeur fixe
      if (!bar.style.width.includes('%')) {
        const currentWidth = parseInt(bar.style.width) || 50;
        const newWidth = Math.max(10, Math.min(95, currentWidth + (Math.random() * 10 - 5)));
        bar.style.width = `${newWidth}%`;
      }
    });
    
    // Mettre à jour les températures des zones aléatoirement
    document.querySelectorAll('.zone-temp').forEach(function(temp) {
      const text = temp.textContent;
      if (text.includes('°C')) {
        const currentTemp = parseInt(text);
        const newTemp = Math.max(1, Math.min(50, currentTemp + (Math.random() * 2 - 1)));
        temp.textContent = `${Math.round(newTemp)}°C`;
      }
    });
  }
  
  // Simuler l'activité du système toutes les 5 secondes
  setInterval(simulateSystemActivity, 5000);
});
EOL

print_success "Fichier JavaScript pour la navigation unifiée créé: $JS_DIR/louna-navigation.js"

# Étape 3 : Créer une route pour le tableau de bord système
print_message "Étape 3 : Création d'une route pour le tableau de bord système..."

cat > "$ROUTES_DIR/louna-dashboard.js" << 'EOL'
/**
 * Routes pour le tableau de bord système de Louna
 */

const express = require('express');
const router = express.Router();

// Route pour le tableau de bord système
router.get('/', (req, res) => {
  res.render('louna-dashboard', {
    title: 'Tableau de Bord Système',
    activePage: 'home',
    layout: 'layouts/louna-template'
  });
});

module.exports = router;
EOL

print_success "Route pour le tableau de bord système créée: $ROUTES_DIR/louna-dashboard.js"

# Étape 4 : Modifier le fichier server.js pour utiliser la nouvelle route
print_message "Étape 4 : Modification du fichier server.js pour utiliser la nouvelle route..."

# Vérifier si la route est déjà incluse
if ! grep -q "lounaDashboardRouter" "$APP_DIR/server.js"; then
  # Trouver la ligne après laquelle ajouter la nouvelle route
  ROUTES_LINE=$(grep -n "const.*Router" "$APP_DIR/server.js" | tail -1 | cut -d: -f1)
  
  # Ajouter la nouvelle route après la dernière ligne de route
  sed -i '' "${ROUTES_LINE}a\\
const lounaDashboardRouter = require('./routes/louna-dashboard');\\
" "$APP_DIR/server.js"
  
  # Trouver la ligne après laquelle ajouter l'utilisation de la route
  USE_LINE=$(grep -n "app.use.*router" "$APP_DIR/server.js" | tail -1 | cut -d: -f1)
  
  # Ajouter l'utilisation de la route après la dernière ligne d'utilisation
  sed -i '' "${USE_LINE}a\\
app.use('/louna', lounaDashboardRouter);\\
" "$APP_DIR/server.js"
  
  print_success "Fichier server.js modifié pour utiliser la nouvelle route."
else
  print_message "La route est déjà incluse dans le fichier server.js."
fi

# Étape 5 : Créer un script pour rediriger /luna vers /louna
print_message "Étape 5 : Création d'un script pour rediriger /luna vers /louna..."

# Vérifier si la redirection est déjà incluse
if ! grep -q "app.get('/luna'" "$APP_DIR/server.js"; then
  # Trouver la ligne après laquelle ajouter la redirection
  REDIRECT_LINE=$(grep -n "app.use" "$APP_DIR/server.js" | tail -1 | cut -d: -f1)
  
  # Ajouter la redirection après la dernière ligne d'utilisation
  sed -i '' "${REDIRECT_LINE}a\\
// Rediriger /luna vers /louna\\
app.get('/luna', (req, res) => {\\
  res.redirect('/louna');\\
});\\
\\
app.get('/luna/:path', (req, res) => {\\
  res.redirect(\`/louna/\${req.params.path}\`);\\
});\\
\\
app.get('/luna/:path/:subpath', (req, res) => {\\
  res.redirect(\`/louna/\${req.params.path}/\${req.params.subpath}\`);\\
});\\
" "$APP_DIR/server.js"
  
  print_success "Redirection de /luna vers /louna ajoutée au fichier server.js."
else
  print_message "La redirection est déjà incluse dans le fichier server.js."
fi

# Étape 6 : Redémarrer le serveur
print_message "Étape 6 : Redémarrage du serveur..."

# Vérifier si le serveur est en cours d'exécution
if lsof -i:3000 -t &> /dev/null; then
  print_message "Arrêt du serveur en cours..."
  kill -9 $(lsof -i:3000 -t) 2>/dev/null
  sleep 2
  print_success "Serveur arrêté."
fi

print_message "Démarrage du serveur..."
cd "$APP_DIR" || exit

# Lancer le serveur en arrière-plan
nohup node server.js > /dev/null 2>&1 &

# Attendre que le serveur démarre
sleep 3

print_success "Serveur démarré."
print_message "L'application est accessible à l'adresse: http://localhost:3000/louna"

# Ouvrir le navigateur
print_message "Ouverture de l'application dans le navigateur..."
open "http://localhost:3000/louna"

print_success "Application de l'interface unifiée terminée !"
print_message "Toutes les interfaces ont maintenant le même aspect visuel et sont bien connectées les unes aux autres."
