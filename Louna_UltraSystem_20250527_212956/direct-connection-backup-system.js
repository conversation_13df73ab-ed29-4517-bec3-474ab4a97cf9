/**
 * SYSTÈME DE SAUVEGARDE ET RESTAURATION POUR LA CONNEXION DIRECTE
 * 
 * Ce module gère la sauvegarde et la restauration du système de connexion directe
 * pour assurer la fiabilité et la récupération en cas de problème
 */

const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

class DirectConnectionBackupSystem extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.config = {
            backupDir: options.backupDir || path.join(__dirname, 'backups', 'direct-connection'),
            maxBackups: options.maxBackups || 10,
            autoBackupInterval: options.autoBackupInterval || 300000, // 5 minutes
            compressionEnabled: options.compressionEnabled || true,
            encryptionEnabled: options.encryptionEnabled || false
        };
        
        this.backupHistory = [];
        this.autoBackupTimer = null;
        
        this.log('🔄 Système de sauvegarde de connexion directe initialisé');
        this.initializeBackupSystem();
    }
    
    /**
     * Initialise le système de sauvegarde
     */
    async initializeBackupSystem() {
        try {
            // Créer le répertoire de sauvegarde
            await this.ensureBackupDirectory();
            
            // Charger l'historique des sauvegardes
            await this.loadBackupHistory();
            
            // Démarrer les sauvegardes automatiques
            this.startAutoBackup();
            
            // Créer une sauvegarde initiale
            await this.createBackup('initial_backup');
            
            this.log('✅ Système de sauvegarde initialisé avec succès');
            
        } catch (error) {
            this.log(`❌ Erreur lors de l'initialisation: ${error.message}`);
        }
    }
    
    /**
     * Assure que le répertoire de sauvegarde existe
     */
    async ensureBackupDirectory() {
        if (!fs.existsSync(this.config.backupDir)) {
            fs.mkdirSync(this.config.backupDir, { recursive: true });
            this.log(`📁 Répertoire de sauvegarde créé: ${this.config.backupDir}`);
        }
    }
    
    /**
     * Charge l'historique des sauvegardes
     */
    async loadBackupHistory() {
        const historyFile = path.join(this.config.backupDir, 'backup-history.json');
        
        if (fs.existsSync(historyFile)) {
            try {
                const historyData = fs.readFileSync(historyFile, 'utf8');
                this.backupHistory = JSON.parse(historyData);
                this.log(`📚 Historique chargé: ${this.backupHistory.length} sauvegardes`);
            } catch (error) {
                this.log(`⚠️ Erreur lors du chargement de l'historique: ${error.message}`);
                this.backupHistory = [];
            }
        }
    }
    
    /**
     * Sauvegarde l'historique des sauvegardes
     */
    async saveBackupHistory() {
        const historyFile = path.join(this.config.backupDir, 'backup-history.json');
        
        try {
            fs.writeFileSync(historyFile, JSON.stringify(this.backupHistory, null, 2));
        } catch (error) {
            this.log(`❌ Erreur lors de la sauvegarde de l'historique: ${error.message}`);
        }
    }
    
    /**
     * Démarre les sauvegardes automatiques
     */
    startAutoBackup() {
        if (this.autoBackupTimer) {
            clearInterval(this.autoBackupTimer);
        }
        
        this.autoBackupTimer = setInterval(async () => {
            await this.createBackup('auto_backup');
        }, this.config.autoBackupInterval);
        
        this.log(`⏰ Sauvegardes automatiques démarrées (${this.config.autoBackupInterval / 1000}s)`);
    }
    
    /**
     * Arrête les sauvegardes automatiques
     */
    stopAutoBackup() {
        if (this.autoBackupTimer) {
            clearInterval(this.autoBackupTimer);
            this.autoBackupTimer = null;
            this.log('⏹️ Sauvegardes automatiques arrêtées');
        }
    }
    
    /**
     * Crée une sauvegarde complète du système
     */
    async createBackup(type = 'manual', description = '') {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupId = `${type}_${timestamp}`;
        
        try {
            this.log(`💾 Création de la sauvegarde: ${backupId}`);
            
            // Collecter les données du système
            const systemData = await this.collectSystemData();
            
            // Créer la sauvegarde
            const backupPath = path.join(this.config.backupDir, `${backupId}.json`);
            const backupData = {
                id: backupId,
                type: type,
                description: description,
                timestamp: new Date().toISOString(),
                version: '1.0.0',
                data: systemData
            };
            
            // Sauvegarder les données
            fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));
            
            // Ajouter à l'historique
            this.backupHistory.unshift({
                id: backupId,
                type: type,
                description: description,
                timestamp: backupData.timestamp,
                path: backupPath,
                size: fs.statSync(backupPath).size
            });
            
            // Limiter le nombre de sauvegardes
            await this.cleanupOldBackups();
            
            // Sauvegarder l'historique
            await this.saveBackupHistory();
            
            this.log(`✅ Sauvegarde créée: ${backupId}`);
            this.emit('backupCreated', { id: backupId, type: type });
            
            return backupId;
            
        } catch (error) {
            this.log(`❌ Erreur lors de la création de sauvegarde: ${error.message}`);
            this.emit('backupError', { error: error.message, type: type });
            throw error;
        }
    }
    
    /**
     * Collecte les données du système à sauvegarder
     */
    async collectSystemData() {
        const systemData = {
            // Configuration de connexion directe
            directConnection: {
                config: global.directConnection ? global.directConnection.config : null,
                stats: global.directConnection ? global.directConnection.getStats() : null
            },
            
            // Configuration de l'optimiseur de vitesse
            speedOptimizer: {
                config: global.speedOptimizer ? global.speedOptimizer.config : null,
                stats: global.speedOptimizer ? global.speedOptimizer.getPerformanceStats() : null
            },
            
            // État du système cognitif
            cognitiveSystem: {
                available: !!global.cognitiveSystem,
                agents: global.cognitiveSystem ? Object.keys(global.cognitiveSystem.agents || {}) : []
            },
            
            // Variables d'environnement (sans les clés sensibles)
            environment: {
                hasOpenAIKey: !!process.env.OPENAI_API_KEY,
                hasAnthropicKey: !!process.env.ANTHROPIC_API_KEY,
                hasDeepSeekKey: !!process.env.DEEPSEEK_API_KEY,
                nodeVersion: process.version,
                platform: process.platform
            },
            
            // Informations système
            system: {
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                timestamp: new Date().toISOString()
            }
        };
        
        return systemData;
    }
    
    /**
     * Nettoie les anciennes sauvegardes
     */
    async cleanupOldBackups() {
        if (this.backupHistory.length > this.config.maxBackups) {
            const toDelete = this.backupHistory.slice(this.config.maxBackups);
            
            for (const backup of toDelete) {
                try {
                    if (fs.existsSync(backup.path)) {
                        fs.unlinkSync(backup.path);
                        this.log(`🗑️ Ancienne sauvegarde supprimée: ${backup.id}`);
                    }
                } catch (error) {
                    this.log(`⚠️ Erreur lors de la suppression: ${error.message}`);
                }
            }
            
            this.backupHistory = this.backupHistory.slice(0, this.config.maxBackups);
        }
    }
    
    /**
     * Restaure le système à partir d'une sauvegarde
     */
    async restoreFromBackup(backupId) {
        try {
            this.log(`🔄 Restauration depuis: ${backupId}`);
            
            // Trouver la sauvegarde
            const backup = this.backupHistory.find(b => b.id === backupId);
            if (!backup) {
                throw new Error(`Sauvegarde non trouvée: ${backupId}`);
            }
            
            // Charger les données de sauvegarde
            const backupData = JSON.parse(fs.readFileSync(backup.path, 'utf8'));
            
            // Créer une sauvegarde de l'état actuel avant restauration
            await this.createBackup('pre_restore', `Avant restauration de ${backupId}`);
            
            // Restaurer les configurations
            await this.restoreSystemData(backupData.data);
            
            this.log(`✅ Restauration terminée: ${backupId}`);
            this.emit('backupRestored', { id: backupId });
            
            return true;
            
        } catch (error) {
            this.log(`❌ Erreur lors de la restauration: ${error.message}`);
            this.emit('restoreError', { error: error.message, backupId: backupId });
            throw error;
        }
    }
    
    /**
     * Restaure les données système
     */
    async restoreSystemData(data) {
        // Restaurer la configuration de connexion directe
        if (data.directConnection && global.directConnection) {
            if (data.directConnection.config) {
                Object.assign(global.directConnection.config, data.directConnection.config);
                this.log('🔧 Configuration de connexion directe restaurée');
            }
        }
        
        // Restaurer la configuration de l'optimiseur
        if (data.speedOptimizer && global.speedOptimizer) {
            if (data.speedOptimizer.config) {
                Object.assign(global.speedOptimizer.config, data.speedOptimizer.config);
                this.log('⚡ Configuration de l\'optimiseur restaurée');
            }
        }
        
        // Réinitialiser les connexions si nécessaire
        if (global.directConnection && global.directConnection.initializeAPIs) {
            await global.directConnection.initializeAPIs();
            this.log('🔄 APIs réinitialisées');
        }
    }
    
    /**
     * Obtient la liste des sauvegardes disponibles
     */
    getAvailableBackups() {
        return this.backupHistory.map(backup => ({
            id: backup.id,
            type: backup.type,
            description: backup.description,
            timestamp: backup.timestamp,
            size: backup.size,
            sizeFormatted: this.formatFileSize(backup.size)
        }));
    }
    
    /**
     * Supprime une sauvegarde spécifique
     */
    async deleteBackup(backupId) {
        try {
            const backupIndex = this.backupHistory.findIndex(b => b.id === backupId);
            if (backupIndex === -1) {
                throw new Error(`Sauvegarde non trouvée: ${backupId}`);
            }
            
            const backup = this.backupHistory[backupIndex];
            
            // Supprimer le fichier
            if (fs.existsSync(backup.path)) {
                fs.unlinkSync(backup.path);
            }
            
            // Supprimer de l'historique
            this.backupHistory.splice(backupIndex, 1);
            
            // Sauvegarder l'historique
            await this.saveBackupHistory();
            
            this.log(`🗑️ Sauvegarde supprimée: ${backupId}`);
            this.emit('backupDeleted', { id: backupId });
            
            return true;
            
        } catch (error) {
            this.log(`❌ Erreur lors de la suppression: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * Exporte une sauvegarde
     */
    async exportBackup(backupId, exportPath) {
        try {
            const backup = this.backupHistory.find(b => b.id === backupId);
            if (!backup) {
                throw new Error(`Sauvegarde non trouvée: ${backupId}`);
            }
            
            // Copier le fichier de sauvegarde
            fs.copyFileSync(backup.path, exportPath);
            
            this.log(`📤 Sauvegarde exportée: ${backupId} -> ${exportPath}`);
            return true;
            
        } catch (error) {
            this.log(`❌ Erreur lors de l'export: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * Importe une sauvegarde
     */
    async importBackup(importPath, description = 'Sauvegarde importée') {
        try {
            if (!fs.existsSync(importPath)) {
                throw new Error(`Fichier non trouvé: ${importPath}`);
            }
            
            // Valider le fichier de sauvegarde
            const backupData = JSON.parse(fs.readFileSync(importPath, 'utf8'));
            if (!backupData.id || !backupData.data) {
                throw new Error('Format de sauvegarde invalide');
            }
            
            // Créer un nouvel ID
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const newId = `imported_${timestamp}`;
            
            // Copier vers le répertoire de sauvegarde
            const newPath = path.join(this.config.backupDir, `${newId}.json`);
            backupData.id = newId;
            backupData.description = description;
            backupData.importedAt = new Date().toISOString();
            
            fs.writeFileSync(newPath, JSON.stringify(backupData, null, 2));
            
            // Ajouter à l'historique
            this.backupHistory.unshift({
                id: newId,
                type: 'imported',
                description: description,
                timestamp: backupData.timestamp,
                path: newPath,
                size: fs.statSync(newPath).size
            });
            
            await this.saveBackupHistory();
            
            this.log(`📥 Sauvegarde importée: ${newId}`);
            this.emit('backupImported', { id: newId });
            
            return newId;
            
        } catch (error) {
            this.log(`❌ Erreur lors de l'import: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * Obtient les statistiques du système de sauvegarde
     */
    getBackupStats() {
        const totalSize = this.backupHistory.reduce((sum, backup) => sum + backup.size, 0);
        const typeStats = {};
        
        this.backupHistory.forEach(backup => {
            typeStats[backup.type] = (typeStats[backup.type] || 0) + 1;
        });
        
        return {
            totalBackups: this.backupHistory.length,
            totalSize: totalSize,
            totalSizeFormatted: this.formatFileSize(totalSize),
            typeStats: typeStats,
            oldestBackup: this.backupHistory[this.backupHistory.length - 1]?.timestamp,
            newestBackup: this.backupHistory[0]?.timestamp,
            autoBackupEnabled: !!this.autoBackupTimer
        };
    }
    
    /**
     * Formate la taille de fichier
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * Arrête le système de sauvegarde
     */
    shutdown() {
        this.stopAutoBackup();
        this.log('🛑 Système de sauvegarde arrêté');
    }
    
    /**
     * Log avec timestamp
     */
    log(message) {
        console.log(`[DirectBackup] ${new Date().toISOString()} ${message}`);
    }
}

module.exports = DirectConnectionBackupSystem;
