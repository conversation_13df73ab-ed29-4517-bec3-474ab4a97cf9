#!/bin/bash

# Louna Force Reload - Script de lancement avec rechargement forcé de l'interface
# Force le rechargement complet de l'interface moderne

echo "🔄 Lancement de Louna avec rechargement forcé de l'interface..."

# Aller dans le répertoire de l'application
cd "/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933"

# Vider le cache npm si nécessaire
echo "🗑️ Nettoyage du cache..."
npm cache clean --force 2>/dev/null || true

# Définir les variables d'environnement pour forcer le rechargement
export FORCE_RELOAD=true
export CACHE_VERSION=2025
export MODERN_INTERFACE=true

echo "🎨 Interface moderne forcée activée"
echo "🚀 Lancement de l'application Electron..."

# Lancer l'application Electron avec rechargement forcé
npm run electron

echo "📱 Application fermée"
echo "✅ Session terminée"
