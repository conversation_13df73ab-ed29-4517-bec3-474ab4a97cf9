{"id": "reasoning_training", "name": "Formation au Raisonnement", "description": "Dataset pour améliorer les capacités de raisonnement", "data": [{"input": "Si A > B et B > C, que peut-on dire de A et C ?", "expectedOutput": "Si A > B et B > C, alors A > C (propriété de transitivité)."}, {"input": "Résoudre: 2x + 5 = 13", "expectedOutput": "2x = 13 - 5 = 8, donc x = 4"}, {"input": "Quelle est la logique derrière la suite: 2, 4, 8, 16, ?", "expectedOutput": "Chaque nombre est multiplié par 2. Le suivant est 32."}], "createdAt": "2025-05-27T17:36:16.080Z", "updatedAt": "2025-05-27T17:36:16.080Z"}