#!/usr/bin/env node

/**
 * SCRIPT DE CORRECTION DE LA COMMUNICATION AGENT
 * Corrige les problèmes de routes qui empêchent la communication avec l'agent
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

console.log('🔧 CORRECTION DE LA COMMUNICATION AGENT LOUNA');
console.log('==============================================');

async function testAgentAPI() {
    console.log('\n1. Test des APIs d\'agent...');

    const tests = [
        { name: 'API Agents Status', url: 'http://localhost:3000/api/agents/status' },
        { name: 'API Chat Agent Status', url: 'http://localhost:3000/api/chat/agent/status' },
        { name: 'API MCP Agent Status', url: 'http://localhost:3000/api/mcp/agent/status' }
    ];

    for (const test of tests) {
        try {
            console.log(`\n🔍 Test: ${test.name}`);
            const response = await axios.get(test.url, { timeout: 5000 });

            if (response.data.success) {
                console.log(`✅ ${test.name} - OK`);
                console.log(`   Données: ${JSON.stringify(response.data).substring(0, 100)}...`);
            } else {
                console.log(`❌ ${test.name} - Échec: ${response.data.error}`);
            }
        } catch (error) {
            console.log(`❌ ${test.name} - Erreur: ${error.message}`);
        }
    }
}

async function fixRouteConflicts() {
    console.log('\n2. Correction des conflits de routes...');

    const serverPath = path.join(__dirname, 'server.js');

    if (!fs.existsSync(serverPath)) {
        console.log('❌ Fichier server.js non trouvé');
        return false;
    }

    let serverContent = fs.readFileSync(serverPath, 'utf8');

    // Vérifier s'il y a des routes qui peuvent interférer
    const problematicRoutes = [
        '/api/mcp/agent/status',
        '/api/cognitive/status'
    ];

    let hasConflicts = false;

    for (const route of problematicRoutes) {
        if (serverContent.includes(route)) {
            console.log(`⚠️ Route potentiellement conflictuelle trouvée: ${route}`);
            hasConflicts = true;
        }
    }

    if (hasConflicts) {
        console.log('\n📝 Ajout de logs de debug pour identifier les conflits...');

        // Ajouter un middleware de debug pour tracer les routes
        const debugMiddleware = `
// MIDDLEWARE DE DEBUG POUR TRACER LES ROUTES
app.use((req, res, next) => {
    if (req.path.includes('/api/agents/status') || req.path.includes('/agent/status')) {
        console.log('🔍 [ROUTE DEBUG]', {
            method: req.method,
            path: req.path,
            url: req.url,
            timestamp: new Date().toISOString()
        });
    }
    next();
});

`;

        // Insérer le middleware après les imports
        const insertPoint = serverContent.indexOf('// Utiliser les routes');
        if (insertPoint !== -1) {
            serverContent = serverContent.slice(0, insertPoint) + debugMiddleware + serverContent.slice(insertPoint);

            fs.writeFileSync(serverPath, serverContent);
            console.log('✅ Middleware de debug ajouté');
            return true;
        }
    }

    return false;
}

async function createSimpleAgentTest() {
    console.log('\n3. Création d\'un test simple d\'agent...');

    const testPath = path.join(__dirname, 'simple-agent-test.js');

    const testContent = `const axios = require('axios');

async function testAgent() {
    console.log('🤖 Test simple de l\\'agent...');

    try {
        // Test direct de l'API agents/status
        const response = await axios.get('http://localhost:3000/api/agents/status');
        console.log('✅ Réponse reçue:', response.data);

        // Test de chat si l'agent est disponible
        if (response.data.success) {
            console.log('\\n💬 Test de chat...');
            const chatResponse = await axios.post('http://localhost:3000/api/chat/message', {
                message: 'Bonjour, comment allez-vous ?',
                conversationId: 'test_' + Date.now()
            });

            if (chatResponse.data.success) {
                console.log('✅ Chat fonctionne:', chatResponse.data.response);
            } else {
                console.log('❌ Chat échoué:', chatResponse.data.error);
            }
        }

    } catch (error) {
        console.log('❌ Erreur:', error.message);

        // Afficher plus de détails sur l'erreur
        if (error.response) {
            console.log('   Status:', error.response.status);
            console.log('   Data:', error.response.data);
        }
    }
}

testAgent();`;

    fs.writeFileSync(testPath, testContent);
    console.log(`✅ Test simple créé: ${testPath}`);
}

async function checkAgentManager() {
    console.log('\n4. Vérification du gestionnaire d\'agents...');

    const agentManagerPath = path.join(__dirname, 'agent-manager.js');

    if (fs.existsSync(agentManagerPath)) {
        console.log('✅ agent-manager.js trouvé');

        // Vérifier la configuration des agents
        const agentsConfigPath = path.join(__dirname, 'data', 'config', 'agents.json');

        if (fs.existsSync(agentsConfigPath)) {
            try {
                const agentsConfig = JSON.parse(fs.readFileSync(agentsConfigPath, 'utf8'));
                console.log('✅ Configuration des agents trouvée');
                console.log(`   Agents configurés: ${Object.keys(agentsConfig.agents || {}).length}`);
                console.log(`   Agent par défaut: ${agentsConfig.defaultAgent || 'Non défini'}`);

                // Afficher les agents disponibles
                if (agentsConfig.agents) {
                    for (const [id, agent] of Object.entries(agentsConfig.agents)) {
                        console.log(`   - ${id}: ${agent.name} (${agent.model})`);
                    }
                }
            } catch (error) {
                console.log('❌ Erreur lecture configuration agents:', error.message);
            }
        } else {
            console.log('❌ Configuration des agents non trouvée');
        }
    } else {
        console.log('❌ agent-manager.js non trouvé');
    }
}

async function restartRecommendation() {
    console.log('\n5. Recommandations...');

    console.log('📋 Pour corriger la communication avec l\'agent:');
    console.log('');
    console.log('1. 🔄 Redémarrez le serveur pour appliquer les corrections');
    console.log('2. 🧪 Exécutez le test simple: node simple-agent-test.js');
    console.log('3. 🔍 Vérifiez les logs pour identifier les conflits de routes');
    console.log('4. 🎯 Si le problème persiste, vérifiez l\'ordre des routes dans server.js');
    console.log('');
    console.log('🚀 Une fois corrigé, nous pourrons restaurer toutes vos fonctionnalités:');
    console.log('   - 🎥 LTX Video (génération vidéo en live)');
    console.log('   - 🎨 Génération d\'images/3D illimitée');
    console.log('   - 🎤 Reconnaissance vocale et synthèse');
    console.log('   - 📺 Analyse vidéo YouTube');
    console.log('   - 🧠 Mémoire thermique complète');
    console.log('   - ⚡ Accélérateurs KYBER');
}

async function main() {
    try {
        await testAgentAPI();
        await fixRouteConflicts();
        await createSimpleAgentTest();
        await checkAgentManager();
        await restartRecommendation();

        console.log('\n✅ Correction terminée !');
        console.log('🔄 Redémarrez maintenant le serveur et testez avec: node simple-agent-test.js');

    } catch (error) {
        console.error('❌ Erreur lors de la correction:', error);
    }
}

main();
