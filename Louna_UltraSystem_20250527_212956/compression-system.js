/**
 * Système de Compression/Décompression Avancé pour Louna
 * Support multiple formats avec optimisation intelligente
 */

const fs = require('fs').promises;
const path = require('path');
const zlib = require('zlib');
const { promisify } = require('util');
const crypto = require('crypto');

// Promisifier les méthodes zlib
const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);
const deflate = promisify(zlib.deflate);
const inflate = promisify(zlib.inflate);
const brotliCompress = promisify(zlib.brotliCompress);
const brotliDecompress = promisify(zlib.brotliDecompress);

class AdvancedCompressionSystem {
    constructor() {
        this.algorithms = {
            gzip: {
                name: 'GZIP',
                compress: gzip,
                decompress: gunzip,
                extension: '.gz',
                level: 6,
                bestFor: ['text', 'json', 'xml', 'html']
            },
            deflate: {
                name: 'DEFLATE',
                compress: deflate,
                decompress: inflate,
                extension: '.def',
                level: 6,
                bestFor: ['small_files', 'quick_compression']
            },
            brotli: {
                name: 'BROTLI',
                compress: brotliCompress,
                decompress: brotliDecompress,
                extension: '.br',
                level: 6,
                bestFor: ['web_content', 'modern_browsers']
            }
        };

        this.stats = {
            totalCompressions: 0,
            totalDecompressions: 0,
            totalBytesSaved: 0,
            averageCompressionRatio: 0,
            processingTime: 0
        };

        this.compressionHistory = [];
        this.activeJobs = new Map();
        
        this.initializeSystem();
    }

    /**
     * Initialise le système de compression
     */
    async initializeSystem() {
        console.log('🗜️ Initialisation du système de compression avancé...');
        
        // Créer les répertoires nécessaires
        await this.createDirectories();
        
        // Charger l'historique
        await this.loadHistory();
        
        console.log('✅ Système de compression initialisé');
    }

    /**
     * Crée les répertoires nécessaires
     */
    async createDirectories() {
        const dirs = [
            path.join(__dirname, 'compressed'),
            path.join(__dirname, 'temp-compression'),
            path.join(__dirname, 'compression-logs')
        ];

        for (const dir of dirs) {
            try {
                await fs.mkdir(dir, { recursive: true });
            } catch (error) {
                if (error.code !== 'EEXIST') {
                    console.error(`Erreur création répertoire ${dir}:`, error);
                }
            }
        }
    }

    /**
     * Compresse un fichier avec l'algorithme optimal
     */
    async compressFile(inputPath, outputPath = null, algorithm = null) {
        const startTime = Date.now();
        const jobId = crypto.randomUUID();
        
        try {
            // Analyser le fichier pour choisir l'algorithme optimal
            const fileInfo = await this.analyzeFile(inputPath);
            const selectedAlgorithm = algorithm || this.selectOptimalAlgorithm(fileInfo);
            
            // Créer le job de compression
            const job = {
                id: jobId,
                type: 'compression',
                inputPath,
                outputPath,
                algorithm: selectedAlgorithm,
                startTime,
                status: 'processing',
                progress: 0
            };
            
            this.activeJobs.set(jobId, job);
            
            // Lire le fichier
            const inputData = await fs.readFile(inputPath);
            job.originalSize = inputData.length;
            job.progress = 25;
            
            // Compresser
            const compressedData = await this.algorithms[selectedAlgorithm].compress(inputData, {
                level: this.algorithms[selectedAlgorithm].level
            });
            job.compressedSize = compressedData.length;
            job.progress = 75;
            
            // Générer le chemin de sortie si non spécifié
            if (!outputPath) {
                const ext = this.algorithms[selectedAlgorithm].extension;
                outputPath = inputPath + ext;
            }
            
            // Écrire le fichier compressé
            await fs.writeFile(outputPath, compressedData);
            job.outputPath = outputPath;
            job.progress = 100;
            
            // Calculer les statistiques
            const compressionRatio = (1 - compressedData.length / inputData.length) * 100;
            const processingTime = Date.now() - startTime;
            
            job.status = 'completed';
            job.compressionRatio = compressionRatio;
            job.processingTime = processingTime;
            job.bytesSaved = inputData.length - compressedData.length;
            
            // Mettre à jour les statistiques globales
            this.updateStats(job);
            
            // Ajouter à l'historique
            this.compressionHistory.push({
                ...job,
                timestamp: new Date().toISOString()
            });
            
            // Nettoyer le job actif
            this.activeJobs.delete(jobId);
            
            console.log(`✅ Compression réussie: ${inputPath} -> ${outputPath}`);
            console.log(`📊 Ratio: ${compressionRatio.toFixed(2)}%, Taille: ${inputData.length} -> ${compressedData.length} bytes`);
            
            return {
                success: true,
                jobId,
                inputPath,
                outputPath,
                algorithm: selectedAlgorithm,
                originalSize: inputData.length,
                compressedSize: compressedData.length,
                compressionRatio,
                bytesSaved: inputData.length - compressedData.length,
                processingTime
            };
            
        } catch (error) {
            const job = this.activeJobs.get(jobId);
            if (job) {
                job.status = 'error';
                job.error = error.message;
                this.activeJobs.delete(jobId);
            }
            
            console.error(`❌ Erreur compression ${inputPath}:`, error);
            throw error;
        }
    }

    /**
     * Décompresse un fichier
     */
    async decompressFile(inputPath, outputPath = null, algorithm = null) {
        const startTime = Date.now();
        const jobId = crypto.randomUUID();
        
        try {
            // Détecter l'algorithme si non spécifié
            const detectedAlgorithm = algorithm || this.detectAlgorithm(inputPath);
            
            if (!detectedAlgorithm || !this.algorithms[detectedAlgorithm]) {
                throw new Error(`Algorithme de décompression non supporté: ${detectedAlgorithm}`);
            }
            
            // Créer le job de décompression
            const job = {
                id: jobId,
                type: 'decompression',
                inputPath,
                outputPath,
                algorithm: detectedAlgorithm,
                startTime,
                status: 'processing',
                progress: 0
            };
            
            this.activeJobs.set(jobId, job);
            
            // Lire le fichier compressé
            const compressedData = await fs.readFile(inputPath);
            job.compressedSize = compressedData.length;
            job.progress = 25;
            
            // Décompresser
            const decompressedData = await this.algorithms[detectedAlgorithm].decompress(compressedData);
            job.originalSize = decompressedData.length;
            job.progress = 75;
            
            // Générer le chemin de sortie si non spécifié
            if (!outputPath) {
                const ext = this.algorithms[detectedAlgorithm].extension;
                outputPath = inputPath.replace(ext, '');
            }
            
            // Écrire le fichier décompressé
            await fs.writeFile(outputPath, decompressedData);
            job.outputPath = outputPath;
            job.progress = 100;
            
            const processingTime = Date.now() - startTime;
            job.status = 'completed';
            job.processingTime = processingTime;
            
            // Mettre à jour les statistiques
            this.stats.totalDecompressions++;
            this.stats.processingTime += processingTime;
            
            // Ajouter à l'historique
            this.compressionHistory.push({
                ...job,
                timestamp: new Date().toISOString()
            });
            
            // Nettoyer le job actif
            this.activeJobs.delete(jobId);
            
            console.log(`✅ Décompression réussie: ${inputPath} -> ${outputPath}`);
            
            return {
                success: true,
                jobId,
                inputPath,
                outputPath,
                algorithm: detectedAlgorithm,
                compressedSize: compressedData.length,
                originalSize: decompressedData.length,
                processingTime
            };
            
        } catch (error) {
            const job = this.activeJobs.get(jobId);
            if (job) {
                job.status = 'error';
                job.error = error.message;
                this.activeJobs.delete(jobId);
            }
            
            console.error(`❌ Erreur décompression ${inputPath}:`, error);
            throw error;
        }
    }

    /**
     * Compresse des données en mémoire
     */
    async compressData(data, algorithm = 'gzip') {
        if (!this.algorithms[algorithm]) {
            throw new Error(`Algorithme non supporté: ${algorithm}`);
        }

        const startTime = Date.now();
        const inputBuffer = Buffer.isBuffer(data) ? data : Buffer.from(data, 'utf8');
        
        try {
            const compressedData = await this.algorithms[algorithm].compress(inputBuffer);
            const processingTime = Date.now() - startTime;
            const compressionRatio = (1 - compressedData.length / inputBuffer.length) * 100;
            
            return {
                success: true,
                data: compressedData,
                algorithm,
                originalSize: inputBuffer.length,
                compressedSize: compressedData.length,
                compressionRatio,
                processingTime
            };
        } catch (error) {
            console.error(`Erreur compression données:`, error);
            throw error;
        }
    }

    /**
     * Décompresse des données en mémoire
     */
    async decompressData(compressedData, algorithm = 'gzip') {
        if (!this.algorithms[algorithm]) {
            throw new Error(`Algorithme non supporté: ${algorithm}`);
        }

        const startTime = Date.now();
        const inputBuffer = Buffer.isBuffer(compressedData) ? compressedData : Buffer.from(compressedData);
        
        try {
            const decompressedData = await this.algorithms[algorithm].decompress(inputBuffer);
            const processingTime = Date.now() - startTime;
            
            return {
                success: true,
                data: decompressedData,
                algorithm,
                compressedSize: inputBuffer.length,
                originalSize: decompressedData.length,
                processingTime
            };
        } catch (error) {
            console.error(`Erreur décompression données:`, error);
            throw error;
        }
    }

    /**
     * Analyse un fichier pour déterminer ses caractéristiques
     */
    async analyzeFile(filePath) {
        try {
            const stats = await fs.stat(filePath);
            const extension = path.extname(filePath).toLowerCase();
            
            // Lire un échantillon pour analyser le contenu
            const sampleSize = Math.min(1024, stats.size);
            const fileHandle = await fs.open(filePath, 'r');
            const buffer = Buffer.alloc(sampleSize);
            await fileHandle.read(buffer, 0, sampleSize, 0);
            await fileHandle.close();
            
            // Analyser le type de contenu
            const contentType = this.analyzeContent(buffer, extension);
            
            return {
                size: stats.size,
                extension,
                contentType,
                isText: this.isTextContent(buffer),
                entropy: this.calculateEntropy(buffer)
            };
        } catch (error) {
            console.error(`Erreur analyse fichier ${filePath}:`, error);
            return {
                size: 0,
                extension: '',
                contentType: 'unknown',
                isText: false,
                entropy: 0
            };
        }
    }

    /**
     * Sélectionne l'algorithme optimal basé sur l'analyse du fichier
     */
    selectOptimalAlgorithm(fileInfo) {
        // Pour les fichiers texte
        if (fileInfo.isText || ['text', 'json', 'xml', 'html'].includes(fileInfo.contentType)) {
            return fileInfo.size > 1024 * 1024 ? 'brotli' : 'gzip'; // Brotli pour gros fichiers texte
        }
        
        // Pour les petits fichiers
        if (fileInfo.size < 1024) {
            return 'deflate';
        }
        
        // Pour les fichiers avec faible entropie (très répétitifs)
        if (fileInfo.entropy < 3) {
            return 'gzip';
        }
        
        // Par défaut
        return 'gzip';
    }

    /**
     * Détecte l'algorithme de compression d'un fichier
     */
    detectAlgorithm(filePath) {
        const extension = path.extname(filePath).toLowerCase();
        
        for (const [algorithm, config] of Object.entries(this.algorithms)) {
            if (config.extension === extension) {
                return algorithm;
            }
        }
        
        // Tentative de détection par signature
        return this.detectBySignature(filePath);
    }

    /**
     * Détecte l'algorithme par signature de fichier
     */
    async detectBySignature(filePath) {
        try {
            const fileHandle = await fs.open(filePath, 'r');
            const buffer = Buffer.alloc(10);
            await fileHandle.read(buffer, 0, 10, 0);
            await fileHandle.close();
            
            // Signatures des formats de compression
            if (buffer[0] === 0x1f && buffer[1] === 0x8b) {
                return 'gzip';
            }
            
            // Signature Brotli (approximative)
            if (buffer[0] === 0x21 || buffer[0] === 0x81) {
                return 'brotli';
            }
            
            return 'deflate'; // Par défaut
        } catch (error) {
            return 'gzip'; // Fallback
        }
    }

    /**
     * Analyse le contenu d'un buffer
     */
    analyzeContent(buffer, extension) {
        const textExtensions = ['.txt', '.json', '.xml', '.html', '.css', '.js', '.md'];
        
        if (textExtensions.includes(extension)) {
            return 'text';
        }
        
        // Vérifier si c'est du texte par analyse des bytes
        if (this.isTextContent(buffer)) {
            return 'text';
        }
        
        return 'binary';
    }

    /**
     * Vérifie si le contenu est du texte
     */
    isTextContent(buffer) {
        let textBytes = 0;
        
        for (let i = 0; i < buffer.length; i++) {
            const byte = buffer[i];
            if ((byte >= 32 && byte <= 126) || byte === 9 || byte === 10 || byte === 13) {
                textBytes++;
            }
        }
        
        return (textBytes / buffer.length) > 0.7;
    }

    /**
     * Calcule l'entropie d'un buffer (mesure de randomness)
     */
    calculateEntropy(buffer) {
        const frequencies = new Array(256).fill(0);
        
        // Compter les fréquences
        for (let i = 0; i < buffer.length; i++) {
            frequencies[buffer[i]]++;
        }
        
        // Calculer l'entropie
        let entropy = 0;
        for (let i = 0; i < 256; i++) {
            if (frequencies[i] > 0) {
                const probability = frequencies[i] / buffer.length;
                entropy -= probability * Math.log2(probability);
            }
        }
        
        return entropy;
    }

    /**
     * Met à jour les statistiques globales
     */
    updateStats(job) {
        this.stats.totalCompressions++;
        this.stats.totalBytesSaved += job.bytesSaved;
        this.stats.processingTime += job.processingTime;
        
        // Recalculer la moyenne du ratio de compression
        const totalRatio = this.compressionHistory.reduce((sum, entry) => {
            return sum + (entry.compressionRatio || 0);
        }, job.compressionRatio);
        
        this.stats.averageCompressionRatio = totalRatio / (this.compressionHistory.length + 1);
    }

    /**
     * Obtient le statut du système
     */
    getSystemStatus() {
        return {
            algorithms: Object.keys(this.algorithms),
            stats: this.stats,
            activeJobs: Array.from(this.activeJobs.values()),
            recentHistory: this.compressionHistory.slice(-10),
            performance: {
                averageProcessingTime: this.stats.processingTime / Math.max(this.stats.totalCompressions + this.stats.totalDecompressions, 1),
                totalOperations: this.stats.totalCompressions + this.stats.totalDecompressions,
                spaceSaved: this.formatBytes(this.stats.totalBytesSaved)
            }
        };
    }

    /**
     * Formate les bytes en format lisible
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Charge l'historique depuis le fichier
     */
    async loadHistory() {
        try {
            const historyPath = path.join(__dirname, 'compression-logs', 'history.json');
            const data = await fs.readFile(historyPath, 'utf8');
            const parsed = JSON.parse(data);
            
            this.compressionHistory = parsed.history || [];
            this.stats = { ...this.stats, ...parsed.stats };
            
            console.log(`📊 Historique compression chargé: ${this.compressionHistory.length} entrées`);
        } catch (error) {
            console.log('📊 Nouveau fichier d\'historique compression créé');
        }
    }

    /**
     * Sauvegarde l'historique
     */
    async saveHistory() {
        try {
            const historyPath = path.join(__dirname, 'compression-logs', 'history.json');
            const data = {
                history: this.compressionHistory,
                stats: this.stats,
                lastUpdate: new Date().toISOString()
            };
            
            await fs.writeFile(historyPath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('Erreur sauvegarde historique compression:', error);
        }
    }

    /**
     * Nettoie les anciens fichiers temporaires
     */
    async cleanup() {
        try {
            const tempDir = path.join(__dirname, 'temp-compression');
            const files = await fs.readdir(tempDir);
            
            for (const file of files) {
                const filePath = path.join(tempDir, file);
                const stats = await fs.stat(filePath);
                const age = Date.now() - stats.mtime.getTime();
                
                // Supprimer les fichiers de plus d'une heure
                if (age > 60 * 60 * 1000) {
                    await fs.unlink(filePath);
                }
            }
        } catch (error) {
            console.error('Erreur nettoyage fichiers temporaires:', error);
        }
    }

    /**
     * Arrête le système de compression
     */
    async stop() {
        await this.saveHistory();
        await this.cleanup();
        console.log('🗜️ Système de compression arrêté');
    }
}

module.exports = AdvancedCompressionSystem;
