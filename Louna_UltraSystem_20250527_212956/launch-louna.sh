#!/bin/bash

# Script pour lancer l'application Louna avec Electron

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "Node.js n'est pas installé. Veuillez installer Node.js pour exécuter cette application."
    exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo "npm n'est pas installé. Veuillez installer npm pour exécuter cette application."
    exit 1
fi

# Vérifier si Electron est installé
if ! npm list -g electron &> /dev/null; then
    echo "Electron n'est pas installé globalement. Installation en cours..."
    npm install -g electron
fi

# Vérifier si les dépendances sont installées
if [ ! -d "node_modules" ]; then
    echo "Installation des dépendances..."
    npm install
fi

# Vérifier si les dossiers de données existent
if [ ! -d "data" ]; then
    echo "Création du dossier de données..."
    mkdir -p data/memory
    mkdir -p data/accelerators
fi

# Lancer l'application avec Electron
echo "Lancement de l'application Louna..."
npm run electron
