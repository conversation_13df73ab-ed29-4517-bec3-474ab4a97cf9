// Système de suivi d'évolution de Louna
const fs = require('fs');
const path = require('path');

class EvolutionTracker {
    constructor() {
        this.dataFile = './data/evolution-history.json';
        this.history = this.loadHistory();
        this.startTime = Date.now();
        this.lastSnapshot = null;
        
        // Métriques d'évolution
        this.evolutionMetrics = {
            qi: {
                initial: 120,
                current: 120,
                growth: 0,
                growthRate: 0.01, // 1% par jour d'apprentissage
                maxGrowth: 200
            },
            neurons: {
                initial: 145,
                current: 145,
                growth: 0,
                growthRate: 0.05, // 5% par semaine
                activeRatio: 0.61 // 89/145
            },
            memory: {
                initialEntries: 83,
                currentEntries: 83,
                growth: 0,
                zones: 6,
                avgTemperature: 0.58
            },
            learning: {
                experiencePoints: 150,
                learningBonus: 20,
                cyclesCompleted: 0,
                optimizationsApplied: 0
            },
            creativity: {
                level: 95,
                innovations: 0,
                zone6Activity: 0.7,
                creativeOutputs: 0
            }
        };
    }

    // Charger l'historique d'évolution
    loadHistory() {
        try {
            if (fs.existsSync(this.dataFile)) {
                const data = fs.readFileSync(this.dataFile, 'utf8');
                return JSON.parse(data);
            }
        } catch (error) {
            console.warn('Erreur chargement historique évolution:', error.message);
        }
        
        return {
            snapshots: [],
            startDate: new Date().toISOString(),
            totalUptime: 0,
            evolutionEvents: []
        };
    }

    // Sauvegarder l'historique
    saveHistory() {
        try {
            const dir = path.dirname(this.dataFile);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            
            fs.writeFileSync(this.dataFile, JSON.stringify(this.history, null, 2));
        } catch (error) {
            console.warn('Erreur sauvegarde historique évolution:', error.message);
        }
    }

    // Prendre un snapshot de l'état actuel
    takeSnapshot() {
        const now = Date.now();
        const snapshot = {
            timestamp: new Date().toISOString(),
            uptime: now - this.startTime,
            metrics: JSON.parse(JSON.stringify(this.evolutionMetrics)),
            performance: this.calculatePerformance(),
            evolutionScore: this.calculateEvolutionScore()
        };

        this.history.snapshots.push(snapshot);
        this.lastSnapshot = snapshot;

        // Limiter l'historique à 1000 snapshots
        if (this.history.snapshots.length > 1000) {
            this.history.snapshots.shift();
        }

        this.saveHistory();
        return snapshot;
    }

    // Calculer les performances actuelles
    calculatePerformance() {
        return {
            qiEfficiency: (this.evolutionMetrics.qi.current / this.evolutionMetrics.qi.maxGrowth) * 100,
            neuronUtilization: this.evolutionMetrics.neurons.activeRatio * 100,
            memoryEfficiency: (this.evolutionMetrics.memory.currentEntries / 200) * 100, // Max théorique 200
            learningRate: this.evolutionMetrics.learning.experiencePoints / 1000 * 100, // Max théorique 1000
            creativityIndex: this.evolutionMetrics.creativity.level
        };
    }

    // Calculer le score d'évolution global
    calculateEvolutionScore() {
        const performance = this.calculatePerformance();
        const weights = {
            qi: 0.25,
            neurons: 0.25,
            memory: 0.20,
            learning: 0.15,
            creativity: 0.15
        };

        return Math.round(
            performance.qiEfficiency * weights.qi +
            performance.neuronUtilization * weights.neurons +
            performance.memoryEfficiency * weights.memory +
            performance.learningRate * weights.learning +
            performance.creativityIndex * weights.creativity
        );
    }

    // Détecter l'évolution
    detectEvolution() {
        if (!this.lastSnapshot || this.history.snapshots.length < 2) {
            return { evolved: false, reason: 'Données insuffisantes' };
        }

        const current = this.lastSnapshot;
        const previous = this.history.snapshots[this.history.snapshots.length - 2];
        
        const changes = {
            qi: current.metrics.qi.current - previous.metrics.qi.current,
            neurons: current.metrics.neurons.current - previous.metrics.neurons.current,
            memory: current.metrics.memory.currentEntries - previous.metrics.memory.currentEntries,
            experience: current.metrics.learning.experiencePoints - previous.metrics.learning.experiencePoints,
            creativity: current.metrics.creativity.level - previous.metrics.creativity.level
        };

        // Seuils d'évolution significative
        const thresholds = {
            qi: 0.5,
            neurons: 1,
            memory: 2,
            experience: 5,
            creativity: 1
        };

        const significantChanges = [];
        for (const [metric, change] of Object.entries(changes)) {
            if (Math.abs(change) >= thresholds[metric]) {
                significantChanges.push({
                    metric,
                    change,
                    direction: change > 0 ? 'croissance' : 'décroissance',
                    percentage: ((change / previous.metrics[metric === 'experience' ? 'learning' : metric][metric === 'experience' ? 'experiencePoints' : 'current']) * 100).toFixed(2)
                });
            }
        }

        if (significantChanges.length > 0) {
            const evolutionEvent = {
                timestamp: new Date().toISOString(),
                type: 'evolution_detected',
                changes: significantChanges,
                evolutionScore: current.evolutionScore,
                previousScore: previous.evolutionScore
            };

            this.history.evolutionEvents.push(evolutionEvent);
            this.saveHistory();

            return {
                evolved: true,
                changes: significantChanges,
                event: evolutionEvent
            };
        }

        return { evolved: false, reason: 'Aucun changement significatif' };
    }

    // Simuler l'évolution basée sur l'activité
    simulateEvolution(activityData = {}) {
        const {
            learningActivity = 0.5,
            memoryActivity = 0.5,
            creativeActivity = 0.7,
            interactionCount = 0
        } = activityData;

        // Évolution du QI basée sur l'apprentissage
        if (learningActivity > 0.6) {
            this.evolutionMetrics.qi.current += this.evolutionMetrics.qi.growthRate * learningActivity;
            this.evolutionMetrics.qi.current = Math.min(this.evolutionMetrics.qi.current, this.evolutionMetrics.qi.maxGrowth);
        }

        // Évolution des neurones basée sur l'activité
        if (memoryActivity > 0.5) {
            this.evolutionMetrics.neurons.current += this.evolutionMetrics.neurons.growthRate * memoryActivity;
        }

        // Évolution de la mémoire
        if (interactionCount > 0) {
            this.evolutionMetrics.memory.currentEntries += Math.floor(interactionCount / 10);
        }

        // Évolution de l'expérience
        this.evolutionMetrics.learning.experiencePoints += Math.floor(learningActivity * 10);
        this.evolutionMetrics.learning.cyclesCompleted++;

        // Évolution de la créativité
        if (creativeActivity > 0.8) {
            this.evolutionMetrics.creativity.level = Math.min(100, this.evolutionMetrics.creativity.level + 0.1);
            this.evolutionMetrics.creativity.innovations++;
        }

        return this.takeSnapshot();
    }

    // Obtenir le rapport d'évolution
    getEvolutionReport() {
        const snapshots = this.history.snapshots;
        if (snapshots.length === 0) {
            return { error: 'Aucune donnée d\'évolution disponible' };
        }

        const latest = snapshots[snapshots.length - 1];
        const oldest = snapshots[0];
        const timespan = new Date(latest.timestamp) - new Date(oldest.timestamp);

        return {
            period: {
                start: oldest.timestamp,
                end: latest.timestamp,
                duration: timespan,
                durationHours: Math.round(timespan / (1000 * 60 * 60))
            },
            evolution: {
                qi: {
                    initial: oldest.metrics.qi.current,
                    current: latest.metrics.qi.current,
                    growth: latest.metrics.qi.current - oldest.metrics.qi.current,
                    growthPercentage: ((latest.metrics.qi.current - oldest.metrics.qi.current) / oldest.metrics.qi.current * 100).toFixed(2)
                },
                neurons: {
                    initial: oldest.metrics.neurons.current,
                    current: latest.metrics.neurons.current,
                    growth: latest.metrics.neurons.current - oldest.metrics.neurons.current
                },
                memory: {
                    initial: oldest.metrics.memory.currentEntries,
                    current: latest.metrics.memory.currentEntries,
                    growth: latest.metrics.memory.currentEntries - oldest.metrics.memory.currentEntries
                },
                experience: {
                    initial: oldest.metrics.learning.experiencePoints,
                    current: latest.metrics.learning.experiencePoints,
                    growth: latest.metrics.learning.experiencePoints - oldest.metrics.learning.experiencePoints
                }
            },
            performance: latest.performance,
            evolutionScore: latest.evolutionScore,
            events: this.history.evolutionEvents.slice(-10), // 10 derniers événements
            trends: this.calculateTrends(),
            isEvolving: this.isCurrentlyEvolving()
        };
    }

    // Calculer les tendances
    calculateTrends() {
        const snapshots = this.history.snapshots.slice(-10); // 10 derniers snapshots
        if (snapshots.length < 2) return {};

        const trends = {};
        const metrics = ['qi', 'neurons', 'memory'];
        
        metrics.forEach(metric => {
            const values = snapshots.map(s => s.metrics[metric].current || s.metrics[metric].currentEntries);
            const slope = this.calculateSlope(values);
            trends[metric] = {
                direction: slope > 0 ? 'croissant' : slope < 0 ? 'décroissant' : 'stable',
                intensity: Math.abs(slope),
                confidence: values.length >= 5 ? 'high' : 'medium'
            };
        });

        return trends;
    }

    // Calculer la pente (tendance)
    calculateSlope(values) {
        const n = values.length;
        const sumX = (n * (n - 1)) / 2;
        const sumY = values.reduce((a, b) => a + b, 0);
        const sumXY = values.reduce((sum, y, x) => sum + x * y, 0);
        const sumX2 = values.reduce((sum, _, x) => sum + x * x, 0);
        
        return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    }

    // Vérifier si le système évolue actuellement
    isCurrentlyEvolving() {
        const recentEvents = this.history.evolutionEvents.slice(-5);
        const recentEvolutionCount = recentEvents.filter(e => 
            Date.now() - new Date(e.timestamp).getTime() < 24 * 60 * 60 * 1000 // 24h
        ).length;

        return recentEvolutionCount > 0;
    }
}

module.exports = EvolutionTracker;
