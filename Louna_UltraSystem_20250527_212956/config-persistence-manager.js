/**
 * GESTIONNAIRE DE PERSISTANCE DES CONFIGURATIONS LOUNA
 * Sauvegarde et restaure toutes les configurations système
 */

const fs = require('fs').promises;
const path = require('path');

class ConfigPersistenceManager {
    constructor(configDir = './data/config') {
        this.configDir = configDir;
        this.configFiles = {
            mcpConnection: path.join(configDir, 'mcp-connection.json'),
            fallbackSystem: path.join(configDir, 'fallback-system.json'),
            preprocessor: path.join(configDir, 'preprocessor.json'),
            cache: path.join(configDir, 'cache-config.json'),
            kyberAccelerators: path.join(configDir, 'kyber-config.json'),
            thermalMemory: path.join(configDir, 'thermal-memory-config.json'),
            systemSettings: path.join(configDir, 'system-settings.json')
        };
        
        this.defaultConfigs = {
            mcpConnection: {
                port: 3002,
                maxRetries: 5,
                retryDelay: 3000,
                healthCheckInterval: 10000,
                debug: true,
                allowInternet: true,
                allowDesktop: true,
                allowSystemCommands: true,
                lastSuccessfulConnection: null,
                connectionHistory: []
            },
            fallbackSystem: {
                enabled: true,
                intelligentResponses: 5,
                contextualResponses: 4,
                emergencyResponses: 3,
                maxSimilarityThreshold: 0.6,
                cacheEnabled: true,
                debugMode: true,
                responseTypes: {
                    recherche_internet: { confidence: 0.8, enabled: true },
                    analyse_complexe: { confidence: 0.75, enabled: true },
                    creation_contenu: { confidence: 0.8, enabled: true },
                    question_technique: { confidence: 0.75, enabled: true },
                    conversation_generale: { confidence: 0.7, enabled: true }
                }
            },
            preprocessor: {
                enabled: true,
                maxCacheSize: 1000,
                cacheTTL: 3600000, // 1 heure
                semanticCacheEnabled: true,
                instantResponses: 7,
                patterns: 12,
                efficiency: 50.0,
                averageProcessingTime: 1,
                cleanupInterval: 600000 // 10 minutes
            },
            cache: {
                maxSize: 1000,
                ttl: 3600000, // 1 heure
                semanticEnabled: true,
                compressionEnabled: true,
                autoCleanup: true,
                cleanupInterval: 600000, // 10 minutes
                hitRateTarget: 80.0
            },
            kyberAccelerators: {
                autoAcceleratorsEnabled: true,
                maxAutoAccelerators: 10,
                defaultBoostFactor: 2.0,
                criticalThreshold: 0.8,
                monitoringInterval: 3000,
                acceleratorTypes: {
                    memory_optimizer: { enabled: true, maxBoost: 3.0 },
                    thermal_cooler: { enabled: true, maxBoost: 2.0 },
                    qi_enhancer: { enabled: true, maxBoost: 3.0 },
                    cpu_accelerator: { enabled: true, maxBoost: 2.5 },
                    response_optimizer: { enabled: true, maxBoost: 2.0 }
                }
            },
            thermalMemory: {
                memoryCycleInterval: 30,
                memoryDecayRate: 0.95,
                temperatureCursorSensitivity: 0.1,
                importanceFactor: 1.2,
                accessFactor: 1.1,
                maxMemories: 10000,
                autoEvolution: true,
                backupInterval: 300000, // 5 minutes
                zones: {
                    instant: { threshold: 0.8, maxEntries: 100 },
                    shortTerm: { threshold: 0.65, maxEntries: 500 },
                    working: { threshold: 0.5, maxEntries: 1000 },
                    mediumTerm: { threshold: 0.35, maxEntries: 2000 },
                    longTerm: { threshold: 0.2, maxEntries: 5000 },
                    creative: { threshold: 0.1, maxEntries: 1000 }
                }
            },
            systemSettings: {
                version: "2.0.0",
                lastUpdate: null,
                autoSaveInterval: 60000, // 1 minute
                emergencyBackupEnabled: true,
                performanceMonitoring: true,
                debugMode: true,
                timeouts: {
                    agentPrincipal: 9000,
                    ollama: 8000,
                    mcp: 5000,
                    fallback: 3000
                }
            }
        };
        
        console.log('💾 Gestionnaire de persistance des configurations initialisé');
    }

    /**
     * Initialise le système de persistance
     */
    async initialize() {
        try {
            // Créer le répertoire de configuration s'il n'existe pas
            await this.ensureConfigDirectory();
            
            // Charger ou créer les configurations par défaut
            await this.loadOrCreateConfigs();
            
            // Démarrer la sauvegarde automatique
            this.startAutoSave();
            
            console.log('✅ Système de persistance des configurations initialisé');
            return true;
        } catch (error) {
            console.error('❌ Erreur initialisation persistance:', error);
            return false;
        }
    }

    /**
     * Assure que le répertoire de configuration existe
     */
    async ensureConfigDirectory() {
        try {
            await fs.mkdir(this.configDir, { recursive: true });
            console.log(`📁 Répertoire de configuration: ${this.configDir}`);
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw error;
            }
        }
    }

    /**
     * Charge ou crée les configurations
     */
    async loadOrCreateConfigs() {
        for (const [configName, configPath] of Object.entries(this.configFiles)) {
            try {
                // Essayer de charger la configuration existante
                const configData = await fs.readFile(configPath, 'utf8');
                const config = JSON.parse(configData);
                
                // Fusionner avec les valeurs par défaut pour les nouvelles propriétés
                this.configs = this.configs || {};
                this.configs[configName] = { ...this.defaultConfigs[configName], ...config };
                
                console.log(`📖 Configuration ${configName} chargée`);
            } catch (error) {
                // Créer la configuration par défaut si elle n'existe pas
                this.configs = this.configs || {};
                this.configs[configName] = { ...this.defaultConfigs[configName] };
                
                await this.saveConfig(configName);
                console.log(`📝 Configuration ${configName} créée avec valeurs par défaut`);
            }
        }
    }

    /**
     * Sauvegarde une configuration spécifique
     */
    async saveConfig(configName) {
        try {
            if (!this.configs || !this.configs[configName]) {
                console.warn(`⚠️ Configuration ${configName} non trouvée`);
                return false;
            }

            const configPath = this.configFiles[configName];
            const configData = JSON.stringify(this.configs[configName], null, 2);
            
            await fs.writeFile(configPath, configData, 'utf8');
            console.log(`💾 Configuration ${configName} sauvegardée`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur sauvegarde ${configName}:`, error);
            return false;
        }
    }

    /**
     * Sauvegarde toutes les configurations
     */
    async saveAllConfigs() {
        const results = [];
        for (const configName of Object.keys(this.configFiles)) {
            const result = await this.saveConfig(configName);
            results.push({ configName, success: result });
        }
        
        const successCount = results.filter(r => r.success).length;
        console.log(`💾 Sauvegarde complète: ${successCount}/${results.length} configurations`);
        
        return results;
    }

    /**
     * Met à jour une configuration
     */
    async updateConfig(configName, updates) {
        try {
            if (!this.configs || !this.configs[configName]) {
                console.warn(`⚠️ Configuration ${configName} non trouvée`);
                return false;
            }

            // Fusionner les mises à jour
            this.configs[configName] = { ...this.configs[configName], ...updates };
            
            // Sauvegarder immédiatement
            const saved = await this.saveConfig(configName);
            
            if (saved) {
                console.log(`🔄 Configuration ${configName} mise à jour`);
            }
            
            return saved;
        } catch (error) {
            console.error(`❌ Erreur mise à jour ${configName}:`, error);
            return false;
        }
    }

    /**
     * Obtient une configuration
     */
    getConfig(configName) {
        if (!this.configs || !this.configs[configName]) {
            console.warn(`⚠️ Configuration ${configName} non trouvée, utilisation des valeurs par défaut`);
            return this.defaultConfigs[configName] || {};
        }
        
        return this.configs[configName];
    }

    /**
     * Obtient toutes les configurations
     */
    getAllConfigs() {
        return this.configs || {};
    }

    /**
     * Démarre la sauvegarde automatique
     */
    startAutoSave() {
        const autoSaveInterval = this.getConfig('systemSettings').autoSaveInterval || 60000;
        
        this.autoSaveTimer = setInterval(async () => {
            await this.saveAllConfigs();
        }, autoSaveInterval);
        
        console.log(`⏰ Sauvegarde automatique démarrée (toutes les ${autoSaveInterval/1000}s)`);
    }

    /**
     * Arrête la sauvegarde automatique
     */
    stopAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
            console.log('⏰ Sauvegarde automatique arrêtée');
        }
    }

    /**
     * Sauvegarde l'état actuel des systèmes
     */
    async saveSystemStates(systems) {
        try {
            const systemStates = {
                timestamp: Date.now(),
                systems: {}
            };

            // Sauvegarder l'état du MCP
            if (systems.mcpConnectionManager) {
                const mcpStats = systems.mcpConnectionManager.getStats();
                systemStates.systems.mcp = {
                    isConnected: mcpStats.isConnected,
                    stats: mcpStats.stats,
                    lastHealthCheck: mcpStats.lastHealthCheck
                };
                
                await this.updateConfig('mcpConnection', {
                    lastSuccessfulConnection: mcpStats.stats.lastConnectionTime,
                    connectionHistory: [...(this.getConfig('mcpConnection').connectionHistory || []), {
                        timestamp: Date.now(),
                        success: mcpStats.isConnected,
                        stats: mcpStats.stats
                    }].slice(-10) // Garder seulement les 10 dernières
                });
            }

            // Sauvegarder l'état du fallback
            if (systems.intelligentFallback) {
                const fallbackStats = systems.intelligentFallback.getStats();
                systemStates.systems.fallback = fallbackStats;
                
                await this.updateConfig('fallbackSystem', {
                    lastStats: fallbackStats,
                    lastUpdate: Date.now()
                });
            }

            // Sauvegarder l'état du pré-processeur
            if (systems.ultraFastPreprocessor) {
                const preprocessorStats = systems.ultraFastPreprocessor.getStats();
                systemStates.systems.preprocessor = preprocessorStats;
                
                await this.updateConfig('preprocessor', {
                    lastStats: preprocessorStats,
                    lastUpdate: Date.now()
                });
            }

            // Sauvegarder l'état du cache
            if (systems.intelligentCache) {
                const cacheStats = systems.intelligentCache.getStats();
                systemStates.systems.cache = cacheStats;
                
                await this.updateConfig('cache', {
                    lastStats: cacheStats,
                    lastUpdate: Date.now()
                });
            }

            // Sauvegarder dans un fichier d'état système
            const stateFile = path.join(this.configDir, 'system-states.json');
            await fs.writeFile(stateFile, JSON.stringify(systemStates, null, 2), 'utf8');
            
            console.log('💾 États des systèmes sauvegardés');
            return true;
        } catch (error) {
            console.error('❌ Erreur sauvegarde états systèmes:', error);
            return false;
        }
    }

    /**
     * Restaure les états des systèmes
     */
    async restoreSystemStates() {
        try {
            const stateFile = path.join(this.configDir, 'system-states.json');
            const stateData = await fs.readFile(stateFile, 'utf8');
            const systemStates = JSON.parse(stateData);
            
            console.log(`🔄 États des systèmes restaurés (${new Date(systemStates.timestamp).toLocaleString()})`);
            return systemStates;
        } catch (error) {
            console.log('📝 Aucun état système précédent trouvé, démarrage avec configuration par défaut');
            return null;
        }
    }

    /**
     * Crée une sauvegarde complète
     */
    async createFullBackup() {
        try {
            const backupData = {
                timestamp: Date.now(),
                version: "2.0.0",
                configs: this.getAllConfigs(),
                systemStates: await this.restoreSystemStates()
            };

            const backupFile = path.join(this.configDir, `full-backup-${Date.now()}.json`);
            await fs.writeFile(backupFile, JSON.stringify(backupData, null, 2), 'utf8');
            
            console.log(`💾 Sauvegarde complète créée: ${backupFile}`);
            return backupFile;
        } catch (error) {
            console.error('❌ Erreur création sauvegarde complète:', error);
            return null;
        }
    }

    /**
     * Obtient les statistiques de persistance
     */
    getStats() {
        const configCount = Object.keys(this.configFiles).length;
        const loadedCount = this.configs ? Object.keys(this.configs).length : 0;
        
        return {
            configFiles: configCount,
            loadedConfigs: loadedCount,
            autoSaveActive: !!this.autoSaveTimer,
            configDirectory: this.configDir,
            lastUpdate: Date.now()
        };
    }

    /**
     * Arrêt propre du gestionnaire
     */
    async shutdown() {
        console.log('🛑 Arrêt du gestionnaire de persistance...');
        
        // Arrêter la sauvegarde automatique
        this.stopAutoSave();
        
        // Sauvegarder une dernière fois
        await this.saveAllConfigs();
        
        // Créer une sauvegarde complète
        await this.createFullBackup();
        
        console.log('✅ Gestionnaire de persistance arrêté proprement');
    }
}

module.exports = ConfigPersistenceManager;
