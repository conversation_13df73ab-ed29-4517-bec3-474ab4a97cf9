/**
 * SYSTÈME TURBO ADAPTATIF ULTRA-INTELLIGENT
 * 
 * Système qui adapte automatiquement le mode TURBO en fonction de:
 * - Longueur de la question
 * - Longueur de la réponse attendue
 * - Temps de traitement
 * - Complexité du contenu
 * - Historique de performance
 */

const EventEmitter = require('events');

class AdaptiveTurboSystem extends EventEmitter {
    constructor() {
        super();
        
        // Configuration du système TURBO adaptatif
        this.config = {
            // Modes TURBO disponibles
            modes: {
                eco: {
                    name: 'ECO',
                    speedMultiplier: 1.0,
                    maxTokens: 150,
                    temperature: 0.3,
                    timeout: 5000,
                    description: 'Mode économique pour réponses simples'
                },
                normal: {
                    name: 'NORMAL',
                    speedMultiplier: 1.5,
                    maxTokens: 300,
                    temperature: 0.5,
                    timeout: 3000,
                    description: 'Mode standard pour usage général'
                },
                turbo: {
                    name: 'TURBO',
                    speedMultiplier: 2.0,
                    maxTokens: 500,
                    temperature: 0.7,
                    timeout: 2000,
                    description: 'Mode rapide pour réponses complexes'
                },
                ultra: {
                    name: 'ULTRA',
                    speedMultiplier: 3.0,
                    maxTokens: 800,
                    temperature: 0.8,
                    timeout: 1500,
                    description: 'Mode ultra-rapide pour urgences'
                },
                quantum: {
                    name: 'QUANTUM',
                    speedMultiplier: 5.0,
                    maxTokens: 1200,
                    temperature: 0.9,
                    timeout: 1000,
                    description: 'Mode quantique pour traitement instantané'
                },
                transcendent: {
                    name: 'TRANSCENDENT',
                    speedMultiplier: 10.0,
                    maxTokens: 2000,
                    temperature: 1.0,
                    timeout: 500,
                    description: 'Mode transcendant pour conscience élargie'
                }
            },
            
            // Seuils de détection automatique
            thresholds: {
                questionLength: {
                    short: 50,      // < 50 chars
                    medium: 200,    // 50-200 chars
                    long: 500,      // 200-500 chars
                    veryLong: 1000  // > 500 chars
                },
                responseTime: {
                    instant: 500,   // < 500ms
                    fast: 1500,     // 500-1500ms
                    normal: 3000,   // 1500-3000ms
                    slow: 5000      // > 3000ms
                },
                complexity: {
                    simple: 0.3,
                    moderate: 0.6,
                    complex: 0.8,
                    veryComplex: 1.0
                }
            }
        };
        
        // Mode actuel
        this.currentMode = 'normal';
        
        // Historique des performances
        this.performanceHistory = [];
        this.maxHistorySize = 100;
        
        // Statistiques
        this.stats = {
            totalRequests: 0,
            modeChanges: 0,
            averageResponseTime: 0,
            averageQuestionLength: 0,
            averageResponseLength: 0,
            modeDistribution: {
                eco: 0,
                normal: 0,
                turbo: 0,
                ultra: 0,
                quantum: 0,
                transcendent: 0
            },
            performanceGains: 0
        };
        
        // Patterns de détection
        this.patterns = {
            urgent: /urgent|rapide|vite|immédiat|maintenant|pressé/i,
            complex: /expliquer|analyser|détailler|approfondir|complexe/i,
            simple: /oui|non|bonjour|merci|salut|ok/i,
            creative: /créer|imaginer|inventer|générer|concevoir/i,
            technical: /code|programmation|algorithme|technique|développement/i,
            emotional: /sentiment|émotion|ressenti|humeur|état/i
        };
        
        this.log('🚀 Système TURBO adaptatif initialisé');
    }
    
    /**
     * Analyse une question et détermine le mode TURBO optimal
     */
    analyzeAndSelectMode(question, context = {}) {
        const analysis = this.analyzeQuestion(question, context);
        const optimalMode = this.selectOptimalMode(analysis);
        
        this.log(`🎯 Mode sélectionné: ${optimalMode.toUpperCase()} pour "${question.substring(0, 50)}..."`);
        
        return {
            mode: optimalMode,
            analysis: analysis,
            config: this.config.modes[optimalMode],
            reasoning: this.explainModeSelection(analysis, optimalMode)
        };
    }
    
    /**
     * Analyse complète d'une question
     */
    analyzeQuestion(question, context = {}) {
        const length = question.length;
        const words = question.split(/\s+/).length;
        
        return {
            // Analyse de base
            length: length,
            wordCount: words,
            
            // Classification de longueur
            lengthCategory: this.classifyLength(length),
            
            // Détection de patterns
            patterns: this.detectPatterns(question),
            
            // Analyse de complexité
            complexity: this.calculateComplexity(question),
            
            // Urgence détectée
            urgency: this.detectUrgency(question),
            
            // Type de contenu
            contentType: this.classifyContent(question),
            
            // Contexte
            hasContext: Object.keys(context).length > 0,
            contextComplexity: this.analyzeContextComplexity(context),
            
            // Prédiction de réponse
            expectedResponseLength: this.predictResponseLength(question),
            expectedComplexity: this.predictResponseComplexity(question)
        };
    }
    
    /**
     * Classifie la longueur d'une question
     */
    classifyLength(length) {
        const thresholds = this.config.thresholds.questionLength;
        
        if (length < thresholds.short) return 'short';
        if (length < thresholds.medium) return 'medium';
        if (length < thresholds.long) return 'long';
        return 'veryLong';
    }
    
    /**
     * Détecte les patterns dans la question
     */
    detectPatterns(question) {
        const detected = {};
        
        Object.keys(this.patterns).forEach(pattern => {
            detected[pattern] = this.patterns[pattern].test(question);
        });
        
        return detected;
    }
    
    /**
     * Calcule la complexité d'une question
     */
    calculateComplexity(question) {
        let complexity = 0;
        
        // Facteurs de complexité
        const factors = {
            length: Math.min(question.length / 500, 1) * 0.3,
            questionMarks: (question.match(/\?/g) || []).length * 0.1,
            technicalTerms: this.countTechnicalTerms(question) * 0.2,
            nestedClauses: this.countNestedClauses(question) * 0.15,
            abstractConcepts: this.countAbstractConcepts(question) * 0.25
        };
        
        complexity = Object.values(factors).reduce((sum, factor) => sum + factor, 0);
        return Math.min(complexity, 1);
    }
    
    /**
     * Détecte l'urgence dans une question
     */
    detectUrgency(question) {
        const urgentWords = ['urgent', 'rapide', 'vite', 'immédiat', 'maintenant', 'pressé', 'emergency'];
        const urgencyScore = urgentWords.reduce((score, word) => {
            return score + (question.toLowerCase().includes(word) ? 1 : 0);
        }, 0);
        
        return Math.min(urgencyScore / urgentWords.length, 1);
    }
    
    /**
     * Classifie le type de contenu
     */
    classifyContent(question) {
        const types = [];
        
        if (this.patterns.technical.test(question)) types.push('technical');
        if (this.patterns.creative.test(question)) types.push('creative');
        if (this.patterns.emotional.test(question)) types.push('emotional');
        if (this.patterns.complex.test(question)) types.push('complex');
        if (this.patterns.simple.test(question)) types.push('simple');
        
        return types.length > 0 ? types : ['general'];
    }
    
    /**
     * Prédit la longueur de réponse nécessaire
     */
    predictResponseLength(question) {
        let predictedLength = 100; // Base
        
        // Facteurs d'augmentation
        if (question.includes('expliquer')) predictedLength += 200;
        if (question.includes('détailler')) predictedLength += 300;
        if (question.includes('analyser')) predictedLength += 250;
        if (question.includes('comment')) predictedLength += 150;
        if (question.includes('pourquoi')) predictedLength += 200;
        
        // Facteurs de réduction
        if (this.patterns.simple.test(question)) predictedLength = 50;
        
        return Math.max(predictedLength, 50);
    }
    
    /**
     * Prédit la complexité de réponse nécessaire
     */
    predictResponseComplexity(question) {
        const baseComplexity = this.calculateComplexity(question);
        
        // Ajustements basés sur le type de question
        let adjustment = 0;
        if (question.includes('pourquoi')) adjustment += 0.2;
        if (question.includes('comment')) adjustment += 0.15;
        if (this.patterns.technical.test(question)) adjustment += 0.3;
        if (this.patterns.creative.test(question)) adjustment += 0.25;
        
        return Math.min(baseComplexity + adjustment, 1);
    }
    
    /**
     * Sélectionne le mode optimal basé sur l'analyse
     */
    selectOptimalMode(analysis) {
        let score = {
            eco: 0,
            normal: 0,
            turbo: 0,
            ultra: 0,
            quantum: 0,
            transcendent: 0
        };
        
        // Score basé sur la longueur
        switch (analysis.lengthCategory) {
            case 'short':
                score.eco += 3;
                score.normal += 2;
                break;
            case 'medium':
                score.normal += 3;
                score.turbo += 2;
                break;
            case 'long':
                score.turbo += 3;
                score.ultra += 2;
                break;
            case 'veryLong':
                score.ultra += 3;
                score.quantum += 2;
                break;
        }
        
        // Score basé sur la complexité
        if (analysis.complexity < 0.3) {
            score.eco += 2;
            score.normal += 1;
        } else if (analysis.complexity < 0.6) {
            score.normal += 2;
            score.turbo += 1;
        } else if (analysis.complexity < 0.8) {
            score.turbo += 2;
            score.ultra += 1;
        } else {
            score.ultra += 2;
            score.quantum += 3;
            score.transcendent += 1;
        }
        
        // Score basé sur l'urgence
        if (analysis.urgency > 0.5) {
            score.ultra += 3;
            score.quantum += 2;
            score.transcendent += 1;
        }
        
        // Score basé sur les patterns
        if (analysis.patterns.urgent) {
            score.ultra += 4;
            score.quantum += 3;
        }
        if (analysis.patterns.simple) {
            score.eco += 3;
            score.normal += 1;
        }
        if (analysis.patterns.complex) {
            score.turbo += 2;
            score.ultra += 3;
        }
        if (analysis.patterns.technical) {
            score.turbo += 2;
            score.ultra += 1;
        }
        
        // Score basé sur la réponse prédite
        if (analysis.expectedResponseLength > 500) {
            score.turbo += 2;
            score.ultra += 1;
        }
        if (analysis.expectedComplexity > 0.7) {
            score.ultra += 2;
            score.quantum += 1;
        }
        
        // Sélectionner le mode avec le score le plus élevé
        const selectedMode = Object.keys(score).reduce((a, b) => 
            score[a] > score[b] ? a : b
        );
        
        return selectedMode;
    }
    
    /**
     * Explique pourquoi un mode a été sélectionné
     */
    explainModeSelection(analysis, mode) {
        const reasons = [];
        
        reasons.push(`Longueur: ${analysis.lengthCategory} (${analysis.length} chars)`);
        reasons.push(`Complexité: ${(analysis.complexity * 100).toFixed(1)}%`);
        
        if (analysis.urgency > 0.3) {
            reasons.push(`Urgence détectée: ${(analysis.urgency * 100).toFixed(1)}%`);
        }
        
        const activePatterns = Object.keys(analysis.patterns)
            .filter(p => analysis.patterns[p]);
        if (activePatterns.length > 0) {
            reasons.push(`Patterns: ${activePatterns.join(', ')}`);
        }
        
        reasons.push(`Réponse prédite: ${analysis.expectedResponseLength} chars`);
        
        return reasons;
    }
    
    /**
     * Adapte le mode en temps réel basé sur les performances
     */
    adaptModeBasedOnPerformance(actualResponseTime, actualResponseLength) {
        const performance = {
            responseTime: actualResponseTime,
            responseLength: actualResponseLength,
            timestamp: Date.now(),
            mode: this.currentMode
        };
        
        this.performanceHistory.push(performance);
        
        // Limiter la taille de l'historique
        if (this.performanceHistory.length > this.maxHistorySize) {
            this.performanceHistory.shift();
        }
        
        // Analyser les tendances et ajuster si nécessaire
        const shouldAdjust = this.shouldAdjustMode(performance);
        if (shouldAdjust.adjust) {
            this.log(`🔄 Ajustement automatique: ${this.currentMode} → ${shouldAdjust.newMode}`);
            this.currentMode = shouldAdjust.newMode;
            this.stats.modeChanges++;
        }
        
        this.updateStats(performance);
    }
    
    /**
     * Détermine si le mode doit être ajusté
     */
    shouldAdjustMode(performance) {
        const recentPerformances = this.performanceHistory.slice(-5);
        const avgResponseTime = recentPerformances.reduce((sum, p) => sum + p.responseTime, 0) / recentPerformances.length;
        
        const currentModeConfig = this.config.modes[this.currentMode];
        
        // Si les réponses sont trop lentes, passer en mode plus rapide
        if (avgResponseTime > currentModeConfig.timeout * 1.5) {
            const modes = Object.keys(this.config.modes);
            const currentIndex = modes.indexOf(this.currentMode);
            if (currentIndex < modes.length - 1) {
                return {
                    adjust: true,
                    newMode: modes[currentIndex + 1],
                    reason: 'Performance trop lente'
                };
            }
        }
        
        // Si les réponses sont très rapides, on peut passer en mode plus économique
        if (avgResponseTime < currentModeConfig.timeout * 0.3) {
            const modes = Object.keys(this.config.modes);
            const currentIndex = modes.indexOf(this.currentMode);
            if (currentIndex > 0) {
                return {
                    adjust: true,
                    newMode: modes[currentIndex - 1],
                    reason: 'Performance excellente, optimisation possible'
                };
            }
        }
        
        return { adjust: false };
    }
    
    /**
     * Met à jour les statistiques
     */
    updateStats(performance) {
        this.stats.totalRequests++;
        this.stats.averageResponseTime = 
            (this.stats.averageResponseTime + performance.responseTime) / 2;
        this.stats.averageResponseLength = 
            (this.stats.averageResponseLength + performance.responseLength) / 2;
        
        this.stats.modeDistribution[performance.mode]++;
    }
    
    /**
     * Méthodes utilitaires
     */
    countTechnicalTerms(text) {
        const technicalTerms = ['api', 'algorithm', 'database', 'function', 'variable', 'class', 'method'];
        return technicalTerms.reduce((count, term) => 
            count + (text.toLowerCase().includes(term) ? 1 : 0), 0
        );
    }
    
    countNestedClauses(text) {
        return (text.match(/,/g) || []).length + (text.match(/;/g) || []).length;
    }
    
    countAbstractConcepts(text) {
        const abstractWords = ['concept', 'théorie', 'principe', 'idée', 'notion', 'philosophie'];
        return abstractWords.reduce((count, word) => 
            count + (text.toLowerCase().includes(word) ? 1 : 0), 0
        );
    }
    
    analyzeContextComplexity(context) {
        return Object.keys(context).length * 0.1;
    }
    
    /**
     * Obtient le mode actuel et sa configuration
     */
    getCurrentMode() {
        return {
            mode: this.currentMode,
            config: this.config.modes[this.currentMode],
            stats: this.stats
        };
    }
    
    /**
     * Force un mode spécifique
     */
    setMode(mode) {
        if (!this.config.modes[mode]) {
            throw new Error(`Mode TURBO inconnu: ${mode}`);
        }
        
        const oldMode = this.currentMode;
        this.currentMode = mode;
        this.stats.modeChanges++;
        
        this.log(`🔄 Mode changé manuellement: ${oldMode} → ${mode}`);
        this.emit('modeChanged', { from: oldMode, to: mode, manual: true });
    }
    
    /**
     * Logging
     */
    log(message) {
        console.log(`[AdaptiveTurbo] ${new Date().toISOString()} ${message}`);
    }
}

module.exports = AdaptiveTurboSystem;
