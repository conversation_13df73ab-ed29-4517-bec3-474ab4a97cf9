<!-- Interface de chat -->
<div class="card">
  <div class="card-header">
    <i class="bi bi-chat-dots card-icon"></i>
    <h2 class="card-title">Interface de Chat</h2>
  </div>
  
  <div class="chat-container" style="height: 400px; display: flex; flex-direction: column;">
    <div class="chat-messages" style="flex-grow: 1; overflow-y: auto; padding: 15px; background-color: rgba(0, 0, 0, 0.1); border-radius: 5px; margin-bottom: 15px;">
      <!-- Messages -->
      <div class="message user" style="margin-bottom: 15px; text-align: right;">
        <div style="display: inline-block; background-color: var(--header-bg); color: #333; padding: 10px 15px; border-radius: 15px 15px 0 15px; max-width: 80%; text-align: left;">
          <div style="font-weight: bold; margin-bottom: 5px;">Vous</div>
          <div><PERSON><PERSON><PERSON> <PERSON><PERSON>, comment vas-tu aujourd'hui ?</div>
          <div style="font-size: 10px; text-align: right; margin-top: 5px;">08:00:15</div>
        </div>
      </div>
      
      <div class="message assistant" style="margin-bottom: 15px;">
        <div style="display: inline-block; background-color: var(--card-bg); padding: 10px 15px; border-radius: 15px 15px 15px 0; max-width: 80%;">
          <div style="font-weight: bold; margin-bottom: 5px; color: var(--header-bg);">Louna</div>
          <div>Bonjour ! Je vais très bien, merci. Je suis prête à vous aider. Que puis-je faire pour vous aujourd'hui ?</div>
          <div style="font-size: 10px; text-align: right; margin-top: 5px; color: var(--text-secondary);">08:00:20</div>
        </div>
      </div>
      
      <div class="message user" style="margin-bottom: 15px; text-align: right;">
        <div style="display: inline-block; background-color: var(--header-bg); color: #333; padding: 10px 15px; border-radius: 15px 15px 0 15px; max-width: 80%; text-align: left;">
          <div style="font-weight: bold; margin-bottom: 5px;">Vous</div>
          <div>Peux-tu me parler de la mémoire thermique et comment elle fonctionne ?</div>
          <div style="font-size: 10px; text-align: right; margin-top: 5px;">08:00:45</div>
        </div>
      </div>
      
      <div class="message assistant" style="margin-bottom: 15px;">
        <div style="display: inline-block; background-color: var(--card-bg); padding: 10px 15px; border-radius: 15px 15px 15px 0; max-width: 80%;">
          <div style="font-weight: bold; margin-bottom: 5px; color: var(--header-bg);">Louna</div>
          <div>
            La mémoire thermique est un concept inspiré du fonctionnement du cerveau humain. Elle organise les informations en "zones" de différentes températures qui correspondent à différents niveaux de priorité et de rétention.
            <br><br>
            Les zones chaudes (comme la Zone 1 à 48°C) contiennent les informations récentes et importantes qui sont facilement accessibles, mais qui se "refroidissent" avec le temps si elles ne sont pas utilisées.
            <br><br>
            Les zones plus froides (comme les Zones 5 et 6 à 5°C et 3°C) contiennent des informations à long terme, qui sont moins immédiatement accessibles mais plus durables.
            <br><br>
            Les informations se déplacent entre ces zones en fonction de leur utilisation et de leur importance, créant un système de mémoire dynamique qui priorise naturellement les informations pertinentes.
          </div>
          <div style="font-size: 10px; text-align: right; margin-top: 5px; color: var(--text-secondary);">08:01:15</div>
        </div>
      </div>
    </div>
    
    <div class="chat-input" style="display: flex; gap: 10px;">
      <input type="text" placeholder="Tapez votre message ici..." style="flex-grow: 1; padding: 10px 15px; border-radius: 20px; border: none; background-color: rgba(255, 255, 255, 0.1); color: var(--text-color);">
      <button class="action-button" style="border-radius: 20px; padding: 10px 20px;">
        <i class="bi bi-send"></i> Envoyer
      </button>
    </div>
  </div>
</div>

<!-- Options de chat -->
<div class="grid-container" style="margin-top: 20px;">
  <div class="grid-item">
    <div class="card">
      <div class="card-header">
        <i class="bi bi-gear card-icon"></i>
        <h3 class="card-title">Options de Chat</h3>
      </div>
      
      <div style="padding: 15px;">
        <div class="option-item" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
          <span>Utiliser la mémoire thermique</span>
          <label class="switch" style="position: relative; display: inline-block; width: 50px; height: 24px;">
            <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: var(--accent-color); border-radius: 34px; transition: .4s;"></span>
          </label>
        </div>
        
        <div class="option-item" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
          <span>Activer les suggestions</span>
          <label class="switch" style="position: relative; display: inline-block; width: 50px; height: 24px;">
            <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: var(--accent-color); border-radius: 34px; transition: .4s;"></span>
          </label>
        </div>
        
        <div class="option-item" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
          <span>Mode conversation privée</span>
          <label class="switch" style="position: relative; display: inline-block; width: 50px; height: 24px;">
            <input type="checkbox" style="opacity: 0; width: 0; height: 0;">
            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; border-radius: 34px; transition: .4s;"></span>
          </label>
        </div>
      </div>
    </div>
  </div>
  
  <div class="grid-item">
    <div class="card">
      <div class="card-header">
        <i class="bi bi-clock-history card-icon"></i>
        <h3 class="card-title">Historique des Conversations</h3>
      </div>
      
      <div style="padding: 15px;">
        <div class="conversation-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.05); cursor: pointer;">
          <div style="font-weight: bold;">Conversation sur la mémoire thermique</div>
          <div style="font-size: 12px; color: var(--text-secondary);">Aujourd'hui, 08:00</div>
        </div>
        
        <div class="conversation-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.05); cursor: pointer;">
          <div style="font-weight: bold;">Discussion sur les accélérateurs</div>
          <div style="font-size: 12px; color: var(--text-secondary);">Hier, 15:30</div>
        </div>
        
        <div class="conversation-item" style="padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.05); cursor: pointer;">
          <div style="font-weight: bold;">Configuration du système</div>
          <div style="font-size: 12px; color: var(--text-secondary);">23/05/2025, 10:15</div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="grid-item">
    <div class="card">
      <div class="card-header">
        <i class="bi bi-lightning card-icon"></i>
        <h3 class="card-title">Suggestions Rapides</h3>
      </div>
      
      <div style="padding: 15px;">
        <div class="suggestion-item" style="padding: 8px 15px; border-radius: 20px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.1); display: inline-block; cursor: pointer;">
          Explique-moi les zones thermiques
        </div>
        
        <div class="suggestion-item" style="padding: 8px 15px; border-radius: 20px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.1); display: inline-block; cursor: pointer;">
          Comment fonctionnent les accélérateurs ?
        </div>
        
        <div class="suggestion-item" style="padding: 8px 15px; border-radius: 20px; margin-bottom: 10px; background-color: rgba(255, 255, 255, 0.1); display: inline-block; cursor: pointer;">
          Montre-moi les statistiques système
        </div>
      </div>
    </div>
  </div>
</div>
