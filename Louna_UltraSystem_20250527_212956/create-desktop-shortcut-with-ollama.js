/**
 * Script pour créer un raccourci sur le bureau pour l'application Louna avec Ollama
 * Ce script crée un fichier .command sur le bureau qui lance l'application
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

// Chemin vers le bureau
const desktopPath = path.join(os.homedir(), 'Desktop');

// Chemin vers le répertoire de l'application
const appPath = __dirname;

// Chemin vers le script de démarrage
const scriptPath = path.join(appPath, 'launch-louna-with-ollama.sh');

// Chemin vers le raccourci sur le bureau
const shortcutPath = path.join(desktopPath, 'Louna avec Ollama.command');

// Contenu du script de démarrage
const scriptContent = `#!/bin/bash

# Raccourci pour lancer l'application Louna avec Ollama
cd "${appPath.replace(/\\/g, '\\\\')}"
./launch-louna-with-ollama.sh
`;

// Fonction principale
async function main() {
  try {
    // Vérifier si le script de démarrage existe
    if (!fs.existsSync(scriptPath)) {
      console.error(`Le script de démarrage n'existe pas : ${scriptPath}`);
      process.exit(1);
    }
    
    // Vérifier si le bureau existe
    if (!fs.existsSync(desktopPath)) {
      console.error(`Le bureau n'existe pas : ${desktopPath}`);
      process.exit(1);
    }
    
    // Créer le raccourci
    fs.writeFileSync(shortcutPath, scriptContent, 'utf8');
    
    // Rendre le raccourci exécutable
    execSync(`chmod +x "${shortcutPath}"`);
    
    console.log(`Raccourci créé sur le bureau : ${shortcutPath}`);
  } catch (error) {
    console.error('Erreur lors de la création du raccourci :', error);
  }
}

// Exécuter la fonction principale
main();
