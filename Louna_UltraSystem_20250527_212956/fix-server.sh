#!/bin/bash

# Script pour réparer le fichier server.js
# Ce script corrige les problèmes dans le fichier server.js

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message stylisé
print_message() {
  echo -e "${PURPLE}[Louna]${NC} $1"
}

# Fonction pour afficher une erreur
print_error() {
  echo -e "${RED}[Erreur]${NC} $1"
}

# Fonction pour afficher un succès
print_success() {
  echo -e "${GREEN}[Succès]${NC} $1"
}

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Working/code/deepseek-node-ui"
SERVER_FILE="$APP_DIR/server.js"

# Afficher un message de démarrage
clear
echo -e "${PURPLE}"
echo "███████╗██╗██╗  ██╗    ███████╗███████╗██████╗ ██╗   ██╗███████╗██████╗ "
echo "██╔════╝██║╚██╗██╔╝    ██╔════╝██╔════╝██╔══██╗██║   ██║██╔════╝██╔══██╗"
echo "█████╗  ██║ ╚███╔╝     ███████╗█████╗  ██████╔╝██║   ██║█████╗  ██████╔╝"
echo "██╔══╝  ██║ ██╔██╗     ╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██╔══╝  ██╔══██╗"
echo "██║     ██║██╔╝ ██╗    ███████║███████╗██║  ██║ ╚████╔╝ ███████╗██║  ██║"
echo "╚═╝     ╚═╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${CYAN}Réparation du fichier server.js${NC}"
echo ""
print_message "Début de la réparation du fichier server.js..."
sleep 1

# Étape 1 : Sauvegarder le fichier original
print_message "Étape 1 : Sauvegarde du fichier original..."
cp "$SERVER_FILE" "$SERVER_FILE.bak"
print_success "Fichier original sauvegardé: $SERVER_FILE.bak"

# Étape 2 : Créer un nouveau fichier server.js
print_message "Étape 2 : Création d'un nouveau fichier server.js..."

cat > "$SERVER_FILE" << 'EOL'
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const morgan = require('morgan');
const ejs = require('ejs');
const expressLayouts = require('express-ejs-layouts');

// Initialiser l'application Express
const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Configuration de l'application
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(expressLayouts);
app.set('layout', 'layouts/layout');
app.use(express.static(path.join(__dirname, 'public')));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(session({
  secret: 'louna-secret-key',
  resave: false,
  saveUninitialized: true,
  cookie: { secure: false }
}));
app.use(morgan('dev'));

// Routes
const indexRouter = require('./routes/index');
const lunaRouter = require('./routes/luna');
const thermalMemoryRouter = require('./routes/thermal-memory');
const lounaDashboardRouter = require('./routes/louna-dashboard');

app.use('/', indexRouter);
app.use('/luna', lunaRouter);
app.use('/luna/thermal-memory', thermalMemoryRouter);
app.use('/louna', lounaDashboardRouter);

// Rediriger /luna vers /louna
app.get('/luna', (req, res) => {
  res.redirect('/louna');
});

app.get('/luna/:path', (req, res) => {
  res.redirect(`/louna/${req.params.path}`);
});

app.get('/luna/:path/:subpath', (req, res) => {
  res.redirect(`/louna/${req.params.path}/${req.params.subpath}`);
});

// Middleware de gestion des erreurs
app.use((req, res, next) => {
  res.status(404).render('error', {
    title: 'Erreur 404',
    message: 'Page non trouvée',
    error: { status: 404, stack: '' }
  });
});

app.use((err, req, res, next) => {
  res.status(err.status || 500).render('error', {
    title: `Erreur ${err.status || 500}`,
    message: err.message,
    error: process.env.NODE_ENV === 'development' ? err : {}
  });
});

// Démarrer le serveur
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Serveur démarré sur le port ${PORT}`);
});

// Exporter l'application
module.exports = app;
EOL

print_success "Nouveau fichier server.js créé."

# Étape 3 : Vérifier si le répertoire routes/louna-dashboard.js existe
print_message "Étape 3 : Vérification du répertoire routes/louna-dashboard.js..."

if [ ! -f "$APP_DIR/routes/louna-dashboard.js" ]; then
  print_message "Le fichier routes/louna-dashboard.js n'existe pas. Création du fichier..."
  
  mkdir -p "$APP_DIR/routes"
  
  cat > "$APP_DIR/routes/louna-dashboard.js" << 'EOL'
/**
 * Routes pour le tableau de bord système de Louna
 */

const express = require('express');
const router = express.Router();

// Route pour le tableau de bord système
router.get('/', (req, res) => {
  res.render('louna-dashboard', {
    title: 'Tableau de Bord Système',
    activePage: 'home',
    layout: 'layouts/louna-template'
  });
});

module.exports = router;
EOL

  print_success "Fichier routes/louna-dashboard.js créé."
else
  print_message "Le fichier routes/louna-dashboard.js existe déjà."
fi

# Étape 4 : Vérifier si le répertoire views/layouts existe
print_message "Étape 4 : Vérification du répertoire views/layouts..."

if [ ! -d "$APP_DIR/views/layouts" ]; then
  print_message "Le répertoire views/layouts n'existe pas. Création du répertoire..."
  mkdir -p "$APP_DIR/views/layouts"
  print_success "Répertoire views/layouts créé."
else
  print_message "Le répertoire views/layouts existe déjà."
fi

# Étape 5 : Vérifier si le fichier views/layouts/louna-template.ejs existe
print_message "Étape 5 : Vérification du fichier views/layouts/louna-template.ejs..."

if [ ! -f "$APP_DIR/views/layouts/louna-template.ejs" ]; then
  print_message "Le fichier views/layouts/louna-template.ejs n'existe pas. Création du fichier..."
  
  cat > "$APP_DIR/views/layouts/louna-template.ejs" << 'EOL'
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Louna - <%= title %></title>
  
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  
  <!-- Polices Google -->
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  
  <!-- CSS unifié de Louna -->
  <link rel="stylesheet" href="/css/louna-unified.css">
  
  <!-- CSS spécifique à la page (optionnel) -->
  <% if (typeof pageStyles !== 'undefined') { %>
    <link rel="stylesheet" href="<%= pageStyles %>">
  <% } %>
</head>
<body>
  <!-- Barre de navigation horizontale -->
  <nav class="top-navbar">
    <div class="logo-container">
      <i class="bi bi-braces-asterisk"></i>
      <span class="logo-text">Louna</span>
    </div>
    
    <div class="nav-links">
      <a href="/louna/chat" class="nav-item <%= activePage === 'chat' ? 'active' : '' %>">
        <i class="bi bi-chat-dots"></i>
        <span>Chat</span>
      </a>
      <a href="/louna/presentation" class="nav-item <%= activePage === 'presentation' ? 'active' : '' %>">
        <i class="bi bi-easel"></i>
        <span>Présentation</span>
      </a>
      <a href="/louna" class="nav-item <%= activePage === 'home' ? 'active' : '' %>">
        <i class="bi bi-house"></i>
        <span>Accueil</span>
      </a>
      <a href="/louna/prompts" class="nav-item <%= activePage === 'prompts' ? 'active' : '' %>">
        <i class="bi bi-lightning"></i>
        <span>Prompts</span>
      </a>
      <a href="/louna/memory" class="nav-item <%= activePage === 'memory' ? 'active' : '' %>">
        <i class="bi bi-brain"></i>
        <span>Mémoire</span>
      </a>
      <a href="/louna/thermal" class="nav-item <%= activePage === 'thermal' ? 'active' : '' %>">
        <i class="bi bi-thermometer-half"></i>
        <span>Thermique</span>
      </a>
    </div>
    
    <div class="nav-right">
      <a href="/louna/cerveau" class="nav-item">
        <i class="bi bi-cpu"></i>
        <span>Cerveau</span>
      </a>
      <a href="/louna/ia" class="nav-item">
        <i class="bi bi-robot"></i>
        <span>IA</span>
      </a>
      <a href="/louna/multimedia" class="nav-item">
        <i class="bi bi-film"></i>
        <span>Multimédia</span>
      </a>
      <a href="/louna/code" class="nav-item">
        <i class="bi bi-code-square"></i>
        <span>Code</span>
      </a>
      <a href="/louna/systeme" class="nav-item">
        <i class="bi bi-gear"></i>
        <span>Système</span>
      </a>
      <a href="/louna/securite" class="nav-item">
        <i class="bi bi-shield-lock"></i>
        <span>Sécurité</span>
      </a>
      <a href="/louna/parametres" class="nav-item">
        <i class="bi bi-sliders"></i>
        <span>Paramètres</span>
      </a>
    </div>
  </nav>
  
  <!-- Conteneur principal -->
  <div class="main-container">
    <!-- En-tête de l'interface -->
    <div class="interface-header">
      <div>
        <h1 class="interface-title">Interface Louna</h1>
        <p class="interface-subtitle">Système cognitif avancé avec mémoire thermique</p>
      </div>
      
      <div class="status-container">
        <span class="status-label">Statut</span>
        <div class="status-indicator"></div>
        <span class="status-text">Actif</span>
        <button class="action-button">Déconnecter</button>
      </div>
    </div>
    
    <!-- Contenu spécifique à la page -->
    <%- body %>
  </div>
  
  <!-- Scripts communs -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  
  <!-- Script pour la navigation uniforme -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Gérer les clics sur les liens de navigation
      document.querySelectorAll('.nav-item').forEach(function(link) {
        link.addEventListener('click', function(e) {
          // Stocker l'URL actuelle dans le stockage local
          localStorage.setItem('lastValidUrl', window.location.href);
          
          // Laisser la navigation se faire normalement
        });
      });
      
      // Gérer les erreurs de navigation
      window.addEventListener('error', function(event) {
        console.error('Erreur détectée:', event.error);
        
        // Vérifier si l'erreur est liée à la navigation
        if (event.error && (
            event.error.message.includes('navigation') || 
            event.error.message.includes('route') || 
            event.error.message.includes('undefined')
          )) {
          console.log('Erreur de navigation détectée, tentative de récupération...');
          
          // Récupérer la dernière URL valide
          const lastValidUrl = localStorage.getItem('lastValidUrl') || '/louna';
          
          // Rediriger vers la dernière URL valide
          window.location.href = lastValidUrl;
        }
      });
    });
  </script>
  
  <!-- Script spécifique à la page (optionnel) -->
  <% if (typeof pageScript !== 'undefined') { %>
    <script src="<%= pageScript %>"></script>
  <% } %>
</body>
</html>
EOL

  print_success "Fichier views/layouts/louna-template.ejs créé."
else
  print_message "Le fichier views/layouts/louna-template.ejs existe déjà."
fi

# Étape 6 : Vérifier si le fichier views/louna-dashboard.ejs existe
print_message "Étape 6 : Vérification du fichier views/louna-dashboard.ejs..."

if [ ! -f "$APP_DIR/views/louna-dashboard.ejs" ]; then
  print_message "Le fichier views/louna-dashboard.ejs n'existe pas. Création du fichier..."
  
  cat > "$APP_DIR/views/louna-dashboard.ejs" << 'EOL'
<!-- Tableau de bord système -->
<div class="card">
  <div class="card-header">
    <i class="bi bi-speedometer2 card-icon"></i>
    <h2 class="card-title">Tableau de Bord Système</h2>
  </div>
  
  <!-- Statistiques système -->
  <div class="grid-container" style="margin-bottom: 20px;">
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Utilisation RAM (physique/Conteneurs)</div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: 65%;"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Utilisation CPU (Conteneurs)</div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: 45%;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Statistiques détaillées -->
  <div class="grid-container">
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Temps de démarrage</div>
          <div class="stat-value">519ms</div>
          <div class="stat-badge">Initialisation rapide</div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Statut système</div>
          <div class="stat-value" style="font-size: 16px; color: #1dd1a1;">Opérationnel</div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Dernière mise à jour</div>
          <div class="stat-value">08:01:51</div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Types de mémoire surveillés -->
  <h3 style="margin: 20px 0 15px; font-size: 16px;">Types de mémoire surveillés</h3>
  
  <div class="grid-container">
    <div class="grid-item">
      <div class="memory-type">
        <i class="bi bi-cpu" style="color: #54a0ff; margin-right: 10px;"></i>
        <div>
          <div style="font-weight: bold; margin-bottom: 5px;">RAM physique</div>
          <div style="font-size: 14px; color: var(--text-secondary);">Mémoire de l'ordinateur hôte utilisée par l'application</div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="memory-type">
        <i class="bi bi-thermometer-half" style="color: #ff9f43; margin-right: 10px;"></i>
        <div>
          <div style="font-weight: bold; margin-bottom: 5px;">Mémoire thermique</div>
          <div style="font-size: 14px; color: var(--text-secondary);">Modèle de mémoire virtuelle conceptualisé pour l'IA</div>
        </div>
      </div>
    </div>
    
    <div class="grid-item">
      <div class="memory-type">
        <i class="bi bi-hdd" style="color: #1dd1a1; margin-right: 10px;"></i>
        <div>
          <div style="font-weight: bold; margin-bottom: 5px;">Cache système</div>
          <div style="font-size: 14px; color: var(--text-secondary);">Mémoire temporaire utilisée pour les opérations</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Grille de 3 cartes -->
<div class="grid-container">
  <!-- Carte Mémoire Thermique -->
  <div class="grid-item">
    <div class="card">
      <div class="card-header">
        <i class="bi bi-thermometer-half card-icon"></i>
        <h3 class="card-title">Mémoire Thermique</h3>
        <span class="card-badge">OPTIMALISÉE</span>
      </div>
      
      <div class="progress-bar">
        <div class="progress-fill" style="width: 75%;"></div>
      </div>
      
      <div style="margin: 15px 0;">
        <div style="font-size: 14px; margin-bottom: 5px;">Capacité totale:</div>
        <div style="font-weight: bold;">1250 unités réparties sur 6 zones</div>
        <div style="font-size: 14px; color: var(--text-secondary); margin-top: 5px;">Utilisation actuelle:</div>
        <div style="font-size: 14px; color: var(--text-secondary);">42% (525 unités)</div>
        <div style="font-size: 14px; margin-top: 10px;">Zones actives:</div>
        <div style="font-weight: bold;">6 zones configurées</div>
      </div>
    </div>
  </div>
  
  <!-- Carte Zones Thermiques -->
  <div class="grid-item">
    <div class="card">
      <div class="card-header">
        <i class="bi bi-layers card-icon"></i>
        <h3 class="card-title">Zones Thermiques</h3>
        <span class="card-badge">CONNECTÉE</span>
      </div>
      
      <div class="thermal-zones">
        <div class="thermal-zone">
          <span class="zone-name">Zone 1 (Instant)</span>
          <span class="zone-temp hot">48°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill hot" style="width: 90%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 2 (Court terme)</span>
          <span class="zone-temp warm">35°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill warm" style="width: 70%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 3 (Moyen)</span>
          <span class="zone-temp medium">20°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill medium" style="width: 50%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 4 (Moyen terme)</span>
          <span class="zone-temp cool">10°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill cool" style="width: 30%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 5 (Long terme)</span>
          <span class="zone-temp cool">5°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill cool" style="width: 20%;"></div>
        </div>
        
        <div class="thermal-zone">
          <span class="zone-name">Zone 6 (Rêve)</span>
          <span class="zone-temp cool">3°C</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill cool" style="width: 10%;"></div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Carte Accélérateurs -->
  <div class="grid-item">
    <div class="card">
      <div class="card-header">
        <i class="bi bi-lightning card-icon"></i>
        <h3 class="card-title">Accélérateurs</h3>
        <span class="card-badge">OPTIMALISÉS</span>
      </div>
      
      <div class="accelerators">
        <div class="accelerator-item">
          <span class="accelerator-name">Accélérateur Réflexif</span>
          <span class="accelerator-value">x3.1</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 80%; background-color: #54a0ff;"></div>
        </div>
        
        <div class="accelerator-item">
          <span class="accelerator-name">Accélérateur Thermique</span>
          <span class="accelerator-value">x2.7</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 70%; background-color: #54a0ff;"></div>
        </div>
        
        <div class="accelerator-item">
          <span class="accelerator-name">Connecteur Thermique</span>
          <span class="accelerator-value">x2.1</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 60%; background-color: #54a0ff;"></div>
        </div>
        
        <div style="margin-top: 20px;">
          <div style="font-size: 14px; margin-bottom: 5px;">Efficacité moyenne:</div>
          <div style="font-weight: bold; font-size: 24px;">92%</div>
          <div style="font-size: 12px; color: var(--text-secondary); margin-top: 5px;">Dernière mise à jour: 08:00 (MESZ)</div>
        </div>
      </div>
    </div>
  </div>
</div>
EOL

  print_success "Fichier views/louna-dashboard.ejs créé."
else
  print_message "Le fichier views/louna-dashboard.ejs existe déjà."
fi

# Étape 7 : Vérifier si le répertoire public/css existe
print_message "Étape 7 : Vérification du répertoire public/css..."

if [ ! -d "$APP_DIR/public/css" ]; then
  print_message "Le répertoire public/css n'existe pas. Création du répertoire..."
  mkdir -p "$APP_DIR/public/css"
  print_success "Répertoire public/css créé."
else
  print_message "Le répertoire public/css existe déjà."
fi

# Étape 8 : Vérifier si le fichier public/css/louna-unified.css existe
print_message "Étape 8 : Vérification du fichier public/css/louna-unified.css..."

if [ ! -f "$APP_DIR/public/css/louna-unified.css" ]; then
  print_message "Le fichier public/css/louna-unified.css n'existe pas. Copie du fichier..."
  cp "louna-unified.css" "$APP_DIR/public/css/louna-unified.css"
  print_success "Fichier public/css/louna-unified.css copié."
else
  print_message "Le fichier public/css/louna-unified.css existe déjà."
fi

# Étape 9 : Redémarrer le serveur
print_message "Étape 9 : Redémarrage du serveur..."

# Vérifier si le serveur est en cours d'exécution
if lsof -i:3000 -t &> /dev/null; then
  print_message "Arrêt du serveur en cours..."
  kill -9 $(lsof -i:3000 -t) 2>/dev/null
  sleep 2
  print_success "Serveur arrêté."
fi

print_message "Démarrage du serveur..."
cd "$APP_DIR" || exit

# Lancer le serveur en arrière-plan
nohup node server.js > /dev/null 2>&1 &

# Attendre que le serveur démarre
sleep 3

print_success "Serveur démarré."
print_message "L'application est accessible à l'adresse: http://localhost:3000/louna"

# Ouvrir le navigateur
print_message "Ouverture de l'application dans le navigateur..."
open "http://localhost:3000/louna"

print_success "Réparation du fichier server.js terminée !"
print_message "Le serveur a été redémarré et l'application est maintenant accessible."
