#!/bin/bash

# Script de lancement optimisé pour Louna
# Utilise le système d'optimisation intégré

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Chemin vers l'application
APP_DIR="/Volumes/seagate/Jarvis_Backup/Jarvis_Backup_20250520_191933"

echo -e "${PURPLE}"
echo "██╗      ██████╗ ██╗   ██╗███╗   ██╗ █████╗ "
echo "██║     ██╔═══██╗██║   ██║████╗  ██║██╔══██╗"
echo "██║     ██║   ██║██║   ██║██╔██╗ ██║███████║"
echo "██║     ██║   ██║██║   ██║██║╚██╗██║██╔══██║"
echo "███████╗╚██████╔╝╚██████╔╝██║ ╚████║██║  ██║"
echo "╚══════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝"
echo -e "${NC}"
echo -e "${YELLOW}🧠 Assistant IA Avancé avec Mémoire Thermique${NC}"
echo -e "${BLUE}🌍 Développé par Jean-Luc Passave, Sainte-Anne, Guadeloupe${NC}"
echo ""

# Vérifier si le répertoire existe
if [ ! -d "$APP_DIR" ]; then
  echo -e "${RED}❌ Répertoire de l'application non trouvé: $APP_DIR${NC}"
  exit 1
fi

# Aller dans le répertoire de l'application
cd "$APP_DIR" || exit 1

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
  echo -e "${RED}❌ Node.js n'est pas installé${NC}"
  exit 1
fi

echo -e "${BLUE}🚀 Lancement optimisé de Louna Electron...${NC}"
echo -e "${BLUE}🧹 Nettoyage des processus existants...${NC}"

# Arrêter les processus existants
pkill -f "node server.js" 2>/dev/null || true
pkill -f "electron" 2>/dev/null || true
sleep 1

# Lancer avec le script optimisé
echo -e "${GREEN}✅ Application Louna Electron lancée avec optimisations mémoire${NC}"
echo -e "${YELLOW}🌐 Interface disponible sur: http://localhost:3005${NC}"
echo -e "${YELLOW}📊 Monitoring QI & Neurones: http://localhost:3005/qi-neuron-monitor.html${NC}"

# Utiliser le script d'optimisation existant
node launch-optimized.js

echo -e "${PURPLE}📱 Application fermée avec le code: $?${NC}"
