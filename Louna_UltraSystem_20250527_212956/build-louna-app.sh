#!/bin/bash

# Script pour construire l'application Louna avec Electron

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "Node.js n'est pas installé. Veuillez installer Node.js pour exécuter cette application."
    exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo "npm n'est pas installé. Veuillez installer npm pour exécuter cette application."
    exit 1
fi

# Vérifier si les dépendances sont installées
if [ ! -d "node_modules" ]; then
    echo "Installation des dépendances..."
    npm install
fi

# Vérifier si electron-builder est installé
if ! npm list electron-builder &> /dev/null; then
    echo "electron-builder n'est pas installé. Installation en cours..."
    npm install --save-dev electron-builder
fi

# Vérifier si sharp est installé
if ! npm list sharp &> /dev/null; then
    echo "sharp n'est pas installé. Installation en cours..."
    npm install --save-dev sharp
fi

# Créer les icônes
echo "Création des icônes..."
node convert-icons.js

# Détecter le système d'exploitation
OS=$(uname -s)
case "$OS" in
    Darwin*)
        # macOS
        echo "Construction de l'application pour macOS..."
        npm run build-mac
        ;;
    Linux*)
        # Linux
        echo "Construction de l'application pour Linux..."
        npm run build-linux
        ;;
    MINGW*|MSYS*|CYGWIN*)
        # Windows
        echo "Construction de l'application pour Windows..."
        npm run build-win
        ;;
    *)
        # Système d'exploitation inconnu
        echo "Système d'exploitation non reconnu. Construction pour toutes les plateformes..."
        npm run build
        ;;
esac

echo "Construction terminée. Les fichiers se trouvent dans le dossier 'dist'."
