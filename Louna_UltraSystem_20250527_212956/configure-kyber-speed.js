#!/usr/bin/env node

/**
 * Configuration des accélérateurs Kyber pour optimiser la vitesse de réponse
 * Spécialement conçu pour améliorer les performances de chat de Louna
 */

const KyberAccelerators = require('./kyber-accelerators');
const fs = require('fs');
const path = require('path');

console.log('🚀 Configuration des accélérateurs Kyber pour la vitesse...\n');

// Configuration optimisée pour la vitesse
const speedConfig = {
    // Intervalles réduits pour des mises à jour plus fréquentes
    updateInterval: 10, // 10 secondes au lieu de 60
    
    // Facteurs de boost élevés pour la vitesse
    maxBoostFactor: 8.0, // Augmenté de 5.0 à 8.0
    minBoostFactor: 1.5, // Augmenté de 1.0 à 1.5
    defaultBoostFactor: 3.0, // Augmenté de 1.5 à 3.0
    
    // Seuils optimisés
    stabilityThreshold: 0.6, // Réduit pour permettre plus d'instabilité
    adaptationRate: 0.1, // Augmenté pour adaptation plus rapide
    energyConsumptionRate: 0.005, // Réduit pour économiser l'énergie
    energyRegenerationRate: 0.02, // Augmenté pour régénération plus rapide
    
    // Configuration spécifique pour chaque accélérateur
    reflexiveBoostFactor: 5.0, // Boost très élevé pour le traitement
    reflexiveStability: 0.95,
    reflexiveEnabled: true,
    
    thermalBoostFactor: 4.0, // Boost élevé pour la mémoire
    thermalStability: 0.90,
    thermalEnabled: true,
    
    connectorBoostFactor: 3.5, // Boost élevé pour les connexions
    connectorStability: 0.92,
    connectorEnabled: true
};

// Initialiser les accélérateurs avec la configuration de vitesse
console.log('🔧 Initialisation des accélérateurs Kyber...');
const kyberAccelerators = new KyberAccelerators(speedConfig);

// Attendre un peu pour que l'initialisation se termine
setTimeout(() => {
    console.log('⚡ Configuration des accélérateurs pour la vitesse maximale...');
    
    // Ajuster les facteurs de boost pour la vitesse
    kyberAccelerators.adjustBoostFactor('reflexive', 6.0);
    kyberAccelerators.adjustBoostFactor('thermal', 5.0);
    kyberAccelerators.adjustBoostFactor('connector', 4.5);
    
    // Vérifier les statistiques
    const stats = kyberAccelerators.getAcceleratorStats();
    console.log('\n📊 Statistiques des accélérateurs Kyber :');
    console.log(`• Boost Réflexif: ${stats.reflexiveBoost}x`);
    console.log(`• Boost Thermique: ${stats.thermalBoost}x`);
    console.log(`• Boost Connecteur: ${stats.connectorBoost}x`);
    console.log(`• Boost Moyen: ${stats.averageBoost}x`);
    console.log(`• Efficacité: ${stats.efficiency}`);
    
    // Créer un fichier de configuration pour l'intégration
    const integrationConfig = {
        kyberAccelerators: {
            enabled: true,
            speedMode: true,
            chatOptimization: true,
            responseAcceleration: true,
            memoryBoost: true,
            processingBoost: true
        },
        chatPerformance: {
            useKyberBoost: true,
            boostChatProcessing: true,
            boostMemoryRetrieval: true,
            boostResponseGeneration: true,
            targetResponseTime: 1000, // 1 seconde
            maxResponseTime: 3000 // 3 secondes max
        },
        voiceOptimization: {
            useKyberForSpeech: true,
            boostSynthesis: true,
            femaleVoiceOptimized: true,
            fastSpeechProcessing: true
        }
    };
    
    const configPath = path.join(__dirname, 'kyber-speed-config.json');
    fs.writeFileSync(configPath, JSON.stringify(integrationConfig, null, 2));
    console.log(`\n✅ Configuration sauvegardée dans: ${configPath}`);
    
    // Créer un script de test des performances avec Kyber
    const testScript = `#!/usr/bin/env node

/**
 * Test des performances avec accélérateurs Kyber
 */

const KyberAccelerators = require('./kyber-accelerators');
const axios = require('axios');

async function testKyberPerformance() {
    console.log('🚀 Test des performances avec accélérateurs Kyber...');
    
    // Initialiser les accélérateurs
    const kyber = new KyberAccelerators(${JSON.stringify(speedConfig, null, 8)});
    
    // Simuler l'application de boost
    const baseResponseTime = 5000; // 5 secondes de base
    const boostedTime = kyber.applyBoost('processing', baseResponseTime);
    
    console.log(\`⏱️ Temps de base: \${baseResponseTime}ms\`);
    console.log(\`⚡ Temps avec Kyber: \${Math.round(boostedTime)}ms\`);
    console.log(\`🚀 Amélioration: \${Math.round((baseResponseTime - boostedTime) / baseResponseTime * 100)}%\`);
    
    // Test de chat réel
    console.log('\\n💬 Test de chat avec Kyber...');
    const startTime = Date.now();
    
    try {
        const response = await axios.post('http://localhost:3007/api/chat/message', {
            message: 'Bonjour Louna, teste tes accélérateurs Kyber !',
            history: [],
            useKyberBoost: true
        }, {
            timeout: 10000
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        console.log(\`✅ Réponse Kyber reçue en \${responseTime}ms\`);
        console.log(\`📝 Réponse: \${response.data.response}\`);
        
        if (responseTime < 1500) {
            console.log('🎉 Performance EXCEPTIONNELLE avec Kyber !');
        } else if (responseTime < 3000) {
            console.log('⚡ Performance EXCELLENTE avec Kyber !');
        } else {
            console.log('👍 Performance BONNE avec Kyber');
        }
        
        // Afficher les stats Kyber
        const stats = kyber.getAcceleratorStats();
        console.log('\\n📊 Stats Kyber après utilisation:');
        console.log(\`• Boost moyen: \${stats.averageBoost}x\`);
        console.log(\`• Efficacité: \${stats.efficiency}\`);
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
    }
}

testKyberPerformance();
`;
    
    const testPath = path.join(__dirname, 'test-kyber-performance.js');
    fs.writeFileSync(testPath, testScript);
    fs.chmodSync(testPath, '755');
    console.log(`✅ Script de test créé: ${testPath}`);
    
    console.log('\n🎉 Configuration Kyber terminée !');
    console.log('\n📋 Accélérateurs configurés :');
    console.log('• Accélérateur Réflexif: 6.0x (traitement ultra-rapide)');
    console.log('• Accélérateur Thermique: 5.0x (mémoire optimisée)');
    console.log('• Connecteur Thermique: 4.5x (connexions rapides)');
    
    console.log('\n🚀 Pour tester les performances Kyber :');
    console.log('node test-kyber-performance.js');
    
    console.log('\n⚡ Les accélérateurs Kyber sont maintenant optimisés pour la vitesse maximale !');
    
    process.exit(0);
}, 2000);
